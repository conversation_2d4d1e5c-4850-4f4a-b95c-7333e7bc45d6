[TOC]

# nvm 版本管理

nvm 是 Node.js 版本管理工具，可以方便地管理多个 Node.js 版本，并切换使用不同的 Node.js 版本。

[教程链接](https://blog.csdn.net/muzidigbig/article/details/141108865)

## 安装

nvm-setup.exe 安装程序

安装到 c 盘不需要配置环境变量

## 使用

```bash
# 查看版本号
nvm -v
# 看安装的所有node.js的版本
nvm ls
# 查显示可以安装的所有node.js的版本
nvm list available
# 安装指定版本的node.js
nvm install 16.15.0
# 卸载对应node版本（如：nvm uninstall 17.2.0）
nvm uninstall 版本号
# 切换node版本
nvm use 16.15.0
# 创建一个别名以便更方便地引用特定的 Node.js 版本
nvm alias <name> <version>
# 卸载指定的 Node.js 版本
nvm uninstall <version>
# 显示当前正在使用的 Node.js 版本
nvm current
# 切换到默认的 Node.js 版本（由 nvm alias 命令设置的别名）
nvm use default
# 在指定版本的 Node.js 环境中执行特定的命令
nvm exec <version> <command>
# 查看当前使用的node版本
node -v
```

## 全局安装 pnpm

```bash
npm install -g @pnpm/exe
# 更新 pnpm
npm update -g @pnpm/exe
# 更新 npm
npm update -g npm
```

# nrm 镜像快捷切换

```bash
# 设置镜像源
npm config set registry https://registry.npmmirror.com

# 推荐使用 nrm 工具，方便切换镜像源
#  安装 nrm 工具
npm i -g nrm
# 查看所有可用的镜像源
nrm ls
# 使用淘宝镜像源
nrm use taobao

# 查看当前使用的镜像源
npm config list
```

# Node JS

## Buffer 类

在 Node.js 中，Buffer 是一个用于处理二进制数据的类，它是一种内存分配方式，可以让你用类似数组的方式操作字节数据。

你可以使用 `Buffer.alloc()` 方法来创建一个指定大小的 Buffer，并用 0 来初始化它。例如，`let buf = Buffer.alloc(10)` 创建了一个大小为 10 字节的 Buffer，并将所有字节初始化为 0。

`Buffer.allocUnsafe()` 方法用于创建指定大小的 Buffer，但不会用任何数据初始化它，这意味着 Buffer 的内容是未知的，可能包含敏感信息。因此，使用 `allocUnsafe()` 时需要格外小心。例如，`let buf_2 = Buffer.allocUnsafe(100)` 创建了一个大小为 100 字节的 Buffer，但其中的内容是未初始化的。

`Buffer.from()` 方法用于从各种数据类型（如字符串、数组或另一个 Buffer）创建一个新的 Buffer 实例。例如，`let buf_3 = Buffer.from("hello world")` 创建了一个 Buffer 实例，其中包含了字符串 "hello world" 的二进制数据。

这些 Buffer 的方法可以帮助你在 Node.js 中高效地处理二进制数据。

溢出时舍弃高位

```js
const buf = Buffer.from('hello')
buf[0] = 256 //溢出 0001 0000 0000 => 0000 0000
console.log(buf) // [ 255, 0 ]
```

## fs 文件系统模块

Node.js 提供了一个 fs 文件系统模块，用于操作文件系统。

### 文件写入

#### writeFile() 文件异步写入

语法:`fs.writeFile(file, data, [options], callback)`
参数说明:

- file: 文件路径
- data: 写入的数据
- options: 可选参数，用于指定文件的打开方式、文件权限等。
- callback: 回调函数，用于处理写入结果。

返回值:undefined

示例:

```js
// 导入fs模块
const fs = require('fs')
// 异步写入文件
// 没有文件会自动创建
fs.writeFile('./text.txt', 'Hello Node.js', (err) => {
  if (err) {
    console.log('文件写入失败！', err)
    return
  }
  console.log('文件写入成功！')
})

// 异步读取文件
fs.readFile('./text.txt', 'utf8', (err, data) => {
  if (err) {
    console.log('文件读取失败！', err)
    return
  }
  console.log('文件读取内容为:', data)
})
```

![1739796455268](images/Node.js/1739796455268.png)

#### writrFileSync() 文件同步写入

语法:`fs.writeFileSync(file, data, [options])`
参数说明:

- file: 文件路径
- data: 写入的数据
- options: 可选参数，用于指定文件的打开方式、文件权限等。

示例:

```js
// 导入fs模块
const fs = require('fs')
// 同步写入文件
// 没有文件会自动创建
fs.writeFileSync('./text.txt', 'Hello Node.js')
// 同步代码会阻塞后续代码的执行，直到文件写入完成
console.log('文件写入成功！')

// 异步读取文件
fs.readFile('./text.txt', 'utf8', (err, data) => {
  if (err) {
    console.log('文件读取失败！', err)
    return
  }
  console.log('文件读取内容为:', data)
})
```

![1739796455268](images/Node.js/1739796455268.png)

#### appendFile() 文件追加写入

语法:`fs.appendFile(file, data, [options], callback)`
参数说明:

- file: 文件路径
- data: 写入的数据
- options: 可选参数，用于指定文件的打开方式、文件权限等。
- callback: 回调函数，用于处理写入结果。

示例:

```js
// 导入fs模块
const fs = require('fs')
// 异步追加写入文件
// 没有文件会自动创建
fs.appendFile('./text.txt', 'Hello Node.js', (err) => {
  if (err) {
    console.log('文件追加写入失败！', err)
    return
  }
  console.log('文件追加写入成功！')
})

// 异步读取文件
fs.readFile('./text.txt', 'utf8', (err, data) => {
  if (err) {
    console.log('文件读取失败！', err)
    return
  }
  console.log('文件读取内容为:', data)
})
```

![1739796561734](images/Node.js/1739796561734.png)

#### appendFileSync() 文件追加同步写入

语法:`fs.appendFileSync(file, data, [options])`
参数说明:

- file: 文件路径
- data: 写入的数据
- options: 可选参数，用于指定文件的打开方式、文件权限等。

示例:

```js
// 导入fs模块
const fs = require('fs')
// 同步追加写入文件
// 没有文件会自动创建
fs.appendFileSync('./text.txt', 'Hello Node.js')
// 同步代码会阻塞后续代码的执行，直到文件写入完成
console.log('文件追加写入成功！')

// 异步读取文件
fs.readFile('./text.txt', 'utf8', (err, data) => {
  if (err) {
    console.log('文件读取失败！', err)
    return
  }
  console.log('文件读取内容为:', data)
})
```

![1739796641272](images/Node.js/1739796641272.png)

#### createWriteStream() 流式写入

**覆盖式写入，异步写入**

语法:`fs.createWriteStream(path, [options])`
参数说明:

1. path: 文件路径
2. options: 可选配置项，可以设置编码、起点（start）、文件模式（mode）等。

示例:

```js
// 导入fs模块
const fs = require('fs')
// 创建写入流对象
// 没有文件会自动创建
const writeStream = fs.createWriteStream('./text.txt')
// 写入数据
writeStream.write('hello, world!\n')
writeStream.write('你好，世界！\n')
// 关闭写入流
// 可有可无，但关闭后无法再写入数据
writeStream.close()
console.log('文件写入成功！')
```

### 文件读取

#### readFile() 文件异步读取

语法:`fs.readFile(path, [options], callback)`
参数说明:

1. path: 文件路径
2. options: 可选配置项，可以设置编码等。
3. callback: 回调函数，用于处理读取结果。

示例:

```js
// 导入fs模块
const fs = require('fs')

// 没有设置编码格式的话，默认是buffer格式，需要手动转换成字符串
fs.readFile('./text.txt', (err, data) => {
  if (err) {
    console.log('读取失败')
    return
  }
  // 转换成字符串
  console.log(data.toString())
})

// 设置编码格式为utf8
fs.readFile('./text.txt', 'utf8', (err, data) => {
  if (err) {
    console.log('读取失败')
    return
  }
  console.log(data)
})
```

![1739797912699](images/Node.js/1739797912699.png)

#### readFileSync() 文件同步读取

语法:`fs.readFileSync(path, [options])`
参数说明:

1. path: 文件路径
2. options: 可选配置项，可以设置编码等。

示例:

```js
// 导入fs模块
const fs = require('fs')

// 没有设置编码格式的话，默认是buffer格式，需要手动转换成字符串
let data = fs.readFileSync('./text.txt')
console.log(data.toString())

// 设置编码格式为utf8
data = fs.readFileSync('./text.txt', 'utf8')
console.log(data)
```

![1739797912699](images/Node.js/1739797912699.png)

#### createReadStream() 流式读取

**异步读取，流式读取**

语法:`fs.createReadStream(path, [options])`
参数说明:

1. path: 文件路径
2. options: 可选配置项，可以设置编码、起点（start）、文件模式（mode）等。

示例:

```js
// 导入fs模块
const fs = require('fs')

// 创建读取流对象
const readStream = fs.createReadStream('./text.txt', 'utf-8')

// 绑定data事件
readStream.on('data', (chunk) => {
  // 一块一块的读取数据，一块的数据上限为64KB
  console.log(chunk)
})

// 绑定end事件
readStream.on('end', () => {
  console.log('文件读取完毕')
})
```

### 文件复制

文件的复制就是读一个文件的内容，然后写到另一个文件中。

利用流式读取和流式写入

```js
// 导入fs模块
const fs = require('fs')

// 创建读取流对象
const readStream = fs.createReadStream('./text.txt', 'utf-8')

// 创建写入流对象
const writeStream = fs.createWriteStream('./new.txt', 'utf-8')

// 监听数据流事件
readStream.on('data', (chunk) => {
  // 写入数据
  writeStream.write(chunk)
})

// 监听结束事件
readStream.on('end', () => {
  // 关闭写入流
  writeStream.end()
})

// 管道流, 将读写流连接起来实现复制文件
readStream.pipe(writeStream)
```

### 文件移动与重命名

语法:`fs.rename(oldPath, newPath, callback)`
同步方法：`fs.renameSync(oldPath, newPath)`
参数说明:

1. oldPath: 旧文件路径
2. newPath: 新文件路径
3. callback: 回调函数，用于处理结果。

示例:

```js
// 导入fs模块
const fs = require('fs')

// 文件重命名
fs.rename('./text.txt', './new.txt', (err) => {
  if (err) {
    console.log('文件重命名操作失败')
    return
  }
  console.log('文件重命名操作成功')
})

// 文件移动
// 首先要有目标文件夹，如果目标文件夹不存在，移动操作失败，需要使用fs.mkdir()创建目标文件夹
fs.rename('./new.txt', './new/new.txt', (err) => {
  if (err) {
    console.log('文件移动操作失败')
    return
  }
  console.log('文件移动操作成功')
})
```

### 文件删除

**推荐使用 fs.rm**

语法:`fs.unlink(path, callback)`或`fs.rm(path, callback)`
同步方法：`fs.unlinkSync(path)`或`fs.rmSync(path)`
参数说明:

1. path: 文件路径
2. callback: 回调函数，用于处理结果。

示例:

```js
// 导入fs模块
const fs = require('fs')

fs.unlink('./text.txt', (err) => {
  if (err) {
    console.log('文件删除失败')
    return
  }
  console.log('文件删除成功！')
})

fs.rm('./text.txt', (err) => {
  if (err) {
    console.log('文件删除失败')
    return
  }
  console.log('文件删除成功！')
})
```

### 文件夹创建

语法:`fs.mkdir(path, [options], callback)`
同步方法：`fs.mkdirSync(path, [mode])`
参数说明:

1. path: 文件夹路径
2. options: 配置项:recursive: 是否递归创建目录。
3. callback: 回调函数，用于处理结果。

示例:

```js
// 导入fs模块
const fs = require('fs')

fs.mkdir('./新建文件夹', (err) => {
  if (err) {
    console.log('文件夹创建失败！')
    return
  }
  console.log('文件夹创建成功！')
})

// 递归创建目录
fs.mkdir('./新建文件夹/新建文件夹/子文件夹', { recursive: true }, (err) => {
  if (err) {
    console.log('文件夹创建失败！')
    return
  }
  console.log('文件夹创建成功！')
})
```

### 读取文件夹

语法:`fs.readdir(path, callback)`
同步方法：`fs.readdirSync(path)`
参数说明:

1. path: 文件夹路径
2. callback: 回调函数，用于处理结果。

示例:

```js
// 导入fs模块
const fs = require('fs')

fs.readdir('./', (err, files) => {
  if (err) {
    console.log('读取文件夹失败！')
    return
  }
  console.log(files)
})
```

### 删除文件夹

语法:`fs.rmdir(path, [options],callback)`
同步方法：`fs.rmdirSync(path, [options])`
参数说明:

1. path: 文件夹路径
2. options: 配置项:recursive: 是否递归删除目录。
3. callback: 回调函数，用于处理结果。

示例:

```js
// 导入fs模块
const fs = require('fs')

// 加入recursive选项，可将非空文件夹删除，否则只能删除空文件夹
fs.rmdir('./新建文件夹',, { recursive: true }, (err) => {
  if (err) {
    console.log('文件夹删除失败！')
    return
  }
  console.log('文件夹删除成功！')
})

// 使用rm直接删除空文件夹或非空文件夹
// 导入fs模块
const fs = require('fs')

// 配置项必须开启才能删除文件夹
fs.rm('./ad', { recursive: true }, (err) => {
  if (err) {
    console.log('文件夹删除失败！')
    return
  }
  console.log('文件夹删除成功！')
})
```

### 查看资源状态

语法:`fs.stat(path, callback)`
同步方法：`fs.statSync(path)`
参数说明:

1. path: 文件或文件夹路径
2. callback: 回调函数，用于处理结果。

示例:

```js
// 导入fs模块
const fs = require('fs')

fs.stat('./text.txt', (err, stats) => {
  if (err) {
    console.log('获取文件状态失败！')
    return
  }
  console.log(stats)
  // 查看是否是文件
  console.log(stats.isFile())
  // 查看是否是文件夹
  console.log(stats.isDirectory())
  // 查看文件大小
  console.log(stats.size)
})
```

### 其他

1. 文件写入的换行符 `\n` 和 `\r\n`
2. fs 的相对路径是相对于命令行所在的目录，而不是当前 js 文件所在的目录。可以使用 `__dirname` 来获取当前 js 文件所在的目录拼接为一个绝对路径。

```js
// 导入fs模块
const fs = require('fs')

// 使用绝对路径
fs.writeFile(__dirname + '/text.txt', 'Hello Node.js', (err) => {
  if (err) {
    console.log('文件写入失败！', err)
    return
  }
  console.log('文件写入成功！')
})

console.log('当前目录绝对路径:' + __dirname) // 输出：当前目录绝对路径:d:\AAA知识点
console.log('当前文件绝对路径:' + __filename) // 输出：当前文件绝对路径:d:\AAA知识点\Node.js.md
```

## Path 模块

### path.resolve()

用于拼接规范的绝对路径(斜杠规范)

```js
const path = require('path')
console.log(path.resolve(__dirname, 'test.txt'))
// Output: d:\临时文件\node_dome\test.txt
```

### path.sep

用于获取当前操作系统的路径分隔符

```js
const path = require('path')

console.log(path.sep)
// Output: \
```

### path.parse()

用于将路径拆分为对象

```js
const path = require('path')

console.log(path.parse(__filename))
// { root: 'd:\\',
//   dir: 'd:\\临时文件\\node_dome',
//   base: 'path.js',
//   ext: '.js',
//   name: 'path' }
```

### 文件信息

```js
const path = require('path')

// 获取文件名
console.log(path.basename(__filename))
// 获取目录名
console.log(path.dirname(__filename))
// 获取扩展名
console.log(path.extname(__filename))
```

## Http 模块

### http.createServer()

创建一个 HTTP 服务器，并返回一个服务器对象。

```js
// 导入http模块
const http = require('http')
// 导入url模块
// const url = require('url')

// 创建http服务对象
const sever = http.createServer((request, response) => {
  // 获取请求的方法
  console.log(request.method)
  // 获取请求的url
  console.log(request.url)
  // 获取 HTTP 协议的版本号
  console.log(request.httpVersion)
  // 获取 HTTP 的请求头
  console.log(request.headers)

  // 设置响应头 Content-Type
  response.setHeader('Content-Type', 'text/html;charset=utf-8')

  // 获取请求的url参数
  // 解析url参数,配置项设置为true，将搜索串转为对象
  // let res = url.parse(request.url, true)
  // console.log(res.pathname, res.query)

  // 获取请求的url参数
  let url = new URL(request.url, 'http://127.0.0.1:3000')
  console.log(url.searchParams.get('name'))
  console.log(url.pathname)

  response.end('love') //设置响应体并结束响应
})

// 监听端口，启动服务
sever.listen(3000, () => {
  console.log('服务启动')
})
```

```js
const http = require('http')

const server = http.createServer((request, response) => {
  // 设置响应状态码
  response.statusCode = 200
  // 设置响应描述
  response.statusMessage = 'I Love You!'
  // 设置响应头
  response.setHeader('content-type', 'text/html;charset=utf-8')
  response.setHeader('sever', 'Node.js')
  response.setHeader('MyHeader', 'love love love')
  // 设置多个同名响应头
  response.setHeader('test', ['1', '2', '3'])
  // 设置响应体
  response.write('Hello')
  response.write(' ')
  response.write('World!')
  // 结束响应，通常情况下使用write之后end方法内就不传入内容了
  response.end('\n我喜欢你！\n')
})

server.listen(3000, () => {
  console.log('Server running at http://localhost:3000/')
})
```

## 单文件服务器练习

目录结构：

```
.
├── sever.js
├── pages
    ├── css
    │   └── index.css
    ├── images
    │   └── logo.png
    ├── js
    │   └── index.js
    ├── favicon.ico
    └── index.html
```

### sever.js

```js
// server.js
const http = require('http')
const fs = require('fs')
const path = require('path')

// 设置 MIME 类型
let mimes = {
  '.html': 'text/html;charset=utf-8',
  '.js': 'text/javascript;charset=utf-8',
  '.css': 'text/css;charset=utf-8',
  '.json': 'application/json;charset=utf-8',
  '.png': 'image/png',
  '.jpg': 'image/jpeg',
  '.gif': 'image/gif',
  '.svg': 'image/svg+xml',
  '.ttf': 'application/x-font-ttf',
  '.woff': 'application/font-woff',
  '.woff2': 'application/font-woff2',
  '.eot': 'application/vnd.ms-fontobject',
  '.otf': 'application/font-sfnt',
  '.ico': 'image/x-icon',
}

const sever = http.createServer((request, response) => {
  // 如果不是 GET 请求，返回 405
  if (request.method !== 'GET') {
    response.statusCode = 405
    response.end('<h1>405 Method Not Allowed</h1>')
    return
  }

  // 获取请求路径
  const { pathname } = new URL(request.url, 'http://127.0.0.1')

  // 网站根目录
  let basePath = __dirname + '/pages'

  if (pathname === '/') {
    response.statusCode = 200
    // 网站首页
    response.end(fs.readFileSync(basePath + '/index.html'))
  } else {
    // 其他页面
    let filePath = basePath + pathname
    console.log('请求的文件路径:', filePath)

    fs.readFile(filePath, (err, data) => {
      if (err) {
        // 设置字符集
        response.setHeader('content-type', 'text/html;charset=utf-8')

        // 错误处理
        switch (err.code) {
          case 'ENOENT':
            response.statusCode = 404
            response.end('<h1>404 Not Found</h1>')
            break
          case 'EPERM':
            response.statusCode = 403
            response.end('<h1>403 Forbidden</h1>')
            break
          default:
            response.statusCode = 500
            response.end('<h1>500 Internal Server Error</h1>')
            break
        }

        return
      }

      // 获取文件后缀名
      const extname = path.extname(filePath)
      // 根据文件后缀名设置响应头
      let type = mimes[extname]
      if (type) {
        // 匹配到 MIME 类型
        response.setHeader('content-type', type)
      } else {
        // 未匹配到 MIME 类型，默认 application/octet-stream
        response.setHeader('content-type', 'application/octet-stream')
      }

      response.statusCode = 200
      response.end(data)
    })
  }
})

sever.listen(3000, () => {
  console.log('Server running at http://localhost:3000')
})
```

### pages/index.html

```html
<!-- pages/index.html -->
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0" />
    <title>Document</title>
    <link
      rel="stylesheet"
      href="./css/index.css" />
  </head>
  <body>
    <table></table>
    <img
      src="./images/logo.jpg"
      style="width: 49%; display: block; margin: 0 auto"
      alt="" />
    <script src="./js/index.js"></script>
  </body>
</html>
```

### pages/css/index.css

```css
/* pages/css/index.css */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

table {
  border-collapse: collapse;
  width: 100%;
}
tr:nth-child(2n) {
  background-color: pink;
}
tr:nth-child(2n-1) {
  background-color: rgb(255, 192, 253);
}
td {
  border: 1px solid black;
  padding: 10px;
  text-align: center;
}
```

### pages/js/index.js

```js
// pages/js/index.js
const table = document.querySelector('table')
let html = ''
for (let i = 0; i < 5; i++) {
  html += '<tr>'
  for (let j = 0; j < 5; j++) {
    html += `<td>${(i * j * 3 + 4) * 10}我爱你</td>`
  }
  html += '</tr>'
}
table.innerHTML = html
const tds = document.querySelectorAll('td')
tds.forEach((item) => {
  item.addEventListener('click', () => {
    item.style.backgroundColor = '#00ccff'
    item.style.color = 'white'
  })
})
```

## 模块化开发

### 引入模块

```js
// 使用 require 引入模块
const http = require('http')
/**
 *
 * 传入路径引入自定义模块
 * 1. 自定义模块建议书写相对路径，不可省略./或../
 * 2. js和json文件导入时可以省略后缀，c/c++书写的node拓展文件也可省略后缀
 * 3. 如果导入其他类型文件，会以js文件进行处理
 * 4. 如果导入的路径是一个文件夹，则会首先检测该文件下的package.json文件中的main属性对应文件，如果不存在，则会检测文件夹下的index.js和index.json文件，如果还找不到就会报错
 * 5. 导入node.js内置模块时，直接require('模块名')即可，不需要指定路径
 * 6. require 语法 引入的是目标模块中的module.exports对象
 *
 */

const myModule = require('./myModule')
```

### 暴露模块

```js
/* function sayHello() {
  console.log('Hello, world!')
}

function sayGoodbye() {
  console.log('Goodbye, world!')
}

module.exports = { sayHello, sayGoodbye } */

exports.sayHello = () => {
  console.log('Hello, world!')
}
exports.sayGoodbye = () => {
  console.log('Goodbye, world!')
}

// require 语法 引入目标模块中的module.exports对象
// exports = module.exports = { sayHello, sayGoodbye }
// module.exports = 'Hello, world!'  //! 可以!
// exports = 123  //! 错误的写法! 不可以!
```

## 包管理工具

### 包的创建

```bash
# 问答式创建
npm init
# 快速创建 (全部默认值)
npm init -y
# 问答式创建 package.json 文件, 括号中是默认值
package name : 包的名称 (不能有中文，不能有大写，默认值为文件夹名称)
version: 包的版本号
description: 包的描述
entry point: 包的入口文件
test command: 运行测试命令
git repository: 包的 git 仓库地址
keywords: 包的关键字
author: 包的作者
license: 开源证书
```

### 搜索包

```bash
# 搜索包
# 命令行搜索
npm s/search 关键字
# 网页搜索
https://www.npmjs.com/
```

### 安装包

```bash
# 安装包
npm i/install 包名

# 安装包到指定目录
npm i/install 包名 --prefix 目录路径

# 安装包到全局
npm i/install -g 包名
npm root -g  # 查看全局安装路径

# 安装包的指定版本
npm i/install 包名@版本号

# 安装包的最新版本
npm i/install 包名@latest

# 安装包的最新稳定版本
# 注意：npm并没有直接支持`@latest-stable`这种语法，通常`@latest`就是最新的稳定版本。
npm i/install 包名@latest

# 安装包的依赖包并保存到package.json的dependencies中
npm i/install --save 包名

# 安装包的devDependencies并保存到package.json的devDependencies中
npm i/install --save-dev 包名

# 安装包的最新版本，并保存到package.json文件中，使用--save-exact可以确保安装的是精确版本号
npm i/install 包名@latest --save-exact

```

### 更新包

```bash
# 更新包
npm update 包名
# 更新包的指定版本
npm update 包名@版本号
# 更新包的最新版本
npm update 包名@latest
# 更新包的最新稳定版本
npm update 包名@latest-stable
# 更新全局包
npm update -g 包名
# 更新所有包
npm update
```

### 卸载包

```bash
# 卸载包
npm uninstall 包名
# 卸载全局包
npm uninstall -g 包名
# 卸载所有包
npm uninstall -g
```

### 发布包

```bash
# 修改为官方镜像源
nrm use npm
# 登录 npm 账号
npm login
# 发布包
npm publish
# 发布包到指定注册表
npm publish --registry=https://registry.npmjs.org/
# 更新包
# 先修改版本号，再运行 npm publish 命令
npm publish
# 删除包
# 需要满足条件
# 1. 你是包的作者
# 2. 没有被其他包依赖
# 3. 每周下载量小于300
# 4. 只有一个维护者
```

### 开发环境和生产环境

开发环境是程序员专门用来写代码的环境，一般是指程序员的电脑，开发环境的项目一般只能程序员自己访问

生产环境是项目代码正式运行的环境，一般是指正式的服务器，生产环境的项目一般每个客户都可以访问

### 开发依赖和生产依赖

#### 生产依赖

生产依赖是指项目运行所依赖的包，这些包一般是项目运行所必需的，如数据库驱动程序、模板引擎、日志库等。

```bash
# 安装生产依赖
npm i/install -S 包名
npm i/install --save 包名
# 包信息保存在 package.json的 dependencies 中
```

#### 开发依赖

开发依赖是指项目开发过程中所需要的包，这些包一般是项目开发过程中所需要的，如测试库、代码检查工具、构建工具等。

```bash
# 安装开发依赖
npm i/install -D 包名
npm i/install --save-dev 包名
# 包信息保存在 package.json的 devDependencies 中
```

### 配置命令别名

```json
{
  "name": "npm",
  "version": "1.0.0",
  "main": "index.js",
  "scripts": {
    "test": "echo \"Error: no test specified\" && exit 1",
    // 自定义命令别名
    // 当我们输入 npm run dev 时，实际上是执行了 nodemon index.js 命令
    // dev 命令和 start 命令可以省略 run
    "dev": "nodemon index.js",
    "start": "node index.js"
  },
  "keywords": [],
  "author": "",
  "license": "ISC",
  "description": ""
}
```

## express 框架

### 安装 express

```bash
npm i express
```

### 基本使用

```js
// 导入express模块
const express = require('express')
// 创建一个express实例
const app = express()
// 创建 get 路由
app.get('/', (request, response) => {
  response.end('Hello World!')
})
// 创建 post 路由
app.post('/login', (request, response) => {
  response.end('Login successfully')
})
// 匹配所有方法
app.all('/api', (request, response) => {
  response.end('all method')
})
// 404 错误处理
app.use((req, res, next) => {
  res.status(404).send("Sorry can't find that!")
})
// 监听端口,启动服务
app.listen(3000, () => {
  console.log('服务已经启动,端口3000监听中...')
  console.log('请访问 http://localhost:3000')
})
```

### express 路由

使用格式：

```js
app.METHOD(PATH, HANDLER)
// 例如：
app.get('/home', (request, response) => {
  response.end('Welcome to home page')
})
```

### express 路由参数

```js
// 导入express模块
const express = require('express')
// 创建一个express实例
const app = express()
// 导入admin数据
const adminData = require('./data/admin')
// 创建 get 路由
app.all('/', (req, res) => {
  console.log('请求方法为', req.method)
  console.log('请求路径为', req.url)
  console.log('请求头为', req.headers)
  console.log('搜索参数为', req.query)

  res.end('Hello World!')
})

app.get('/admin/:id', (req, res) => {
  console.log('路由参数为', req.params)
  let data = adminData.find((item) => item.id === req.params.id * 1)
  res.setHeader('Content-Type', 'application/json;charset=utf-8')
  res.end(JSON.stringify(data))
})

// 监听端口,启动服务
app.listen(3000, () => {
  console.log('服务已经启动,端口3000监听中...')
})
```

### express 响应设置

```js
// 导入express模块
const express = require('express')
// 创建一个express实例
const app = express()
// 导入admin.json数据
const adminData = require('./data/admin.json')
// 创建 get 路由
app.all('/', (req, res) => {
  /**
   * 原生响应设置
   * res.statusCode = 200
   * res.statusMessage = 'OK'
   * res.setHeader('Content-Type', 'text/html;charset=utf-8')
   * res.write('<h1>Hello World!</h1>')
   * res.end('Hello World!')
   */

  // 使用express响应设置
  res.status(200)
  // set 这个 api 会自动设置 'Content-Type', 'text/html;charset=utf-8'
  res.set()
  res.send('<h1>Hello World!</h1>')
  // 可以连着使用，如:
  // res.status(500).set().send('服务器内部错误')

  // 其他响应设置
  res.redirect('/login') // 重定向
  res.download(__dirname + '/data/admin.json', '下载文件') // 下载文件，第二个参数是下载后的默认文件名
  res.json(adminData) // 返回json数据
  res.sendFile(__dirname + '/data/admin.json') // 响应文件内容
})

// 监听端口,启动服务
app.listen(3000, () => {
  console.log('服务已经启动,端口3000监听中...')
  console.log('http://localhost:3000/')
})
```

### express 中间件

中间件本质上是一个回调函数，中间件函数可以像路由回调一样访问请求对象，响应对象，中间件的作用就是使用函数封装公共操作，简化代码，提高开发效率

中间件分为全局中间件和路由中间件，每一个请求到达服务器端后都会执行全局中间件函数

#### 全局中间件

```js
const express = require('express')
const fs = require('fs')
const path = require('path')
const app = express()

// 声明中间件函数
function recordMiddleware(req, res, next) {
  // 记录请求日志
  let { url, ip } = req
  // 保存日志到文件
  fs.appendFileSync(path.resolve(__dirname, './log/log.text'), `请求IP：${ip}，请求地址：${url} \n`)
  // 调用next函数，继续执行后续中间件
  next()
}

// 全局注册中间件
app.use(recordMiddleware)

app.get('/', (req, res) => {
  res.send('Hello World!')
})

app.get('/home', (req, res) => {
  res.send('Welcome to my home page!')
})

app.use((req, res, next) => {
  res.status(404).send("Sorry can't find that!")
})

app.listen(3000, () => {
  console.log('http://localhost:3000')
})
```

#### 路由中间件

```js
const express = require('express')
const app = express()

// 声明中间件
let checkCodeMiddleware = (req, res, next) => {
  // 校验code
  const code = req.query.code
  if (code === '520') {
    next()
  } else {
    res.send('code is error')
  }
}

// 路由中间件是将中间件传入想要受约束的路由的配置项里
app.get('/', checkCodeMiddleware, (req, res) => {
  res.send('hello world')
})

app.get('/admin', (req, res) => {
  res.send('admin')
})

app.use((req, res, next) => {
  res.status(404).send("Sorry can't find that!")
})

app.listen(3000, () => {
  console.log('server is running at http://localhost:3000')
})
```

#### 静态资源中间件

```js
const express = require('express')
const app = express()

// 静态资源中间件设置 public 目录为静态资源目录
// 然后就可以访问静态资源了
// 如果有对应路径的静态文件，也有对应的请求路由，则谁在前面，谁就先响应
app.use(express.static(__dirname + '/public'))

res.send('hello world')
app.get('/', (req, res) => {})
app.use((req, res, next) => {
  res.status(404).send("Sorry can't find that!")
})

app.listen(3000, () => {
  console.log('server is running at http://localhost:3000')
})
```

![1740215785830](images/Node.js/1740215785830.png)

![1740215145379](images/Node.js/1740215145379.png)

![1740215860957](images/Node.js/1740215860957.png)

### 获取请求体数据

```js
const express = require('express')
const app = express()

app.get('/', (req, res) => {
  res.send('Hello World!')
})

// 需要先下载 body-parser 包
// body-parser 中间件使用
const bodyParser = require('body-parser')
// app.use(bodyParser.urlencoded({ extended: false })) //全局使用
// 推荐使用路由中间件，因为不是所有的请求都需要处理请求体

// 解析 application/json 格式请求体 即 JSON数据
const jsonParser = bodyParser.json()
// 解析 application/x-www-form-urlencoded 格式请求体 即 Form 表单数据(键值对)
const urlencodedParser = bodyParser.urlencoded({ extended: false })

app.post('/', urlencodedParser, (req, res) => {
  const { name, like } = req.body
  if (name === '白玉兰' && like === '张超') {
    res.send(`是的!${name}喜欢${like}`)
  } else {
    res.status(401).send('你没有传递neme和like参数')
  }
})

app.listen(3000, () => {
  console.log('http://localhost:3000/')
})
```

### 处理文件上传

```bash
# 安装 formidable 包
pnpm install formidable
```

```js
const express = require('express')
const formidable = require('formidable')
const app = express()

const baseUrl = 'http://localhost:3000'

// 上传文件接口
app.post('/upload', (req, res, next) => {
  // 创建 formidable 对象
  const form = formidable({
    multiples: true,
    uploadDir: __dirname + '/public/uploads', // 上传文件保存路径
    // 保留后缀名
    keepExtensions: true, // 保留后缀名
  })
  // 解析请求报文
  form.parse(req, (err, fields, files) => {
    if (err) {
      next(err)
      return
    }

    // 拼接并返回用户需要访问文件时的 URL
    // img 字段对应上传文件的名称,即发起上传请求时的键名
    let url = '/uploads/' + files.img.newFilename

    res.send(baseUrl + url)
  })
})

// 静态资源目录
app.use(express.static(__dirname + '/public'))

// 404错误处理
app.use((req, res, next) => {
  res.status(404).send("Sorry can't find that!")
})

app.listen(3000, () => {
  console.log('http://localhost:3000/')
})
```

![1740325255082](images/Node.js/1740325255082.png)

#### formidable 上传选项说明

##### 选项说明

- **options.encoding {string}**: 设置编码方式，默认为 `'utf-8'`。
- **options.uploadDir {string}**: 设置文件上传目录，默认为 `os.tmpdir()`。上传后可以使用 `fs.rename()` 移动文件。
- **options.keepExtensions {boolean}**: 是否保留原始文件扩展名，默认为 `false`。
- **options.allowEmptyFiles {boolean}**: 是否允许上传空文件，默认为 `false`。
- **options.minFileSize {number}**: 设置上传文件的最小大小，默认为 `1` 字节。
- **options.maxFiles {number}**: 设置上传文件的最大数量，默认为 `Infinity`（无限制）。
- **options.maxFileSize {number}**: 设置单个上传文件的最大大小，默认为 `200 * 1024 * 1024` 字节（200MB）。
- **options.maxTotalFileSize {number}**: 设置上传文件批次的总大小，默认为 `options.maxFileSize`。
- **options.maxFields {number}**: 设置字段的最大数量，默认为 `1000`。
- **options.maxFieldsSize {number}**: 设置所有字段（不包括文件）可以分配的最大内存量，默认为 `20 * 1024 * 1024` 字节（20MB）。
- **options.hashAlgorithm {string | false}**: 指定计算上传文件校验和的哈希算法，默认为 `false`。参阅 `crypto.createHash` 获取可用算法。
- **options.fileWriteStreamHandler {function}**: 自定义上传文件的写入行为，默认为 `null`。必须返回一个 `Writable` 流的实例。
- **options.filename {function}**: 控制上传文件的新文件名，默认为 `undefined`。函数必须返回一个字符串，将与 `options.uploadDir` 连接。
- **options.filter {function}**: 在上传文件前筛选文件，默认为始终返回 `true` 的函数。必须返回布尔值，不会导致 `form.parse` 错误。
- **options.createDirsFromUploads {boolean}**: 是否允许上传文件夹，默认为 `false`。需要与 `options.uploadDir` 和 `options.filename` 一起使用。

##### 函数详细说明

- **options.filename {function}**: `(name, ext, part, form) -> 字符串`
  - `part` 可以分解为 `originalFilename` 和 `mimetype`。
- **options.filter {function}**: `({name, originalFilename, mimetype}) -> boolean`
  - 类似于 `Array.filter`，返回 `false` 将忽略该文件并处理下一个文件。

##### 示例代码

```javascript
// 控制上传文件的新文件名
const options = {
  filename: (name, ext, part, form) => {
    const { originalFilename, mimetype } = part
    // 自定义逻辑生成新文件名
    return `new-${originalFilename}`
  },
}

// 筛选上传文件
const options = {
  filter: ({ name, originalFilename, mimetype }) => {
    // 仅允许上传图片文件
    return mimetype && mimetype.includes('image')
  },
}

// 取消所有上传
let cancelUploads = false // 在与 form 相同的作用域内创建变量
const options = {
  filter: ({ name, originalFilename, mimetype }) => {
    // 仅允许上传图片文件
    const valid = mimetype && mimetype.includes('image')
    if (!valid) {
      form.emit('error', new formidableErrors.default('invalid type', 0, 400)) // 可选：使 form.parse 错误
      cancelUploads = true // 变量用于在第一次问题后使筛选器返回 false
    }
    return valid && !cancelUploads
  },
}

// 监听上传进度
const form = new formidable.IncomingForm(options)
form.parse(req, (err, fields, files) => {
  console.log(`Received bytes: ${form.bytesReceived}`)
  console.log(`Expected bytes: ${form.bytesExpected}`)
})
```

##### 注意事项

- 如果超过组合字段的大小或某个文件的大小，会触发 `'error'` 事件。
- 使用 `form.emit('error')` 可以使 `form.parse` 报错。
- 使用外部变量可以在出现第一个错误时取消所有上传。

### 防盗链

防盗链（Hotlink Protection）是一种防止其他网站直接链接到你服务器上的资源（如图片、视频、文件等）的技术。通过防盗链，你可以确保只有你授权的网站或应用能够访问这些资源，从而减少带宽消耗和资源滥用。

```js
const express = require('express')
const app = express()

// 允许的域名中间件
// 检测请求头中的 Referer 是否在允许的域名列表中
app.use((req, res, next) => {
  // 从请求头获取Referer
  const referer = req.get('Referer')

  // 允许的域名列表
  const allowedDomains = ['127.0.0.1']

  if (referer) {
    // 如果请求头中设置的有Referer, 则获取Referer的域名
    const refererHost = new URL(referer).hostname

    console.log(allowedDomains.includes(refererHost))

    // 如果Referer在允许的域名列表中，继续处理请求
    if (!allowedDomains.includes(refererHost)) {
      res.status(404).send('<h1>你不能访问这个资源</h1>')
      return
    }
  }
  next()
})

app.use(express.static(__dirname + '/public')) // 假设你的资源放在public目录下

app.listen(3000, () => {
  console.log('Server is running on http://localhost:3000/')
})
```

### 路由模块化

路由模块化是将路由分离到不同的文件中，可以提高代码的可维护性和可复用性。

```js
// app.js
const express = require('express')
const app = express()
const adminRouter = require('./routers/admin')
const homeRouter = require('./routers/home')
app.use(express.static(__dirname + '/public')) // 假设你的资源放在public目录下
app.use('/admin', adminRouter)
app.use('/home', homeRouter)
app.use((req, res, next) => {
  res.status(404).send("Sorry can't find that!")
})
app.listen(3000, () => {
  console.log('Server is running on http://localhost:3000/')
})

// ./routers/admin.js
const express = require('express')
const router = express.Router()
router.get('/', (req, res) => {
  res.send('Admin')
})
module.exports = router

// ./routers/home.js
const express = require('express')
const router = express.Router()
router.get('/', (req, res) => {
  res.send('Home')
})
module.exports = router
```

### EJS 模板引擎

EJS 是一款基于 Node.js 的模板引擎，可以将数据和 html 页面分开，通过模板引擎将数据渲染到 html 页面中。

<!-- 前后端分离前的模板引擎，现在不用了，因为前端渲染已经成为主流。前端框架如 React、Vue 的模板引擎比ejs更加强大，可以直接渲染组件。 -->

```js
const ejs = require('ejs')

let love = 'I love you'

// 使用 ejs 模板引擎渲染模板
let result = ejs.render('<h1><%= love %></h1>', { love: love })

console.log(result) // <h1>I love you</h1>
```

### express 应用生成器

express 应用生成器是一个命令行工具，可以快速生成 express 应用的骨架代码。

前后端耦合，使用 ejs 模板引擎渲染页面，使用 express 路由处理请求，

```bash
# 安装 express-generator 工具
npm i -g express-generator
# 使用 express 命令生成应用骨架
express -e myapp
# 安装依赖
cd myapp && npm i
# 启动应用
npm start
```

### 操作 cookie

```bash
# 安装 cookie-parser 包
pnpm i cookie-parser
```

```js
const express = require('express')
const app = express()
const cookieParser = require('cookie-parser')
const port = 1111

app.use(express.static('public'))
app.use(cookieParser())

app.get('/setCookie', (req, res) => {
  // 设置一个名为name，值为value的cookie, 有效期为默认值（浏览器关闭时失效）
  res.cookie('name', 'value')
  // 设置一个名为name，值为value的cookie，有效期为900000秒，只能通过http协议访问
  res.cookie('name', 'value', { maxAge: 900000, httpOnly: true, secure: true })
  // 设置一个名为name，值为value的cookie，有效期为900000秒，跨域访问时不发送cookie
  res.cookie('name', 'value', { maxAge: 900000, sameSite: 'none' })
  res.send('Cookie set!')
})

app.get('/getCookie', (req, res) => {
  // 获取名为name的cookie
  const name = req.cookies.name
  res.send(`Cookie value: ${name}`)
})

app.get('/clearCookie', (req, res) => {
  // 清除名为name的cookie
  res.clearCookie('name')
  res.send('Cookie cleared!')
})

app.use((req, res, next) => {
  res.status(404).send("Sorry can't find that!")
})

app.listen(port, () => {
  console.log(`http://localhost:${port}`)
})
```

### session

```bash
# 安装 express-session connect-mongo 包
pnpm i express-session connect-mongo
```

```js
const express = require('express')
const app = express()
const session = require('express-session')
const MongoStore = require('connect-mongo')
const port = 1111

app.use(express.static('public'))
// 设置session中间件
app.use(
  session({
    name: 'sid', //设置cookie的name
    secret: 'secret', //参与加密的字符串(签名)
    resave: false, //是否每次请求都重新保存session
    saveUninitialized: true, //是否为每次请求都设置一个cookie用来存储session的id
    store: MongoStore.create({
      mongoUrl: 'mongodb://localhost:27017/test', //连接mongodb的url
      collection: 'sessions', //设置collection的名称
    }),
    cookie: {
      maxAge: 1000 * 60 * 60 * 24 * 7, //cookie的有效期，单位是ms
      httpOnly: true, //是否只允许http访问,防止通过js操作cookie
    },
  })
)

// session的设置
app.get('/login', (req, res) => {
  if (req.query.username === 'admin' && req.query.password === '123456') {
    req.session.username = req.query.username
    req.session.password = req.query.password
    res.send('登陆成功')
  } else {
    res.send('用户名或密码错误')
  }
})

// session的获取
app.get('/profile', (req, res) => {
  if (req.session.username && req.session.password) {
    res.send(`欢迎${req.session.username}登陆`)
  } else {
    res.send('请先登陆')
  }
})

// session的删除
app.get('/logout', (req, res) => {
  req.session.destroy((err) => {
    if (err) {
      console.log(err)
    }
    res.send('退出成功')
  })
})

app.use((req, res, next) => {
  res.status(404).send("Sorry can't find that!")
})

app.listen(port, () => {
  console.log(`http://localhost:${port}`)
})
```

### Md5 加密

```bash
# 安装 MD5 加密包
```

## Mongodb 数据库

Mongodb 是一个基于分布式文件存储的数据库。

特点:

1. 速度更快
2. 拓展性更强
3. 安全性更强
4. 语法与 js 相似，容易上手成本低

## Mongoose 驱动

Mongoose 是一个 Node.js 平台上一个 ODM（对象文档映射）库，用于简化 MongoDB 的操作。

方法:

- `mongoose.connect(uri, options)`: 连接到 MongoDB 数据库。uri 是数据库的地址，options 是连接选项
- `mongoose.model(name, schema)`: 创建一个模型。name 是模型的名称，schema 是定义文档结构的 Schema 对象
- `mongoose.Schema(definition)`: 创建一个 Schema 对象，用于定义文档的结构。definition 是一个对象，描述文档的字段及其类型
- `mongoose.disconnect()`: 断开与 MongoDB 的连接
- `mongoose.connection`: 获取当前的数据库连接对象
- `mongoose.Types.ObjectId()` : 创建唯一标识符类型(new mongoose.Types.ObjectId())
- `mongoose.Types.Decimal128()` : 创建十进制类型(new mongoose.Types.Decimal128())
- `mongoose.Types.Buffer()` : 创建二进制类型(new mongoose.Types.Buffer())
- `mongoose.Types.Mixed()` : 创建任意类型(new mongoose.Types.Mixed())
- `mongoose.Schema.Types.ObjectId` : 指定 ObjectId 类型
- `mongoose.Schema.Types.Decimal128` : 指定 Decimal128 类型
- `mongoose.Schema.Types.Buffer` : 指定 Buffer 类型
- `mongoose.Schema.Types.Mixed` : 指定 Mixed 类型

```js
// 导入 mongoose 模块
const mongoose = require('mongoose')

// 连接数据库, test 是数据库名称(如果不存在,会自动创建)
mongoose.connect('mongodb://127.0.0.1:27017/test')

// 设置一个回调
// 设置连接成功的回调
mongoose.connection.once('open', () => {
  console.log('数据库连接成功')
})
// 设置连接失败的回调
mongoose.connection.on('error', (err) => {
  console.log('数据库连接失败', err)
})
// 设置连接关闭的回调
mongoose.connection.on('close', () => {
  console.log('数据库连接关闭')
})

setTimeout(() => {
  // 关闭数据库连接
  mongoose.connection.close()
}, 2000)
```

### 插入文档

1. **`Model.create()`**:

   - 这是一个静态方法，用于创建一个或多个新的文档，并将其插入到集合中。
   - 例如：`VehicleModel.create({ runConfig: {...}, modelConfig: {...}, ... })`

2. **`Model.insertMany()`**:

   - 这也是一个静态方法，用于创建多个新的文档并插入到集合中。
   - 适用于需要一次性插入多个文档的场景。
   - 例如：`VehicleModel.insertMany([{ runConfig: {...}, modelConfig: {...}, ... }, {...}])`

3. **`Model.collection.insertOne()`** 和 **`Model.collection.insertMany()`**:

   - 这些是直接操作 MongoDB 集合的方法。`insertOne()`用于插入单个文档，`insertMany()`用于插入多个文档。
   - 例如：`VehicleModel.collection.insertOne({ runConfig: {...}, modelConfig: {...}, ... })`

4. **`new Model(doc)` 和 `doc.save()`**:
   - 可以先创建一个模型实例，然后调用`save()`方法将其保存到数据库中。
   - 例如：
     ```javascript
     const vehicle = new VehicleModel({ runConfig: {...}, modelConfig: {...}, ... });
     await vehicle.save();
     ```

```js
// 导入 mongoose 模块
const mongoose = require('mongoose')

// 连接数据库, test 是数据库名称(如果不存在,会自动创建)
mongoose.connect('mongodb://127.0.0.1:27017/test')

// 设置一个回调
// 设置连接成功的回调
mongoose.connection.once('open', async () => {
  console.log('数据库连接成功')
  // 创建文档的结构对象
  // 设置集合中文文档的属性以及属性值的类型
  let BookSchema = new mongoose.Schema({
    name: String,
    author: String,
    price: Number,
  })
  // 创建模型对象，对文档操作的封装对象
  // mongoose 对尾部没有s的单词都加s
  let BookModel = mongoose.model('Book', BookSchema)
  // 新增文档
  try {
    const data = await BookModel.create({
      name: '红楼梦',
      author: '曹雪芹',
      price: 99.99,
    })
    console.log('新增文档成功', data)
    // 关闭数据库连接
    mongoose.connection.close()
  } catch (err) {
    console.log('新增文档失败', err)
  }
})
// 设置连接失败的回调
mongoose.connection.on('error', (err) => {
  console.log('数据库连接失败', err)
})
// 设置连接关闭的回调
mongoose.connection.on('close', () => {
  console.log('数据库连接关闭')
})
```

### 字段类型

Mongoose 支持丰富的字段类型，包括：

1. String : 字符串类型
2. Number : 数字类型
3. Boolean : 布尔类型
4. Array : 数组类型(也可以用[]标识)
5. ObjectId : 唯一标识符类型(对象 id，需要使用 mongoose.Schema.Types.ObjectId 指定)
6. Date : 日期类型
7. Buffer : 二进制数据类型(Buffer 对象, 可以存储文件，需要用 mongoose.Schema.Types.Buffer 指定)
8. Mixed : 任意类型(可以存储任何类型的数据,需要用 mongoose.Schema.Types.Mixed 指定)
9. Decimal128 : 十进制类型(用于存储高精度的数字，需要使用 mongoose.Schema.Types.Decimal128 指定)

注：mongoose.Types.ObjectId() 是用于生成新的 ObjectId，而 mongoose.Schema.Types.ObjectId 是用于在 schema 中定义 ObjectId 类型的字段

```js
// 导入 mongoose 模块
const mongoose = require('mongoose')

// 连接数据库, test 是数据库名称(如果不存在,会自动创建)
mongoose.connect('mongodb://127.0.0.1:27017/test')

// 设置一个回调
// 设置连接成功的回调
mongoose.connection.once('open', async () => {
  console.log('数据库连接成功')
  // 创建文档的结构对象
  // 设置集合中文文档的属性以及属性值的类型
  let BookSchema = new mongoose.Schema({
    name: String,
    author: String,
    price: Number,
    is_hot: Boolean,
    tags: [String],
    pub_time: Date,
    id: mongoose.Schema.Types.ObjectId,
  })
  // 创建模型对象，对文档操作的封装对象
  let BookModel = mongoose.model('Book', BookSchema)
  // 新增文档
  try {
    const data = await BookModel.create({
      name: '红楼梦',
      author: '曹雪芹',
      price: 99.99,
      is_hot: true,
      tags: ['古典', '名著', 1],
      pub_time: new Date(),
      id: new mongoose.Types.ObjectId(),
    })
    console.log('新增文档成功', data)
    // 关闭数据库连接
    mongoose.connection.close()
  } catch (err) {
    console.log('新增文档失败', err)
  }
})
// 设置连接失败的回调
mongoose.connection.on('error', (err) => {
  console.log('数据库连接失败', err)
})
// 设置连接关闭的回调
mongoose.connection.on('close', () => {
  console.log('数据库连接关闭')
})
```

### 字段验证

```js
// 导入 mongoose 模块
const mongoose = require('mongoose')

// 连接数据库, test 是数据库名称(如果不存在,会自动创建)
mongoose.connect('mongodb://127.0.0.1:27017/test')

// 设置一个回调
// 设置连接成功的回调
mongoose.connection.once('open', async () => {
  console.log('数据库连接成功')
  // 创建文档的结构对象
  // 设置集合中文文档的属性以及属性值的类型
  let BookSchema = new mongoose.Schema({
    name: {
      // 字段类型
      type: String,
      // 字段是否必填
      required: true,
      // required: function () {
      //   // 动态判断
      //   return this.isAdmin // 如果是管理员，则 role 必填
      // },
      // 默认值
      default: '红楼梦',
      // 枚举值(值必须是枚举值中的一个)
      enum: ['红楼梦', '三国演义', '水浒传', '西游记', '雪山飞狐'],
      // 正则验证邮箱格式
      match: /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/,
      // 自定义验证函数
      validate: {
        validator: (v) => /^\d{11}$/.test(v),
        message: '手机号格式错误！',
      },
    },
    author: {
      type: String,
      // 唯一值(设置为独一无二的,不能重复)
      // 在新集合内才能生效
      unique: true,
      index: true, // 为字段创建索引
      // 字符串长度限制
      minlength: 3,
      maxlength: 20,
    },
    price: { type: Number, min: 0, max: 150 }, // 数字最小值和最大值
    password: {
      type: String,
      select: false, // 查询时默认不返回此字段
      // 在读取或写入字段时对值进行转换
      set: (v) => {
        return v + '123'
      },
      get: (v) => {
        return v.slice(0, -3)
      },
      // 别名
      alias: 'pwd',
      // 引用其他模型，表示该字段存储的是另一个模型的 ObjectId
      ref: 'User',
      // 自动去除前后空格
      trim: true,
    },
  })
  // 创建模型对象，对文档操作的封装对象
  let BookModel = mongoose.model('Book', BookSchema)
  // 新增文档
  try {
    const data = await BookModel.create({
      name: '红楼梦',
      author: '曹雪芹',
      price: 99.99,
    })
    console.log('新增文档成功', data)
    // 关闭数据库连接
    mongoose.connection.close()
  } catch (err) {
    console.log('新增文档失败', err)
  }
})
// 设置连接失败的回调
mongoose.connection.on('error', (err) => {
  console.log('数据库连接失败', err)
})
// 设置连接关闭的回调
mongoose.connection.on('close', () => {
  console.log('数据库连接关闭')
})
```

```js
const user = await User.findOne().select('+password') // 临时包含 password
// 定义复合索引（name 升序，age 降序）
schema.index({ name: 1, age: -1 })
```

### 删除文档

1. **`Model.deleteOne()`**:

   - 删除与指定条件匹配的第一个文档。
   - 示例：
     ```javascript
     await VehicleModel.deleteOne({ status: '已终止' })
     ```

2. **`Model.deleteMany()`**:

   - 删除所有与指定条件匹配的文档。
   - 示例：
     ```javascript
     await VehicleModel.deleteMany({ status: '已终止' })
     ```

3. **`Model.findByIdAndDelete()`**:

   - 根据提供的文档 ID 删除文档。
   - 示例：
     ```javascript
     await VehicleModel.findByIdAndDelete(id)
     ```

4. **`Model.findOneAndDelete()`**:
   - 删除第一个与指定条件匹配的文档。
   - 示例：
     ```javascript
     await VehicleModel.findOneAndDelete({ status: '已终止' })
     ```

```js
// 导入 mongoose 模块
const mongoose = require('mongoose')

// 连接数据库, test 是数据库名称(如果不存在,会自动创建)
mongoose.connect('mongodb://127.0.0.1:27017/test')

// 设置一个回调
// 设置连接成功的回调
mongoose.connection.once('open', async () => {
  console.log('数据库连接成功')
  // 创建文档的结构对象
  // 设置集合中文文档的属性以及属性值的类型
  let BookSchema = new mongoose.Schema({
    name: {
      // 字段类型
      type: String,
      // 字段是否必填
      required: true,
      // 默认值
      default: '红楼梦',
      // 枚举值(值必须是枚举值中的一个)
      enum: ['红楼梦', '三国演义', '水浒传', '西游记', '雪山飞狐'],
    },
    author: {
      type: String,
    },
    price: Number,
  })
  // 创建模型对象，对文档操作的封装对象
  let BookModel = mongoose.model('books', BookSchema)
  // 删除一条文档
  try {
    const data = await BookModel.deleteOne({ name: '红楼梦' })
    console.log('删除文档成功', data)
  } catch (err) {
    console.log('删除文档失败', err)
  }
  // deletedCount 代表删除的文档数量
  // { acknowledged: true, deletedCount: 1 }

  // 删除多条文档
  try {
    const data = await BookModel.deleteMany({ author: '曹雪芹' })
    console.log('删除文档成功', data)
  } catch (err) {
    console.log('删除文档失败', err)
  }
})
// 设置连接失败的回调
mongoose.connection.on('error', (err) => {
  console.log('数据库连接失败', err)
})
// 设置连接关闭的回调
mongoose.connection.on('close', () => {
  console.log('数据库连接关闭')
})
```

### 更新文档

1. **`Model.update()`**: 更新所有符合条件的文档。

   - 语法：`Model.update(conditions, doc, [options], [callback]);`
   - 例子：`VehicleModel.update({ status: '运行中' }, { status: '已完成' });`

2. **`Model.updateOne()`**: 更新符合条件的第一个文档。

   - 语法：`Model.updateOne(conditions, doc, [options], [callback]);`
   - 例子：`VehicleModel.updateOne({ status: '运行中' }, { status: '已终止' });`

3. **`Model.updateMany()`**: 更新所有符合条件的文档（与`Model.update()`类似，但默认情况下不会更新多个文档，除非设置`{ multi: true }`）。

   - 语法：`Model.updateMany(conditions, doc, [options], [callback]);`
   - 例子：`VehicleModel.updateMany({ status: '运行中' }, { status: '已完成' });`

4. **`Model.findOneAndUpdate()`**: 查找符合条件的第一条文档并更新。

   - 语法：`Model.findOneAndUpdate(conditions, doc, [options], [callback]);`
   - 例子：`VehicleModel.findOneAndUpdate({ status: '运行中' }, { status: '已终止' });`

5. **`Model.findByIdAndUpdate()`**: 根据文档 ID 查找文档并更新。

   - 语法：`Model.findByIdAndUpdate(id, doc, [options], [callback]);`
   - 例子：`VehicleModel.findByIdAndUpdate('507f1f77bcf86cd799439011', { status: '已完成' });`

6. **`Model.replaceOne()`**: 根据条件查找第一条文档并替换它（不更新，而是替换整个文档）。
   - 语法：`Model.replaceOne(conditions, doc, [options], [callback]);`
   - 例子：`VehicleModel.replaceOne({ status: '运行中' }, { status: '已完成', description: '车辆检测任务' });`

```js
// 导入 mongoose 模块
const mongoose = require('mongoose')

// 连接数据库, test 是数据库名称(如果不存在,会自动创建)
mongoose.connect('mongodb://127.0.0.1:27017/test')

// 设置一个回调
// 设置连接成功的回调
mongoose.connection.once('open', async () => {
  console.log('数据库连接成功')
  // 创建文档的结构对象
  // 设置集合中文文档的属性以及属性值的类型
  let BookSchema = new mongoose.Schema({
    name: String,
    author: String,
    price: Number,
  })
  // 创建模型对象，对文档操作的封装对象
  // mongoose 对尾部没有s的单词都加s
  let BookModel = mongoose.model('Book', BookSchema)
  // 更新一条文档
  try {
    // 只会去更新同名属性
    const data = await BookModel.updateOne(
      {
        name: '红楼梦',
      },
      { price: 1000 }
    )
    console.log('更新文档成功', data)
  } catch (err) {
    console.log('更新文档失败', err)
  }

  // 更新多条文档
  try {
    const data = await BookModel.updateMany(
      {
        author: '曹雪芹',
      },
      { price: 2000 }
    )
    console.log('更新多条文档成功', data)
  } catch (err) {
    console.log('更新多条文档失败', err)
  }
  2
})
// 设置连接失败的回调
mongoose.connection.on('error', (err) => {
  console.log('数据库连接失败', err)
})
// 设置连接关闭的回调
mongoose.connection.on('close', () => {
  console.log('数据库连接关闭')
})
```

### 读取文档

1. **`Model.find()`**:

   - 查找所有匹配条件的文档。如果不需要条件查找，传入空对象即可。
   - 示例：`await VehicleModel.find({ field1: 'value1' })`
   - 获取所有文档：`await VehicleModel.find({})`

2. **`Model.findOne()`**:

   - 查找第一个匹配条件的文档。
   - 示例：`await VehicleModel.findOne({ field1: 'value1' })`

3. **`Model.findById()`**:
   - 通过 ID 查找文档。
   - 示例：`await VehicleModel.findById('your_document_id')`

```js
// 导入 mongoose 模块
const mongoose = require('mongoose')

// 连接数据库, test 是数据库名称(如果不存在,会自动创建)
mongoose.connect('mongodb://127.0.0.1:27017/test')

// 设置一个回调
// 设置连接成功的回调
mongoose.connection.once('open', async () => {
  console.log('数据库连接成功')
  // 创建文档的结构对象
  // 设置集合中文文档的属性以及属性值的类型
  let BookSchema = new mongoose.Schema({
    name: String,
    author: String,
    price: Number,
  })
  // 创建模型对象，对文档操作的封装对象
  // mongoose 对尾部没有s的单词都加s
  let BookModel = mongoose.model('Book', BookSchema)
  // 通过属性读取单条文档
  try {
    let book = await BookModel.findOne({ name: '红楼梦' })
    console.log(book)
  } catch (error) {
    console.log(error)
  }
  console.log('---------------------------')
  // 通过id读取单条文档
  try {
    let book = await BookModel.findById('67d1470ff0db075efde8a82d')
    console.log(book)
  } catch (error) {
    console.log(error)
  }
  console.log('---------------------------')
  // 批量获取
  try {
    let books = await BookModel.find({ author: '曹雪芹' })
    console.log(books)
  } catch (error) {
    console.log(error)
  }
  console.log('---------------------------')
  // 直接获取全部文档
  try {
    let books = await BookModel.find()
    console.log(books)
  } catch (error) {
    console.log(error)
  }
})
// 设置连接失败的回调
mongoose.connection.on('error', (err) => {
  console.log('数据库连接失败', err)
})
// 设置连接关闭的回调
mongoose.connection.on('close', () => {
  console.log('数据库连接关闭')
})
```

### 条件控制

#### 运算符

- > 使用 $gt 表示大于
- < 使用 $lt 表示小于
- > = 使用 $gte 表示大于等于
- <= 使用 $lte 表示小于等于
- !== 使用 $ne 表示不等于

```js
// 导入 mongoose 模块
const mongoose = require('mongoose')

// 连接数据库, test 是数据库名称(如果不存在,会自动创建)
mongoose.connect('mongodb://127.0.0.1:27017/test')

// 设置一个回调
// 设置连接成功的回调
mongoose.connection.once('open', async () => {
  console.log('数据库连接成功')
  // 创建文档的结构对象
  // 设置集合中文文档的属性以及属性值的类型
  let BookSchema = new mongoose.Schema({
    name: String,
    author: String,
    price: Number,
  })
  // 创建模型对象，对文档操作的封装对象
  let BookModel = mongoose.model('Book', BookSchema)
  try {
    const data = await BookModel.find({ price: { $gt: 100 } })
    console.log(data)
  } catch (err) {
    console.log(err)
  }
})
// 设置连接失败的回调
mongoose.connection.on('error', (err) => {
  console.log('数据库连接失败', err)
})
// 设置连接关闭的回调
mongoose.connection.on('close', () => {
  console.log('数据库连接关闭')
})
```

#### 逻辑运算

- $and 使用逻辑与运算符
- $or 使用逻辑或运算符

```js
// 导入 mongoose 模块
const mongoose = require('mongoose')

// 连接数据库, test 是数据库名称(如果不存在,会自动创建)
mongoose.connect('mongodb://127.0.0.1:27017/test')

// 设置一个回调
// 设置连接成功的回调
mongoose.connection.once('open', async () => {
  console.log('数据库连接成功')
  // 创建文档的结构对象
  // 设置集合中文文档的属性以及属性值的类型
  let BookSchema = new mongoose.Schema({
    name: String,
    author: String,
    price: Number,
  })
  // 创建模型对象，对文档操作的封装对象
  let BookModel = mongoose.model('Book', BookSchema)
  try {
    const data = await BookModel.find({ $or: [{ name: 'node' }, { author: '曹雪芹' }] })
    console.log(data)
  } catch (err) {
    console.log(err)
  }
  try {
    const data = await BookModel.create({
      $and: [{ price: { $gt: 100 } }, { price: { $lt: 200 } }],
    })
    console.log(data)
  } catch (err) {
    console.log(err)
  }
})
// 设置连接失败的回调
mongoose.connection.on('error', (err) => {
  console.log('数据库连接失败', err)
})
// 设置连接关闭的回调
mongoose.connection.on('close', () => {
  console.log('数据库连接关闭')
})
```

#### 正则匹配

```js
// 导入 mongoose 模块
const mongoose = require('mongoose')

// 连接数据库, test 是数据库名称(如果不存在,会自动创建)
mongoose.connect('mongodb://127.0.0.1:27017/test')

// 设置一个回调
// 设置连接成功的回调
mongoose.connection.once('open', async () => {
  console.log('数据库连接成功')
  // 创建文档的结构对象
  // 设置集合中文文档的属性以及属性值的类型
  let BookSchema = new mongoose.Schema({
    name: String,
    author: String,
    price: Number,
  })
  // 创建模型对象，对文档操作的封装对象
  let BookModel = mongoose.model('Book', BookSchema)
  try {
    const data = await BookModel.find({ name: new RegExp('^' + '红') })
    console.log(data)
  } catch (err) {
    console.log(err)
  }
})
// 设置连接失败的回调
mongoose.connection.on('error', (err) => {
  console.log('数据库连接失败', err)
})
// 设置连接关闭的回调
mongoose.connection.on('close', () => {
  console.log('数据库连接关闭')
})
```

### 自定义读取

- select :指定需要返回的字段
- sort :指定排序规则
- skip :指定跳过的记录数
- limit :指定返回的记录数
- exec :执行查询并返回结果

```js
// 导入 mongoose 模块
const mongoose = require('mongoose')

// 连接数据库, test 是数据库名称(如果不存在,会自动创建)
mongoose.connect('mongodb://127.0.0.1:27017/test')

// 设置一个回调
// 设置连接成功的回调
mongoose.connection.once('open', async () => {
  console.log('数据库连接成功')
  // 创建文档的结构对象
  // 设置集合中文文档的属性以及属性值的类型
  let BookSchema = new mongoose.Schema({
    name: String,
    author: String,
    price: Number,
  })
  // 创建模型对象，对文档操作的封装对象
  let BookModel = mongoose.model('Book', BookSchema)
  try {
    const data = await BookModel.find({ name: new RegExp('^' + '红') })
      .select({ name: 1, author: 1, price: 1 }) // 选择返回的字段(将字段属性值设置为1返回,0不返回,不写默认为0)
      .sort({ price: 1 }) // 1表示升序,-1表示降序,排序的字段必须要在返回的字段中
      .skip(1) // 跳过前面的条数
      .limit(2) // 限制返回的条数
      .exec()
    console.log(data)
  } catch (err) {
    console.log(err)
  }
})
// 设置连接失败的回调
mongoose.connection.on('error', (err) => {
  console.log('数据库连接失败', err)
})
// 设置连接关闭的回调
mongoose.connection.on('close', () => {
  console.log('数据库连接关闭')
})
```

### 查询中间件

```js
// 保存前预处理
userSchema.pre('save', function (next) {
  if (this.isModified('password')) {
    this.password = hashPassword(this.password)
  }
  next()
})

// 查询后处理
userSchema.post('find', function (docs) {
  console.log(`查询到 ${docs.length} 条数据`)
})
```

### Mongoose 模块化

```js
// config.js
module.exports = {
  DBHOST: '127.0.0.1',
  DBPORT: 27017,
  DBNAME: 'test',
}
```

```js
// db.js
/**
 *
 * @param {Function} success 数据库连接成功的回调函数
 * @param {Function} error 数据库连接失败的回调函数
 * @param {Function} close 数据库连接关闭的回调函数
 */
module.exports = (
  success = () => {
    console.log('数据库连接成功')
  },
  error = (err) => {
    console.log('数据库连接失败', err)
  },
  close = () => {
    console.log('数据库连接已关闭')
  }
) => {
  // 导入 mongoose 模块
  const mongoose = require('mongoose')
  const { DBHOST, DBPORT, DBNAME } = require('./config.js')

  // 连接数据库, test 是数据库名称(如果不存在,会自动创建)
  mongoose.connect(`mongodb://${DBHOST}:${DBPORT}/${DBNAME}`)

  // 设置连接成功的回调
  mongoose.connection.once('open', () => {
    success()
  })

  // 设置连接失败的回调
  mongoose.connection.on('error', (err) => {
    error(err)
  })

  // 设置连接关闭的回调
  mongoose.connection.on('close', () => {
    close()
  })
}
```

```js
// BooksModel.js
// 导入 mongoose 模块
const mongoose = require('mongoose')
// 创建文档的结构对象
// 设置集合中文文档的属性以及属性值的类型
let BookSchema = new mongoose.Schema({
  name: String,
  author: String,
  price: Number,
})
// 创建模型对象，对文档操作的封装对象
let BookModel = mongoose.model('Book', BookSchema)

module.exports = BookModel
```

```js
// index.js
const db = require('./db')
const mongoose = require('mongoose')
const BookModel = require('./BooksModel')

db(async () => {
  console.log('数据库连接成功!!!')

  try {
    const data = await BookModel.find({ name: new RegExp('^' + '红') })
      .select({ name: 1, author: 1, price: 1 }) // 选择返回的字段(将字段属性值设置为1返回,0不返回,不写默认为0)
      .sort({ price: 1 }) // 1表示升序,-1表示降序,排序的字段必须要在返回的字段中
      .skip(1) // 跳过前面的条数
      .limit(2) // 限制返回的条数
      .exec()
    console.log(data)
    mongoose.connection.close()
  } catch (err) {
    console.log(err)
  }
})
```

### 图形化管理工具 Studio 3T

Studio 3T 是 MongoDB 图形化管理工具，可以用来管理 MongoDB 数据库。
[下载](https://studio3t.com/download/)

## express 配合 mongoose

## child_process

Node.js 内置的 child_process 模块可以用来创建子进程，可以用来执行 shell 命令。

```js
const { exec } = require('child_process')

// Python脚本路径
const pythonScriptPath = './my.py'

// 执行Python脚本
exec(`python ${pythonScriptPath}`, (error, stdout, stderr) => {
  if (error) {
    console.error(`执行错误: ${error.message}`)
    return
  }
  if (stderr) {
    console.error(`Python脚本错误: ${stderr}`)
    return
  }
  console.log(`Python脚本输出: ${stdout}`)
})
```

```js
const { spawn } = require('child_process')

// Python脚本路径
const pythonScriptPath = 'path/to/your/script.py'

// 创建子进程
const pythonProcess = spawn('python', [pythonScriptPath])

// 监听标准输出
pythonProcess.stdout.on('data', (data) => {
  console.log(`Python脚本输出: ${data}`)
})

// 监听标准错误
pythonProcess.stderr.on('data', (data) => {
  console.error(`Python脚本错误: ${data}`)
})

// 监听进程结束
pythonProcess.on('close', (code) => {
  console.log(`Python脚本执行结束，退出码: ${code}`)
})
```

# Node JS + TS

使用 **TypeScript（TS）** 搭建 Node.js 服务器是非常推荐的，尤其是对于中大型项目或需要长期维护的代码库。以下是详细的理由、优势、注意事项和实践建议：

#### 优势

- **类型安全**：静态类型检查能在编码阶段捕捉潜在错误（如拼写错误、参数类型不匹配），减少运行时崩溃。
- **更好的开发体验**：IDE 支持智能提示、代码补全和重构（如 VS Code），提升开发效率。
- **代码可维护性**：清晰的类型定义让代码更易读，适合团队协作和长期维护。
- **现代语法支持**：TypeScript 支持 ES6+ 语法（如 `async/await`、装饰器），并能编译成兼容旧版 Node.js 的代码。
- **生态完善**：主流 Node.js 框架（如 Express、NestJS、Fastify）和库（如 TypeORM）都提供了完善的 TypeScript 支持。

### 如何开始

#### 基础工具链

- **TypeScript 编译器**：`npm install -D typescript`
- **TS Node**：直接运行 `.ts` 文件（开发环境）
  `npm install -D ts-node`
- **类型定义**：安装 Node.js 和框架的类型包，如：
  `npm install -D @types/node @types/express`

#### 用 Express 搭建 TS 服务器

1. **初始化项目**：

   ```bash
   npm init -y
   npm install express
   npm install -D typescript ts-node @types/node @types/express
   npx tsc --init  # 生成 tsconfig.json
   ```

2. **配置 `tsconfig.json`**：

   ```json
   {
     "compilerOptions": {
       "target": "ES2020",
       "module": "CommonJS",
       "outDir": "./dist",
       "rootDir": "./src",
       "strict": true,
       "esModuleInterop": true
     }
   }
   ```

3. **编写代码 (`src/index.ts`)**：

   ```typescript
   import express, { Request, Response } from 'express'

   const app = express()
   const port = 3000

   interface Message {
     text: string
   }

   app.get('/', (req: Request, res: Response<Message>) => {
     res.json({ text: 'Hello TypeScript!' })
   })

   app.listen(port, () => {
     console.log(`Server running on http://localhost:${port}`)
   })
   ```

4. **运行与编译**：
   - **开发环境**（实时运行）：
     `npx ts-node src/index.ts`
   - **生产环境**（编译为 JS）：
     `npx tsc && node dist/index.js`

### 推荐的框架和工具

- **Express + TypeScript**：灵活轻量，适合快速原型开发。
- **NestJS**：基于 TypeScript 的企业级框架，内置依赖注入、模块化架构，适合复杂应用。
- **Fastify**：高性能框架，官方提供 TypeScript 支持。
- **Prisma/TypeORM**：类型安全的 ORM，与 TypeScript 深度集成。

### 性能考量

- **编译后的 JS 代码性能与原生 JS 一致**，TypeScript 的类型检查仅在编译阶段，不影响运行时性能。
- 使用 `ts-node` 在开发时可能会有轻微启动延迟，生产环境建议始终使用编译后的 JS 文件。

### 框架分析

### Express + TypeScript

#### 特点

- **轻量灵活**：Express 是 Node.js 最经典的轻量级框架，无强制约束，适合自由组合中间件。
- **TypeScript 支持**：通过 `@types/express` 提供类型定义，结合 TS 实现类型安全的路由、请求/响应和中间件。
- **中间件生态**：庞大的中间件库（如 `cors`、`helmet`、`morgan`），适合快速集成功能。

#### 代码示例

```typescript
import express, { Request, Response } from 'express'
import cors from 'cors'

const app = express()
app.use(cors())
app.use(express.json())

interface User {
  id: number
  name: string
}

app.get('/user/:id', (req: Request<{ id: string }>, res: Response<User>) => {
  const userId = parseInt(req.params.id)
  res.json({ id: userId, name: 'Alice' })
})

app.listen(3000, () => {
  console.log('Server running on port 3000')
})
```

#### 优点

- 学习成本低，适合 Node.js 初学者。
- 高度自由，可根据需求灵活选择工具链。
- 社区资源丰富，问题容易解决。

#### 缺点

- 缺乏内置架构规范，大型项目易失控。
- 需手动配置 TS 编译、类型定义和项目结构。

#### 适用场景

- 快速原型开发、小型 API 服务。
- 已有 Express 项目迁移到 TypeScript。
- 开发者偏好“按需配置”而非“开箱即用”。

---

### NestJS

#### 特点

- **企业级框架**：基于 Angular 的设计理念，提供依赖注入（DI）、模块化（Modules）、控制器（Controllers）和服务（Services）等抽象。
- **TypeScript 原生**：完全围绕 TypeScript 设计，强制类型安全。
- **开箱即用**：集成 Swagger（API 文档）、微服务支持、WebSocket、GraphQL 等模块。
- **架构规范**：通过装饰器（如 `@Get()`、`@Post()`）定义路由，强制分层架构（Controller-Service-Repository）。

#### 代码示例

```typescript
// user.controller.ts
import { Controller, Get, Param } from '@nestjs/common'

interface User {
  id: number
  name: string
}

@Controller('users')
export class UserController {
  @Get(':id')
  getUser(@Param('id') id: string): User {
    return { id: parseInt(id), name: 'Alice' }
  }
}

// app.module.ts
import { Module } from '@nestjs/common'
import { UserController } from './user.controller'

@Module({
  controllers: [UserController],
})
export class AppModule {}
```

#### 优点

- 适合大型复杂应用，代码结构清晰易维护。
- 完善的官方生态（如 `@nestjs/typeorm`、`@nestjs/config`）。
- 内置测试工具（如 `Test.createTestingModule`）。

#### 缺点

- 学习曲线陡峭（需理解依赖注入、装饰器等概念）。
- 项目初始化较复杂，可能对小型项目过度设计。

#### 适用场景

- 企业级应用、长期维护的中大型项目。
- 需要严格架构规范的团队协作。
- 需快速集成 Swagger、微服务等高级功能。

---

### Fastify

#### 特点

- **高性能**：基准测试中吞吐量显著高于 Express，适合高并发场景。
- **插件化架构**：通过插件（如 `@fastify/jwt`）扩展功能，核心保持精简。
- **JSON Schema 集成**：内置请求/响应验证，替代手动校验逻辑。
- **TypeScript 原生**：无需额外类型包，提供精确的路由泛型支持。

#### 代码示例

```typescript
import fastify, { FastifyRequest, FastifyReply } from 'fastify'

const app = fastify()

app.get<{ Params: { id: string } }>(
  '/user/:id',
  {
    schema: {
      params: {
        type: 'object',
        properties: { id: { type: 'string' } },
      },
    },
  },
  async (req, res) => {
    const userId = parseInt(req.params.id)
    return { id: userId, name: 'Alice' }
  }
)

app.listen({ port: 3000 })
```

#### 优点

- 性能优异，适合资源敏感型应用。
- 开发体验现代化（如异步优先、类型安全）。
- 插件生态丰富（如 WebSocket、数据库集成）。

#### 缺点

- 中间件生态不如 Express 庞大。
- 部分 Express 中间件需适配才能使用。

#### 适用场景

- 高频 API 服务、实时通信（如 WebSocket）。
- 对性能有严格要求的微服务。
- 希望从 Express 迁移到更现代的框架。

### Prisma / TypeORM

#### 特点

|              | **Prisma**                              | **TypeORM**                                      |
| ------------ | --------------------------------------- | ------------------------------------------------ |
| **定位**     | 类型安全的 ORM + 查询构建器             | 传统 ORM，支持 Active Record 和 Data Mapper 模式 |
| **TS 支持**  | 完全类型安全的查询（自动生成类型）      | 需手动定义实体类型                               |
| **迁移工具** | 强大的 `prisma migrate`                 | 基础迁移支持                                     |
| **查询语法** | 链式 API（如 `prisma.user.findMany()`） | 类 SQL 或 Repository 模式                        |
| **生态**     | 较新，但增长迅速                        | 成熟，支持多种数据库                             |

#### Prisma 示例

```typescript
// schema.prisma
model User {
  id    Int    @id @default(autoincrement())
  name  String
}

// app.ts
import { PrismaClient } from '@prisma/client';
const prisma = new PrismaClient();

const user = await prisma.user.findUnique({ where: { id: 1 } });
```

#### TypeORM 示例

```typescript
// user.entity.ts
import { Entity, PrimaryGeneratedColumn, Column } from 'typeorm'

@Entity()
export class User {
  @PrimaryGeneratedColumn()
  id: number

  @Column()
  name: string
}

// app.ts
import { getRepository } from 'typeorm'
const userRepository = getRepository(User)
const user = await userRepository.findOne({ where: { id: 1 } })
```

#### 推荐选择

- **Prisma**：适合现代 TypeScript 项目，追求类型安全和开发效率。
- **TypeORM**：适合需要兼容多种数据库或已有 ActiveRecord 模式的项目。

### 综合推荐

#### 根据场景选择

1. **快速原型 / 小型项目**

   - **框架**：Express + TypeScript
   - **ORM**：Prisma（简化数据库操作）
   - **理由**：低学习成本，快速上手。

2. **企业级复杂应用**

   - **框架**：NestJS
   - **ORM**：TypeORM（与 `@nestjs/typeorm` 集成）
   - **理由**：架构规范，适合团队协作和长期维护。

3. **高性能 API / 微服务**

   - **框架**：Fastify
   - **ORM**：Prisma
   - **理由**：高吞吐量 + 类型安全的数据库交互。

4. **实时通信（如 WebSocket）**
   - **框架**：Fastify + `@fastify/websocket`
   - **ORM**：按需选择 Prisma/TypeORM

#### 个人偏好

- **如果你重视性能和现代性**：Fastify + Prisma
- **如果你需要严格的架构约束**：NestJS + TypeORM
- **如果你习惯 Express 生态**：Express + TypeScript + Prisma

### 总结

- **Express + TypeScript**：自由灵活，适合“自己掌控一切”的开发者。
- **NestJS**：企业级首选，适合需要规范和扩展性的项目。
- **Fastify**：性能至上的现代框架，适合高频 API 和实时场景。
- **Prisma**：类型安全的 ORM 新星，适合追求开发效率的团队。

# 开发必备第三方包

1. **nodemon** 是一个监视文件的工具，可以自动重启 node 应用。
2. **express** 是 Node.js 平台上一个快速、开放、极简的 web 开发框架。
3. **mongoose** 是 MongoDB 的 Node.js 驱动程序，是一个对象文档映射（ODM）库，用于简化 MongoDB 的操作。
4. **socket.io** 是 Node.js 平台上一个实时通信的框架，支持跨平台通信。
5. **pm2** 是 Node.js 平台上一个进程管理工具，可以用来管理 Node.js 应用，还可以提供负载均衡等功能。
6. **gulp** 是基于流的自动化构建工具，可以自动化地完成重复性任务。
7. **webpack** 是模块打包工具，可以将 JavaScript 模块打包成浏览器可识别的格式，还可以处理其他类型的资源文件。
8. **mocha** 是 Node.js 平台上一个测试框架，可以用来编写测试用例。
9. **chai** 是 Node.js 平台上一个断言库，可以用来编写测试用例。
10. **axios** 是基于 promise 的 HTTP 客户端，可以用来发送 HTTP 请求。
11. **dayjs** 是轻量级的日期处理库，可以用来处理日期。
12. **jose** 是 JSON Web Token 的实现，可以用来生成和验证 JWT。
13. **lodash** 是 JavaScript 实用工具库，提供了一致性、模块化、高性能等功能。
14. **vueuse** 是 Vue 3 的插件库，提供了一系列有用的功能。
15. **vuex** 是 Vue 2 的状态管理库，可以用来管理组件间的状态。
16. **vite** 是下一代前端构建工具，可以用来开发 Vue 3 应用。
17. **pinia** 是 Vue 3 的状态管理库，可以用来管理组件间的状态。
18. **electron** 是使用 JavaScript、HTML 和 CSS 构建跨平台桌面应用的框架。
19. **vue-router** 是 Vue 3 的路由库，可以用来管理应用的路由。
20. **nuxtjs** 是基于 Vue 3 的服务器端渲染框架，可以用来开发 SSR 应用。
21. **react-router** 是 React 路由库，可以用来管理应用的路由。
22. **eslint** 是 JavaScript 代码检查工具，可以用来检查代码质量。
23. **prettier** 是代码格式化工具，可以用来格式化代码。
24. **body-parser** 是 Node.js 平台上一个解析 HTTP 请求体数据的中间件( application/json 或 application/x-www-form-urlencoded 上传数据)。
25. **formidable** 是 Node.js 平台上一个解析 HTTP 请求体数据的库( multipart/form-data 上传文件)。
26. **lowdb** 是 Node.js 平台上一个轻量级的 JSON 数据库，可以用来存储数据。
27. **fluent-ffmpeg** 是 Node.js 平台上一个用来处理多媒体文件的库，可以用来处理视频、音频等多媒体文件。
28. **js-yaml** 是 Node.js 平台上一个用来解析 YAML 文件的库。
29. **nodemailer** 是 Node.js 平台上一个用来发送邮件的库。
30. **multer** 是 Node.js 平台上一个用来处理 multipart/form-data 上传文件的库。

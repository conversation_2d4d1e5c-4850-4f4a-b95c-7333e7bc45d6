<!DOCTYPE html>
<html>
<head>
  <title>Markdown 查看器</title>
  <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
  <link rel="stylesheet" 
        href="https://cdnjs.cloudflare.com/ajax/libs/github-markdown-css/5.2.0/github-markdown.min.css">
  <style>
    .markdown-body { box-sizing: border-box; max-width: 980px; margin: 0 auto; padding: 45px; }
    @media (max-width: 767px) { .markdown-body { padding: 15px; } }
  </style>
</head>
<body>
  <div class="markdown-body" id="content"></div>
  
  <script>
    // 获取要显示的文件
    const urlParams = new URLSearchParams(window.location.search);
    const mdFile = urlParams.get('file');
    
    if(mdFile) {
      fetch(mdFile)
        .then(response => response.text())
        .then(text => {
          document.getElementById('content').innerHTML = marked.parse(text);
        });
    } else {
      document.getElementById('content').innerHTML = '<h1>文件未指定</h1>';
    }
  </script>
</body>
</html>
[TOC]

# Typescript 知识点学习

## TS 编译

### 终端命令

- tsc 文件名(练习.ts)
  - 文件后缀名可写可不写
  - 作用是将指定的 ts 文件编译成 js 文件
  - 缺点是不能实时编译,每次更改了 ts 文件都要再手动编译
- node 文件名(练习.js)
  - 文件后缀名可写可不写
  - 作用是可以在终端执行代码,将打印信息打印在终端

**牢记！！！**

- tsc --init
  - 生成配置文件
- tsc --watch
  - 启用自动编译 ts 文件

### 常用配置文件信息

- noEmitOnError:该选项为 true 时,则只有当没有任何错误时,编译器才会输出文件
- target:设置编译 js 文件时所遵循的 ES 规范
- strict:开启所有的严格模式配置
- rootDir:编译器会将该目录下的 ts 文件进行编译
- outDir：所有编译好的 js 文件都会放在该目录下
- 启用装饰器
  - experimentalDecorators:启用实验性的装饰器支持,装饰器是 TypeScript 为类和类成员提供额外元数据的一种声明方式。
  - emitDecoratorMetadata:当使用装饰器时,这个选项会增加设计时类型元数据的发出,这用于某些反射场景,如 Angular 的依赖注入。
  - 需要关闭
    useDefineForClassFields:决定是否使用 defineProperty 来定义类字段，这与类字段的提案兼容。

## Typescript

### 基元类型

**如果我们在声明变量时,没有规定变量的类型并且没有赋值,该变量会被 ts 隐式推断为 any 类型；如果没有规定变量类型但是赋值了,则会把该变量的数据类型推断为赋值的类型**

```typescript
// 注意都是小写
// 大写是一个对象,可能会占用更多的内存,不建议用大写
let str: string = "six-flower";
let age: number = 21;
let bole: boolean = true;
let nul: null = null;
let undef: undefined = undefined;
```

### 数组

```typescript
let arr: number[] = [1, 2];
// 下面是泛型写法
let arr2: Array<number> = [1, 2];
let arr3: (number | string)[] = [1, "2"];
arr3 = ["a", "b"];
let arr4: [number, string] = [1, "a"];
let arr5: [number, ...string[]] = [1];
arr5 = [1, "a"];
arr5 = [1, "a", "b"];
```

### 函数

```typescript
// 规定xy要传入number类型的实参,规定函数返回值为number类型
// 写法1,形参名可更改
let arr: (a: number, b: number) => void = function (x, y) {};
// 写法2
function add(x: number, y: number): number {
  return x + y;
}
// 设置默认值,如果默认值是前面的参数我们想取到默认值,只需传入undefined就可以取到默认值
function add(x: number = 1, y: number) {
  return x + y;
}
console.log(add(undefined, 2));
// 规定y为可传可不传的参数
function add2(x: number, y?: number): number {
  if (y) {
    return x + y;
  } else {
    return x;
  }
}
// 类型缩小,即缩小类型的范围,如下,让y传入的是字符串时才能使用字符串方法
function up_lower(x: string, y?: string | number): void {
  if (typeof y === "string") {
    console.log(x, y.toLocaleUpperCase);
  }
}
// ?表示是一个可选属性,可选参数不能放在必选参数前面
function up_lower(x: string, y?: string): void {
  if (y) {
    console.log(x, y.toLocaleUpperCase);
  }
  console.log(x, y?.toLocaleLowerCase);
  // 编译为js代码： console.log(x, y === null || y === void 0 ? void 0 : y.toLocaleLowerCase);
}
```

### 对象

```typescript
// 不要使用object和Object,范围太过宽泛,
// 如果用object   函数、数组、所有对象等都在其内
// 如果用Object   数字,字符串等,有构造函数的也都能往里面存了
let obj: { a: number; b: string } = { a: 1, b: "b" };
// 对象内a属性放数值类型的数组
let obj2: { a: number[] } = { a: [1, 2, 3] };
// 冒号前加个问号表示可有可无
let obj3: { a: number; b?: string } = { a: 1 };
// 如果不声明类型
let obj4 = { a: 1, b: "1" };
// 此时obj4中只能存a和b两个属性,并且a只能存number类型,b只能存字符串类型
// 如果声明类型是一个空对象,则变量想存什么样的对象都可以
let obj5: {} = { a: 1, b: "1" };
obj5 = { c: true };
// 自定义类型,可以让对象索引的类型不受限制
let obj6: { [key: string]: string } = { a: "1", b: "2" };
```

### any

```typescript
// any会禁用所有的类型检查,如果声明变量全用any就跟js没有什么区别了了,
let obj: any = { a: 1 };
obj = 1;
obj = "asd";
obj = undefined;
obj = ["ad", 21];
obj = function () {};
```

### 类型别名

```typescript
// 创建出的类型是无法更改(拓展)的
// 定义一个包含数值,字符串,布尔值的类型
type nsb = number | string | boolean;
let x: nsb = 1;
x = "1";
x = true;
// 定义一个字符串数组类型
type arr = string[];
let y: arr = ["1", "2"];
// 定义一个对象类型
type obj = { a: number; b?: number };
let z: obj = { a: 1 };
z = { a: 2, b: 2 };
// 继承一个类型别名
type obj1 = obj & { c: number };
let z2: obj1 = { a: 1, b: 2, c: 3 };
```

### 联合类型

```typescript
// 类似于或
type a = number | string | boolean;
let x: a = 1;
x = "1";
x = true;
```

### 交叉类型

```typescript
// 类型别名配合交叉类型,可以模拟继承
type Point2D = {
  x: number;
  y: number;
};
type Point3D = Point2D & {
  z: number;
};
let a: Point2D = { x: 1, y: 2 };
let b: Point3D = { x: 1, y: 2, z: 3 };
```

### 接口

**接口是用来描述一个对象的**

```typescript
// 定义一个接口
// 接口看起来和类很像,但是接口内没有代码实现,只规定了使用这个接口定义的对象需要有哪几个属性和方法
// 属性和方法不能多也不能少,如果有属性是可选的,需要在属性名后面加问号
// 在定义接口时,在属性名前面写上readonly,可以限制为只读属性
interface Obj {
  // 设置只读属性a
  readonly a: number;
  // 可选属性b
  b?: number;
}
let x: Obj = { a: 1 };
let x: Obj = { a: 1, b: 1 };
// 继承一个接口
interface Obj1 extends Obj {
  c: number;
}
let y: Obj1 = { a: 1, b: 2, c: 3 };
// 通过再定义一次这个接口,从而为该类型拓展新的元素
// 注意：此时下面为接口Obj拓展了一个c属性
// 拓展后的接口对所有使用该接口的变量都生效的,所以上方的那个x变量如果不声明c属性会报错
// 同时继承接口,也继承的是最新的接口
interface Obj {
  d: number;
}
let z: Obj = { a: 1, b: 2, d: 3 };
z.a = 1;
```

！[图 1](./images/assets/ts_知识点学习_数据类型_接口.jpg)

用接口描述函数

```typescript
interface ISum {
  (x: number, y: number): number;
}
const add: ISum = (num1, num2) => {
  return num1 + num2;
};
```

接口的自定义属性名

```typescript
interface RandomKey {
  [propName: string]: string;
}
//propName 写成什么都行
//下面在定义对象的时候,obj注解了RandomKey类型,对象里面的属性名就可以任意的,属性值必须是字符串
const obj: RandomKey = {
  a: "hello",
  b: "lin",
  c: "welcome",
};
```

### 类型断言

```typescript
// 很多函数返回的类型都是联合类型,如果确定返回值为一个确定的类型,可以用类型断言
// 如获取dom对象的方法返回的就是一个联合类型：div标签返回的是：HTMLDivElement | null(鼠标移到变量名上查看)
// 此时如果要使用dom对象的属性就需要使用断言
let div = document.querySelector("div") as HTMLDivElement;
// let div = document.querySelector("div") as any;或者有any也行
div!.innerHTML; //!是非空断言,null会被排除掉
// 此处将"a"的类型断言为any类型,使得x变成any类型变量
let x = "a" as any;
x = 1;
// "a"直接断言为number类型会报错,所以先断言为any然后断言为number,从而将变量y变成数值类型
let y = "a" as any as number;
y = 1;

// ! 非空断言,告诉编译器他一定是个非空(非null或undefined)的值
function up_lower(x: string, y?: string): void {
  if (y) {
    console.log(x, y.toLocaleUpperCase);
  }
  console.log(x, y!.toLocaleLowerCase);
  // 编译为js代码：console.log(x, y.toLocaleLowerCase);
}
```

### 字面量类型

```typescript
// 像这样可以让变量内只能存固定的值
type numW = 1 | 2 | "six-flower";
let x: numW = 1;
x = 2;
x = "six-flower";
```

```typescript
const a = { a: 1, b: "GET" } as const;
function b(x: number, b: "GET" | "POST") {}
b(a.a, a.b);
```

- 当我们规定函数参数只能传入某个文字类型时,如下,传入了 "GET" 但是 ts (类型推断)只会将传入的参数推断为字符串,这时我们可以使用类型断言解决
  ！[图 1](./images/assets/ts_知识点学习_数据类型_文字类型1.jpg)
- 将传入的实参断言为它本身
  ！[图 2](./images/assets/ts_知识点学习_数据类型_文字类型2.jpg)
- 或者用 as const 关键字,将对象 a 断言为只读类型,确保值不会被修改,此时 ts 会把其中的属性推断为常量(此时 a.b 是文字类型"GET")
  ！[图 3](./images/assets/ts_知识点学习_数据类型_文字类型3.jpg)

### 泛型

泛型是指在定义函数、接口等类型的时候,不预先指定具体的类型,而是在使用的时候再指定具体类型的一种特性。

```typescript
// 下面是泛型在函数中的应用
// 用一个类型T代替类型声明,在使用时传入类型T,以此来实现实时指定类型
function a<T, U>(x: T, y: T, z: U) {
  console.log(x, y, z);
}
a<string, number>("a", "b", 1);
a<number, boolean>(1, 2, true);
a<boolean, string>(true, false, "1");

// 下面是泛型在接口中的应用
interface student<T, U> {
  school: T;
  name: T;
  age: U;
}
let a: student<string, number> = {
  school: "大学",
  name: "wu",
  age: 1,
};
```

### 枚举类型

```typescript
// 定义一个枚举类型
enum Dir1 {
  Up,
  Down,
  Left,
  Right,
}
console.log(Dir1);
// Dir1.Up = 0
// Dir1.Down = 1
// Dir1.Left = 2
// Dir1.Right = 3
// Dir1[0] = Up
// Dir1[1] = Down
// Dir1[2] = Left
// Dir1[3] = Right
enum Dir2 {
  Up = 4,
  Down,
  Left,
  Right,
}
console.log(Dir2);
// 常量枚举,可以有效地简化代码
const enum Dir3 {
  Up = "up",
  Down = "down",
  Left = "left",
  Right = "right",
  No = 465,
}
// 只有下面的用法
console.log(Dir3.Up);
console.log(Dir3.No);
console.log(Dir3.Left);
console.log(Dir3.Right);
console.log(Dir3.Down);
```

下面是编译成的 js 代码

```javascript
"use strict";
// 定义一个枚举类型
var Dir1;
(function (Dir1) {
  Dir1[(Dir1["Up"] = 0)] = "Up";
  Dir1[(Dir1["Down"] = 1)] = "Down";
  Dir1[(Dir1["Left"] = 2)] = "Left";
  Dir1[(Dir1["Right"] = 3)] = "Right";
})(Dir1 || (Dir1 = {}));
console.log(Dir1);
// Dir1.Up = 0
// Dir1.Down = 1
// Dir1.Left = 2
// Dir1.Right = 3
// Dir1[0] = Up
// Dir1[1] = Down
// Dir1[2] = Left
// Dir1[3] = Right
var Dir2;
(function (Dir2) {
  Dir2[(Dir2["Up"] = 4)] = "Up";
  Dir2[(Dir2["Down"] = 5)] = "Down";
  Dir2[(Dir2["Left"] = 6)] = "Left";
  Dir2[(Dir2["Right"] = 7)] = "Right";
})(Dir2 || (Dir2 = {}));
console.log(Dir2);
console.log("up" /* Dir3.Up */);
console.log(465 /* Dir3.No */);
console.log("left" /* Dir3.Left */);
console.log("right" /* Dir3.Right */);
console.log("down" /* Dir3.Down */);
```

![图1](./images/assets/ts_知识点学习_数据类型_枚举类型.jpg)

### 类

#### 创建一个基础的类

```typescript
class Hero {
  name: string;
  constructor(name: string) {
    this.name = name;
  }
  run() {
    console.log(this.name + "跑路");
  }
  // 存取器
  get _name() {
    console.log(this.name + "取值了");
    return this.name;
  }
  set _name(value) {
    console.log("为" + this.name + "赋了新值");

    this.name = value;
  }
}
let XQ = new Hero("小乔");
XQ.run();
XQ._name = "小小乔";
XQ._name;
```

![图片1](./images/assets/ts_知识点学习_数据类型_类1.jpg)

#### 类的继承

```typescript
class Hero {
  name: string;
  constructor(name: string) {
    this.name = name;
  }
  run() {
    console.log(this.name + "跑路");
  }
}
class FaShi extends Hero {
  gender: "男" | "女";
  obj: {};
  constructor(name: string, gender: "男" | "女", obj: {}) {
    super(name);
    this.gender = gender;
    this.obj = {
      ...obj,
    };
  }
}
let XQ = new FaShi("小乔", "女", {});
```

#### 属性修饰符

- public:公有的
- private:私有的
- protected:受保护的

```typescript
class A {
  // public 公有的
  // 默认访问修饰符,任何地方都可以访问该属性,包括类的外部和子类
  public a: string;
  // private 私有的
  // 仅在类的内部可访问,无法在类的外部或子类中访问
  private b: string;
  // protected 受保护的
  // 类的内部和任何子类中可访问,无法在类的外部访问
  protected c: string;
  // 下面是拓展
  // readonly 只读的
  // 该属性只能在类的构造函数中赋值,之后不能被修改
  // 可以与其他修饰符结合使用
  readonly d: string;
  private readonly e: string;
  // static 静态属性,只有类构造函数能调用的属性,实例无法调用
  constructor(a: string, b: string, c: string, d: string, e: string) {
    this.a = a;
    this.b = b;
    this.c = c;
    this.d = d;
    this.e = e;
  }
}
class B extends A {
  constructor(a: string, b: string, c: string, d: string, e: string) {
    super(a, b, c, d, e);
  }
}
```

#### 类的实例属性的简写

```typescript
class A {
  constructor(public a: string, private b: string, protected c: string, readonly d: string) {}
}
let a = new A("1", "2", "3", "4");
```

#### 抽象类

抽象类无法被实例化,只能被继承,抽象类实质上就是一个父类,一个专门用来让子类继承的父类

```typescript
// 定义抽象类的方法和定义类相同,不同点在于要加一个关键字abstract,抽象类不能被实例化,继承抽象类的子类必须有相同的属性和方法
// 抽象类实质上就是一个父类,一个专门用来让子类继承的父类
abstract class Hero {
  public name: string;
  constructor(name: string) {
    this.name = name;
  }
  abstract logName(): string;
}
class FaShi extends Hero {
  constructor(name: string) {
    super(name);
  }
  logName(): string {
    console.log(this.name);
    return this.name;
  }
}
let xQ = new FaShi("小乔");
xQ.logName();
```

### 接口与类

```typescript
// 定义一个接口
interface Hero {
  name: string;
  logName: () => void;
}
interface Gender {
  gender: "男" | "女";
  logGender: () => void;
}
// 类使用接口
class FaShi implements Hero, Gender {
  constructor(public name: string, public gender: "男" | "女") {}
  logName() {
    console.log(this.name);
  }
  logGender() {
    console.log(this.gender);
  }
}
// let xQ = new FaShi("小乔", "女");
// 类也可以作为一个类型
// 相同的,js中的内置对象,如Array、Date、Error、RegExp、DOM对象 HTMLElement、DOM数组 NodeList、事件对象 MouseEvent也可以作为一个类型
let xQ: FaShi = new FaShi("小乔", "女");
xQ.logName();
```

### 装饰器

使用装饰器来实现 AOP(Aspect Oriented Program)编程-面向切面编程

- 装饰器实质上就是一个方法(函数), 可以注入到 类, 属性, 方法 中对其进行一些扩展
- 当一个类加了多个装饰器,装饰器从下往上运行

#### 类装饰器 ClassDecorator

- 一个接收参数:类函数

```typescript
function KillHero(target: Function) {
  target.prototype.kill = function () {
    console.log("拿到一个人头");
    if (!this.score) {
      this.score = 1;
    } else {
      this.score++;
    }
  };
  // 覆盖更新方法
  let oldFn = target.prototype.logName;
  target.prototype.logName = function () {
    console.log("新加的逻辑");
    // 注意,这里改变this指向可以返回一个新函数,不然会报错(这里没有利用闭包,oldFn变量会消失)
    oldFn.call(this);
    oldFn.apply(this);
    oldFn.bind(this)();
  };
}

@KillHero
class Hero {
  constructor(public name: string) {}
  public logName() {
    console.log("英雄名字叫" + this.name);
  }
}
let XQ: any = new Hero("小乔");
XQ.logName();
XQ.kill();
console.log(XQ.score);
```

![图片1](./images/assets/ts_知识点学习_数据类型_类装饰器.jpg)

#### 方法装饰器 MethodDecorator

- 三个接收参数:类函数、方法名、成员属性描述符

```typescript
function LogScore(target: object, propertyKey: string, descriptor: PropertyDescriptor) {
  console.log(target); // 对静态方法来说是类的构造函数,对于实例方法来说是类的原型对象prototype
  console.log(propertyKey); // 方法名
  console.log(descriptor); // 成员的属性描述符
  console.log(descriptor.value); //value 获取到修饰的函数
  let oldFn = descriptor.value;
  descriptor.value = function () {
    console.log("覆盖方法,添加功能");
    // 注意,这里改变this指向可以返回一个新函数,不然会报错(这里没有利用闭包,oldFn变量会消失)
    oldFn.call(this);
    oldFn.apply(this);
    oldFn.bind(this)();
  };
}
class Hero {
  constructor(public name: string, public score: number = 0) {}
  @LogScore
  public logName() {
    console.log("英雄名字叫" + this.name);
  }
}
let XQ: any = new Hero("小乔");
XQ.logName();
```

![图片1](./images/assets/ts_知识点学习_数据类型_方法装饰器.jpg)

#### 属性装饰器 PropertyDecorator

- 两个接收参数:类函数、属性名称

```typescript
function GetSet(target: object, propertyKey: string) {
  console.log(target); // 对象静态方法来说是类的构造函数,对于实例方法来说是类的原型对象prototype
  console.log(propertyKey); // 属性名
  Object.defineProperty(target, propertyKey, {
    set(v) {
      console.log("设置" + propertyKey + "属性值为" + v);
      this["_" + propertyKey] = v;
    },
    get() {
      console.log("获取" + propertyKey + "属性值");
      return this["_" + propertyKey];
    },
  });
}
class Hero {
  @GetSet public name: string;
  constructor(name: string) {
    this.name = name;
  }
  public logName() {
    console.log("英雄名字叫" + this.name);
  }
}
let XQ: any = new Hero("小乔");
console.log(XQ.name);
XQ.name = "可爱小乔";
console.log(XQ.name);
console.log(XQ);
```

![图片1](./images/assets/ts_知识点学习_数据类型_属性装饰器.jpg)

#### 参数装饰器 ParameterDecorator

- 三个接收参数:类函数、参数名、参数所在位置的索引

```typescript
function LogParameter(target: any, fnName: string, parameterIndex: number) {
  console.log(target); // 对象静态方法来说是类的构造函数,对于实例方法来说是类的原型对象prototype
  console.log(fnName); // 修饰参数的方法名
  console.log(parameterIndex); // 参数所在位置的索引
}

class Example {
  fn(@LogParameter param: string) {
    console.log(param);
  }
}

const example = new Example();
example.fn("Hello, world!");
```

![图片1](./images/assets/ts_知识点学习_数据类型_参数装饰器.jpg)

### 装饰器工厂

顾名思义,装饰器工厂就是生产装饰器的,需要返回一个函数作为装饰器

```typescript
function KillHero(a: number) {
  return function (target: Function) {
    target.prototype.kill = function () {
      console.log("拿到人头");
      this.score += a;
    };
  };
}

@KillHero(5)
class Hero {
  constructor(public name: string, public score: number = 0) {}
  public logName() {
    console.log("英雄名字叫" + this.name);
  }
}
let XQ: any = new Hero("小乔");
XQ.logName();
XQ.kill();
console.log(XQ.score);
```

![图片1](./images/assets/ts_知识点学习_数据类型_装饰器工厂.jpg)

## 文件

### xx.d.ts 文件:类型声明文件

在该文件里声明的类型可以在该项目中所有 ts 文件中使用

# Vue 与 TypeScript

## 无法识别 vue 组件

```ts
// env.d.ts 加入以下内容
// 通过声明文件
// 告诉ts，所有的vue文件的他的类型 是一个 vue组件
declare module "*.vue" {
  import type { DefineComponent } from "vue";
  const vueComponent: DefineComponent<{}, {}, any>;
  export default vueComponent;
}
// 或者
declare module "*.vue" {
  import type { DefineComponent } from "vue";
  const vueComponent: DefineComponent<object, object, unknown>;
  export default vueComponent;
}
```

## 为组件的 props 标注类型

**可以不标注类型，如果要标注类型，可以用以下方式**

### 1. 运行时声明

```vue
<script setup lang="ts">
import { defineProps } from "vue";
const props = defineProps({
  // 必填值
  foo: { type: String, required: true },
  // 可选值
  bar: Number,
});

props.foo; // string
props.bar; // number | undefined
</script>
```

### 2. 通过泛型参数声明

```vue
<script setup lang="ts">
import { defineProps } from "vue";
const props = defineProps<{
  // 必填值
  foo: string;
  // 可选值
  bar?: number;
}>();
</script>
```

### 3. 通过接口声明

```vue
<script setup lang="ts">
import { defineProps } from "vue";
interface Props {
  foo: string;
  bar?: number;
}

const props = defineProps<Props>();
</script>
```

### Props 通过解构声明默认值

```vue
<script setup lang="ts">
interface Props {
  msg?: string;
  labels?: string[];
}
// 解构赋值声明默认值
const { msg = "hello", labels = ["one", "two"] } = defineProps<Props>();
</script>
```

## 为组件的 emits 标注类型

**可以不标注类型，如果要标注类型，可以用以下方式**

### 1. 运行时声明

```vue
<script setup lang="ts">
import { defineEmits } from "vue";
// 基于选项
// 自己验证参数类型
const emit = defineEmits({
  click: (id: number) => {
    // 返回 `true` 或 `false`
    // 表明验证通过或失败
    return typeof id === "number";
  },
});
</script>
```

### 2. 通过泛型参数声明

```vue
<script setup lang="ts">
import { defineEmits } from "vue";
// 基于类型
const emit = defineEmits<{
  (e: "change", id: number): void;
  (e: "update", value: string): void;
}>();
</script>
```

### 3.最简洁的声明方式

```vue
<script setup lang="ts">
import { defineEmits } from "vue";
const emit = defineEmits<{
  change: [id: number];
  update: [value: string];
}>();
</script>
```

## 为 ref 标注类型

**不标注类类型会自己推导类型**

### Ref 类型

```vue
<script setup lang="ts">
import { ref } from "vue";
import type { Ref } from "vue";
// 基本类型
const num: Ref<number> = ref(1);
// 数组与对象等复杂类型
const a: Ref<number[]> = ref([1]);
const a: Ref<{ num: string }> = ref({ num: "1" });
// 自定义类型
const obj: Ref<{ [propName: string]: string }> = ref({ num: "1", str: "1" });
// 类型别名
type nustr = number | string;
const a: Ref<nustr[]> = ref([1, "1"]);
// 联合类型
const year: Ref<string | number> = ref("2020");
</script>
```

### 泛型参数

```vue
<script setup lang="ts">
import { ref } from "vue";
// 得到的类型：Ref<string | number>
const year = ref<string | number>("2020");
// 如果你指定了一个泛型参数但没有给出初始值，那么最后得到的就将是一个包含 undefined 的联合类型
// 推导得到的类型：Ref<number | undefined>
const n = ref<number>();
</script>
```

### 接口声明

```vue
<script setup lang="ts">
import { ref } from "vue";
interface User {
  name: string;
  age: number;
}
const user = ref<User>({ name: "张三", age: 20 });
</script>
```

## 为 reactivity 标注类型

### 隐式推导

```vue
<script setup lang="ts">
import { reactive } from "vue";
// 推导得到的类型：{ title: string }
const book = reactive({ title: "Vue 3 指引" });
</script>
```

### 显式声明

```vue
<script setup lang="ts">
import { reactive } from "vue";
// 不推荐使用 reactive() 的泛型参数，因为处理了深层次 ref 解包的返回值与泛型参数的类型不同。
// 推荐使用接口声明
interface Book {
  title: string;
  year?: number;
}
const book: Book = reactive({ title: "Vue 3 指引" });
</script>
```

## 为 computed 标注类型

### 隐式推导

```vue
<script setup lang="ts">
import { ref, computed } from "vue";
const count = ref(0);
// 推导得到的类型：ComputedRef<number>
const double = computed(() => count.value * 2);
// => TS Error: Property 'split' does not exist on type 'number'
const result = double.value.split("");
</script>
```

### 显式声明

```vue
<script setup lang="ts">
import { computed, ref } from "vue";
const num = ref(1);
const double = computed<number>(() => {
  // 若返回值不是 number 类型则会报错
  return num.value;
});
</script>
```

## 为事件处理函数标注类型

### 1. 没有声明会被隐式标注为 any 类型

```vue
<script setup lang="ts">
function handleChange(event) {
  // `event` 隐式地标注为 `any` 类型
  console.log(event.target.value);
}
</script>

<template>
  <input
    type="text"
    @change="handleChange" />
</template>
```

![](./images/assets/ts_VueTs_事件处理标注类型1.png)

### 2. 声明后

```vue
<script setup lang="ts">
function handleChange(event: Event) {
  // `event` 隐式地标注为 `any` 类型
  console.log(event.target.value);
}
</script>

<template>
  <input
    type="text"
    @change="handleChange" />
</template>
```

![1730382332609](./images/assets/ts_VueTs_事件处理标注类型2.png)

### 3. 完整写法

```vue
<script setup lang="ts">
function handleChange(event: Event) {
  // `event` 显式地标注为 `Event` 类型
  console.log((event.target as HTMLInputElement).value);
}
</script>
```

## 为 provide/inject 标注类型

Vue 提供了一个 InjectionKey 接口，它是一个继承自 Symbol 的泛型类型，可以用来在提供者和消费者之间同步注入值的类型：

```vue
<!-- 将注入 key 的类型放在一个单独的文件中，这样它就可以被多个组件导入 -->
<!-- 不同组件内声明的key不相同，不是同一个，所以建议放在一个单独文件中 -->
<!-- InjectionKey.ts -->
<script lang="ts">
import type { InjectionKey } from "vue";
interface User {
  name: string;
  age: number;
}
// 方法一
export const UserKey1 = Symbol() as InjectionKey<string>;
// 方法二
export const UserKey2: InjectionKey<User> = Symbol();
</script>
<!-- 父组件 -->
<script setup lang="ts">
// 引入子组件
import { provide } from "vue";
import { UserKey1, UserKey2 } from "@/InjectionKey";
const user = { name: "张三", age: 20 };
provide(UserKey1, "hello");
provide(UserKey2, user);
</script>

<!-- 子组件 -->
<script setup lang="ts">
import { inject } from "vue";
import { UserKey1, UserKey2 } from "@/InjectionKey";
// 不设默认值
const name = inject(UserKey1); // string | undefined
// 设置默认值
const user = inject(UserKey2, { name: "default name", age: 0 }); // User
// {name: string;age: number;}
</script>
```

### Nest.js 常用命令

| name:名称     | alias:别名 | description:描述               |
| ------------- | ---------- | ------------------------------ |
| application   | app        | 生成一个新的应用工作区         |
| class         | cl         | 生成一个类                     |
| configuration | config     | 生成一个 CLI 配置文件          |
| controller    | co         | 生成控制器声明                 |
| decorator     | d          | 生成自定义装饰器               |
| filter        | f          | 生成过滤器声明                 |
| gateway       | ga         | 生成网关声明                   |
| guard         | gu         | 生成守卫声明                   |
| interceptor   | itc        | 生成拦截器声明                 |
| interface     | itf        | 生成接口                       |
| library       | lib        | 在 monorepo 中生成一个新的库   |
| middleware    | mi         | 生成中间件声明                 |
| module        | mo         | 生成模块声明                   |
| pipe          | pi         | 生成管道声明                   |
| provider      | pr         | 生成提供者声明                 |
| resolver      | r          | 生成 GraphQL 解析器声明        |
| resource      | res        | 生成一个新的 CRUD 资源         |
| service       | s          | 生成服务声明                   |
| sub-app       | app        | 在 monorepo 中生成一个新的应用 |

```bash
# 安装 config 和 @type/config 作为全局变量
pnpm i config @type/config
```

```bash
# 创建项目
nest new demoProject
# help
nest --help
# 创建一个模块
nest g mo test
# 创建一个服务
nest g s test --no-spec
# 创建一个控制器
nest g co test --no-spec

# 快速生成一个完整的模块
nest g res test
```

### 内置异常处理器

Nest.js 提供了一些内置的异常处理器，可以帮助我们快速处理常见的错误。

- HttpException ： 基础异常处理器，用于处理 HTTP 错误
- BadRequestException ： 400 Bad Request - 请求参数错误
- UnauthorizedException ： 401 Unauthorized - 未授权
- NotFoundException ： 404 Not Found - 资源未找到
- ForbiddenException ： 403 Forbidden - 禁止访问
- NotAcceptableException ： 406 Not Acceptable - 不可接受
- RequestTimeoutException ： 408 Request Timeout - 请求超时
- ConflictException ： 409 Conflict - 资源冲突
- GoneException ： 410 Gone - 资源已删除
- HttpVersionNotSupportedException ： 505 HTTP Version Not Supported - 不支持的 HTTP 版本
- PayloadTooLargeException ： 413 Payload Too Large - 请求实体过大
- UnsupportedMediaTypeException ： 415 Unsupported Media Type - 不支持的媒体类型
- UnprocessableEntityException ： 422 Unprocessable Entity - 无法处理的实体
- InternalServerErrorException ： 500 Internal Server Error - 服务器内部错误
- NotImplementedException ： 501 Not Implemented - 尚未实现
- ImATeapotException ： 418 I'm a Teapot - 我是茶壶
- MethodNotAllowedException ： 405 Method Not Allowed - 不允许的请求方法
- BadGatewayException ： 502 Bad Gateway - 网关错误
- ServiceUnavailableException ： 503 Service Unavailable - 服务不可用
- GatewayTimeoutException ： 504 Gateway Timeout - 网关超时
- PreconditionFailedException ： 412 Precondition Failed - 前置条件失败

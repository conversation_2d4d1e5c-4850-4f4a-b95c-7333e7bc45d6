[TOC]

# CSS 层叠样式表

## 书写位置

- 内联样式：直接在 HTML 标签的 style 属性中写 CSS 代码，如：`<p style="color:red;">这是一个红色的段落。</p>`
- 内部样式表：在 HTML 文档的 head 标签中添加一个 style 标签，并在 style 标签中写 CSS 代码
- 外部样式表：在 HTML 文档的 head 标签中添加一个 link 标签，并设置 rel 和 href 属性，分别指向外部样式表的路径和文件名，如：`<link rel="stylesheet" href="mystyle.css">`

### 优先级

CSS 样式的优先级是：内联样式 > 内部样式表 > 外部样式表 > 浏览器默认样式。

## 选择器

### 基础选择器

- `*`：通配符选择器，选择所有元素。
- `element`：标签选择器，选择所有指定标签的元素。
- `.class`：类选择器，选择所有带有指定类的元素。
- `#id`：ID 选择器，选择所有带有指定 ID 的元素。

### 复杂选择器

- 群组选择器：用逗号分隔的多个选择器，表示同时匹配多个选择器的元素，如`div,p{color:red;}`
- 后代选择器：用空格分隔的两个选择器，表示选择第一个选择器元素的所有后代元素，如`div p{color:red;}`
- 子代选择器：用 > 符号连接两个选择器，表示选择第一个选择器元素的直接子元素，如`div > p{color:red;}`
- 相邻选择器：用 + 符号连接两个选择器，表示选择第一个选择器元素的相邻的同级元素，如`div + p{color:red;}`
- 毗邻选择器：用 ~ 符号连接两个选择器，表示选择第一个选择器元素的同级元素，如`div ~ p{color:red;}`
- 属性选择器：以 `[属性名]` 或 `[属性名=属性值]` 形式表示，表示选择所有带有指定属性的元素，如`a[href="https://www.baidu.com"]{color:red;}`

#### 伪类选择器

- `:link`：选择所有未被访问的链接。
- `:visited`：选择所有已被访问的链接。
- `:active`：选择正在被用户激活的元素。
- `:hover`：选择鼠标悬停在元素上的元素。
- `:focus`：选择获得焦点的元素。
- `:first-child`：选择父元素的第一个子元素。
- `:last-child`：选择父元素的最后一个子元素。
- `:nth-child(n)`：选择父元素的第 n 个子元素。n 可以是数字、关键字或公式。如 `:nth-child(2n)` 表示选择父元素的偶数个子元素。
- `:nth-last-child(n)`：选择父元素的倒数第 n 个子元素。
- `:first-of-type`：选择父元素的第一个指定类型的子元素。
- `:last-of-type`：选择父元素的最后一个指定类型的子元素。
- `:nth-of-type(n)`：选择父元素的第 n 个指定类型的子元素。
- `:checked`：选择被选中的 checkbox 或 radio 元素。
- `:enabled`：选择所有启用的表单元素。
- `:disabled`：选择所有禁用的表单元素。
- `:empty`：选择没有子元素的元素。

#### 伪元素选择器

- `::before`：在元素内容前插入内容。
- `::after`：在元素内容后插入内容。
- `::first-line`：选择元素的第一行。
- `::first-letter`：选择元素的第一个字母。
- `::selection`：选择用户选取的元素内容。
- `::placeholder`：选择输入框的占位文本样式。

#### 否定伪类选择器

- `:not(选择器)`：选择不匹配指定选择器的元素。

#### 选择器优先级

```
!important（无穷大）> 行内样式（1000）> id选择器（100）> 类选择器（10）> 标签选择器（1）> 通配符选择器（0）> 默认样式 > 继承样式
```

## 盒子模型

### 内容区域

- `width`：内容区域的宽度。
- `height`：内容区域的高度。

### 边框

- `border-width`：边框的宽度。
- `border-style`：边框的样式。
- `border-color`：边框的颜色。
- `border-top`：简写属性，用于设置上边框的宽度、样式和颜色。
- `border-right`：简写属性，用于设置右边框的宽度、样式和颜色。
- `border-bottom`：简写属性，用于设置下边框的宽度、样式和颜色。
- `border-left`：简写属性，用于设置左边框的宽度、样式和颜色。
- `border`：简写属性，用于同时设置边框的宽度、样式和颜色。

#### 边框圆角

`border-radius`：边框的圆角半径。

- 一个值: 四个角都设置相同的圆角半径。
- 两个值: 第一个值设置上左，下右的圆角半径，第二个值设置上右，下左的圆角半径。
- 三个值: 第一个值设置上左的圆角半径，第二个值设置上右、下左的圆角半径，第三个值设置下右的圆角半径。
- 四个值: 分别设置上左、上右、下右、下左的圆角半径。

#### 外间距

- `margin-top`：上外间距。
- `margin-right`：右外间距。
- `margin-bottom`：下外间距。
- `margin-left`：左外间距。
- `margin`：外间距。
  - 一个值：四个方向都设置相同的外间距。
  - 两个值：第一个值设置上下外间距，第二个值设置左右外间距
  - 三个值：第一个值设置上外间距，第二个值设置左右外间距，第三个值设置下外间距。
  - 四个值：分别设置上外间距、右外间距、下外间距、左外间距。

##### margin:0 auto => 有宽度的盒子可居中对齐

#### 内边距

- `padding-top`：上内间距。
- `padding-right`：右内间距。
- `padding-bottom`：下内间距。
- `padding-left`：左内间距。
- `padding`：内间距。
  - 一个值：四个方向都设置相同的内间距。
  - 两个值：第一个值设置上内间距，第二个值设置左右内间距。
  - 三个值：第一个值设置上内间距，第二个值设置左右内间距，第三个值设置下内间距。
  - 四个值：分别设置上内间距、右内间距、下内间距、左内间距。

### 其他

1. margin 和 padding 都能取值为 0, 取值为正数, 但是 padding 不能取值为负数, 没有意义, margin 取值可以为负数 ; 取值为负数用来调整元素位置
2. 注意：padding 和 margin 设置百分比值，百分比是根据父元素的宽度计算的
3. 标准盒子模型

- （宽度高度计算=width/height(内容)+内边距+边框+外间距）
- 内边距与边框会撑大盒子大小
- box-sizing:content-box;

4. 怪异盒子模型

- （宽度高度计算=width/height(内容+内边距+边框)+外间距）
- 内边距与边框不会撑大盒子大小
- box-sizing:border-box;

## 浮动

想让块级元素在同一行显示，可以添加浮动
当一个块级元素添加了浮动属性，那么该元素就会脱离标准文档流，并向右或者向左浮动，直至碰到另一个浮动元素或者碰到父元素边缘

### 浮动的问题

1. 假设有两个块级元素，如果只给前面的块级元素添加浮动，那么后面的块级元素就会顶上前去，两个块级元素在同一行显示；如果只给后面的块级元素添加浮动，那么后面的块级元素不会顶上去，两个块级元素还是分两行显示。
2. 为块级元素添加浮动会让该元素脱离文档流，但是不会脱离文本流，即一个元素内不会出现其他元素的文本内容。如果两个元素宽高相等，前一个浮动起来，后面的没有浮动，则后面的元素会被前面的元素覆盖住，那么后面元素内的文本会在原来的位置显示，如果后面元素宽高比前面的大，则会在没被覆盖住的位置显示文本。如果两个元素都浮动起来，则文本会跟随着一起浮动。
3. 一旦给一个行内元素添加浮动，那么该元素会变成块级元素，可以设置宽高。
4. 两个元素同时设置浮动，如果两个元素的宽度的和大于页面宽度则会出现折行显示。
5. 浮动会让一个盒子的默认宽度 100%，变得跟内容宽度一样,如果设置了固定宽度则不会改变

### 解决浮动后，后面的元素会顶上去的方法

1. 用一个父元素装着浮动的盒子，并为父元素设置固定宽高
2. 用一个父元素装着浮动的盒子，并为父元素设置 overflow:auto 属性
3. 在浮动元素后面加一个元素，并为该元素设置属性 clear:both;
4. 万能清除浮动法：

```css
/* ::after伪元素在某个元素内部的尾部设置信息 */
/* 万能清除浮动法 */
.clear::after {
  display: block;
  content: '';
  clear: both;
  /* 清除浮动的影响 */
  /* 相当于在使用该类的元素尾部添加一个content内容，然后将该内容转换为块级元素，并清除该元素周围的浮动 */
  /* clear的属性值为both是不允许左右有浮动，left是不允许有左浮动，right是不允许有右浮动 */
}
```

## 元素类型

### 常用取值

- `display:none;`：隐藏元素，不占据空间。
- `display:block;`：块级元素，占据一整行，可以设置宽高。
- `display:inline;`：行内元素，不占据一整行，可以设置宽高。
- `display:inline-block;`：行内块元素，可以设置宽高。
- `display:flex;`：弹性盒子，可以设置宽高。
- `display:grid;`：网格元素，可以设置宽高。

### 不常用取值

- `display:table;`：表格元素，可以设置宽高。
- `display:table-cell;`：单元格元素，可以设置宽高。
- `display:table-row;`：行元素，可以设置宽高。
- `display:list-item;`：列表元素，可以设置宽高。

## 溢出

1. `overflow:visible;`：内容溢出显示，超出部分显示。
2. `overflow:hidden;`：内容溢出隐藏，超出部分隐藏。
3. `overflow:scroll;`：内容溢出显示滚动条，可以滚动，两个方向都出现滚动条。
4. `overflow:auto;`：内容溢出显示滚动条，可以自动滚动，超出方向会出现滚动条。

- overflow-x: hidden; 仅隐藏水平方向的溢出
- overflow-y: hidden; 仅隐藏垂直方向的溢出

……

### 文本溢出

#### 单行文本溢出显示省略号（scss 代码，自行增删）

```scss
@mixin danHangSl($width: 0, $line-height: 0) {
  // 需要设置宽度
  width: $width;
  // 设置行高
  line-height: $line-height;
  // 强制不换行
  white-space: nowrap;
  // 溢出隐藏
  overflow: hidden;
  // 加省略号
  text-overflow: ellipsis;
}
```

#### 多行文本溢出显示省略号（scss 代码，自行增删）

```scss
@mixin duoHangSL($width: 0, $hangShu: 1) {
  // 设置宽度，不能设置高度，否则会失效
  width: $width;
  // 溢出隐藏
  overflow: hidden;
  // 将元素设置为WebKit内核的弹性盒子模型
  display: -webkit-box;
  // 限制显示的文本行数,设置标准属性实现兼容性
  line-clamp: $hangShu;
  // 限制显示的文本行数
  -webkit-line-clamp: $hangShu;
  // 设置弹性盒子的排列方向为垂直
  -webkit-box-orient: vertical;
}
```

## 定位

position:
添加定位属性后还需要添加方向关键词调整位置
top:距离；left：距离；bottom：距离；right：距离；

### 静态定位

`position:static;`
默认值，没有定位，元素出现在正常的流中，忽略 top,bottom,left,right 的设置。

### 相对定位

`position:relative;`

- 相对定位是相对于自身原先的位置进行定位
- 适用于几个或十几个像素的微调场景
- 相对定位不会脱离文档流，容器位置发生偏移后，原来占据的空间依然保留。
- 相对定位相对的是自身的屏幕坐标 0，0 点。

### 绝对定位

`position:absolute;`

- 绝对定位,元素相对于最近的已定位的祖先元素进行定位
- 绝对定位脱离文档流，元素位置发生偏移后，原来占据的空间会被其他元素挤占。
- 绝对定位，如果有已定位的祖先元素，则会相对于最近的祖先元素进行定位，如果没有已定位的祖先元素，则相对于 body 进行定位。
- 绝对定位相对的是祖先元素或者 body 的屏幕坐标 0，0 点

### 固定定位

`position:fixed;`

- 固定定位,元素相对于浏览器窗口可视区域进行定位
- 固定定位脱离文档流，元素位置发生偏移后，原来占据的空间会被其他元素挤占。
- 固定定位相对的是浏览器窗口的屏幕坐标 0，0 点。（即使页面滚动，也不会改变位置）

### 粘性定位

`position:sticky;`

- 粘性定位 , 是相对定位和固定定位的结合体 , 通过滚动浏览器窗口判断元素是否达到对应的窗口距离; 如果达到进行吸附展示, 如果没有达到则恢复默认样式 , 或者是继续滚动;
- 父元素不能添加 overflow:hidden 属性或者 overflow:auto 属性，否则会失效。
- 父元素的高度不能低于 sticky 定位元素的高度，否则会失效。
- sticky 定位的元素仅在其父元素内生效。
- 粘性定位初始状态相当于 static 定位。
- 当元素相对于父容器的定位条件符合时，容器会变成固定定位。

### 定位的层级调整

- 如果两个元素都添加了定位，那么默认后面的元素会盖住前面的元素，可以通过调整层级来解决
- 层级调整：z-index 属性，数值越大，元素越靠前，只有添加了定位属性才能生效

### 遇到的问题

- 已经脱离文档流的元素添加的 margin:0 不会 auto；属性不会生效
- 绝对定位和固定定位会影响自适应（width:auto；）的盒子，宽度会消失，因为没有设置固定宽度，此时的宽度是被元素内内容撑开的

## 字体文本相关

- `font-family`：字体系列，可以设置多个，浏览器会按照顺序查找，第一个找到的字体就使用，如果都不支持，则会使用默认字体：微软雅黑
- `font-size`：字体大小，可以设置绝对值，如 12px，也可以设置相对值，如 1.2em，还可以设置百分比，如 100%。
- `font-style`：字体风格，italic/oblique 字体倾斜，normal 正常字体
- `font-weight`：字体粗细， bold/bolder 加粗，lighter 变细，normal 正常字体
- `text-decoration:线的位置 线的样式 线的颜色`
  - `text-decoration:none;`：取消文本装饰
  - underline;下划线 line-through;中划线 overline;上划线; none;取消修饰线
  - solid;单实线 double;双实线 dashed;线段状虚线 dotted;点状虚线 wavy;波浪线
  - 颜色取值
- `text-transform`：文本转换，capitalize 首字母大写，uppercase 全部大写，lowercase 全部小写，none 无转换
- `line-height`：行高，可以设置绝对值，如 12px，也可以设置相对值，如 1.2em，还可以设置百分比，如 100%，设置行高与容器高度相同可实现单行文本居中
- `letter-spacing`：字母间距，可以设置负值，让字母更紧凑，可以设置正值，让字母更松散。
- `word-spacing`：单词间距，可以设置负值，让单词更紧凑，可以设置正值，让单词更松散。
- `text-align`：文本对齐，left 左对齐，right 右对齐，center 居中对齐，justify 两端对齐。
- `text-indent`：首行缩进，可以设置百分比，如 10%。
- `color`: 文本颜色，可以设置颜色值，也可以设置 transparent 透明。

复合属性： `font : weight  style  size/line-height  family`：一次性设置字体系列、大小、风格、粗细、变体。

## 背景相关

- `background-color`：背景颜色，可以设置颜色值，也可以设置 transparent 透明。
- `background-image`：背景图片，可以设置 url 路径，还可以设置 none 无背景图片。
- `background-repeat`：背景图片重复，repeat 平铺，repeat-x 水平平铺，repeat-y 垂直平铺，no-repeat 不平铺。
- `background-position:水平方向 垂直方向`：可以用来调整背景图片的显示位置，可取值或关键词，水平方向关键词:left/right/center; 垂直方向关键词:top/bottom/center
- `background-size`
  - `auto`：默认值，背景图片的原始大小
  - 取值，两个值，第一个值是宽度，第二个值是高度
  - `cover`： 等比例放大这个背景图, 然后直到铺满这个容器为止; 背景图片会显示不全
  - `contain`：等比例缩放这个背景图, 然后只要横向或者是纵向有一个边铺满容器就可以; 背景图片会铺不满容器
- background-attachment：背景固定滚动
- `scroll`：背景滚动; 当浏览器滚动的时候,背景图片会随着浏览器滚动条滚动上去;
- `fixed`：当浏览器滚动的时候不会影响背景图片, 背景图片会固定再某一个位置

背景复合属性
background ：color image repeat position/size attachment

1. 复合属性后面可以跟一个值,也可以跟多个值, 多个值使用空格隔开, 多个取值的时候取值先后顺序可以交换
2. 复合属性使用位置和大小的时候, 必须按照顺序书写, 不能拆开书写
3. /前面代表的是位置; /后面代表的是大小
4. 如果只要位置, /以及后面的大小可以不用跟;
5. 如果只要大小, /前面的位置必须要书写, 就算不调整位置也要写 0px 0px

## 弹性盒子

- `display:flex;`：触发弹性盒子，可以设置宽高。

### flex-direction：修改主轴方向，主轴方向会影响项目的排列方向

- （默认值）row：设置主轴为横向，并从左往右
- row-reverse：设置主轴为横向，并从右往左
- column：设置主轴为纵向，并从上往下
- column-reverse：设置主轴为纵向，并从下往上

### justify-content：修改主轴方向上的对齐方式，不会改变项目排列顺序

- flex-start：项目位于主轴开始位置，项目间没有间距
- flex-end：项目位于主轴结束位置，项目间没有间距
- center：在主轴的居中位置排列，项目间没有间距
- space-around：项目有左右间距，项目与项目间间距叠加
- space-between：项目与项目之间的间距相等，项目与父元素的边没有间距（贴边）
- space-evenly：项目与项目，项目与父元素的边的间距相等

### align-items：修改侧轴上的对齐方式，侧轴与主轴垂直

- flex-start：侧轴的开始位置显示
- flex-end：侧轴的结束位置显示
- center：侧轴的居中位置
- stretch：侧轴拉伸显示; 主轴在横向, 如果项目不设置高度默认高度已经撑满容器; 主轴在纵向,如果不设置宽度可以撑满容器宽度

### flex-wrap：设置项目排列是否折行

- nowrap：默认值，不折行
- wrap：折行，项目会换行排列
- wrap-reverse：折行，项目会换行排列，但是方向相反

### align-content：设置折行后的行间距

- flex-start：项目位于侧轴开始位置，项目间没有间距
- flex-end：项目位于侧轴结束位置，项目间没有间距
- center：在侧轴的居中位置排列，项目间没有间距
- space-around：项目有上下间距，项目与项目间间距叠加
- space-between：项目与项目之间的间距相等，项目与父元素的边没有间距（贴边）
- space-evenly：项目与项目，项目与父元素的边的间距相等

### 项目的样式属性

- order : number
  - 调整项目的排列顺序，默认主轴为从左往右时，数值小的会往前排，数值越大越靠后
- flex: number
  - 占剩余宽度或剩余高度，主轴在横向, 实现的是占剩余宽度的所有; 主轴在纵向, 实现的是占剩余高度的所有
  - 如果给一行内所有元素都设置 flex：1，则会让所有元素均分宽度
  - 给多个项目不同数值，数值越大占的越多
- flex-shrink：不挤压不折行
  - 0 : 代表的是不挤压, 不折行
  - 1 : 代表的是挤压, 不折行（默认值）
- align-self：调整单个项目的对齐方式（应用于侧轴）
  - flex-start：侧轴开始位置
  - flex-end：侧轴结束位置
  - center：侧轴居中位置
  - stretch：侧轴拉伸显示
  - auto：默认值，继承父元素的 align-items 属性

### 注意事项

- 切记添加属性应该添加到父元素上面
- 触发弹性盒子之后 , 所有子元素默认横向显示
- 触发弹性盒子之后 , 如果子元素原来是行内元素, 此时行内元素会变成块级元素
- 触发弹性盒子之后 , 如果只有一个子元素, 给子元素添加 margin:auto; 会让子元素实现水平垂直正居中
- 触发弹性盒子之后 , 只会影响子元素的排列方式, 不会影响孙子辈的排列方向

## 过度与动画

### 过渡效果 transition

transition：all 3s linear 5s;(复合属性)

- all : 代表的是所有能参与过度动画的属性 ;
  - 此处可以写单个属性，如 transition：border 3s linear 5s;
  - 也可以写多个属性，如 transition: border 3s linear 5s, color 3s linear 5s;
- 3s : 代表的是过度动画执行需要花费的时间 ;
- linear : 代表的是过度动画的动画类型;
  - ease : 动画开始和结束的速率是相同的
  - ease-in : 由慢到快
  - ease-out : 由快到慢
  - ease-in-out : 由慢到快再到慢
  - linear : 线性过渡 , 匀速直线运动
  - steps() : 按照步骤执行
  - 贝塞尔曲线 : cubic-bezier(, , , )
- 5s : 代表的是过渡到动画的延迟执行;

#### 单个属性

1. transition-property : 所有能参与过度的动画属性; 如果使用单一属性的话, 后面多个属性之间使用逗号隔开, 但是多个属性可以直接使用 all 代替; 需要注意的是 : display, visibility , 渐变不参与过度 ;
2. transition-duration : 过度动画需要花费的时间 ;
3. transition-timing-function : 过度动画执行的类型
4. transition-delay: 过度动画的延迟执行 ;

### 平面动态效果 transform

只添加动态效果不会有过渡效果，想要有过渡效果需要在初始状态中加过渡效果

transform 后面可以跟多个动态效果

```css
div {
  transform: translate(x, y) rotate(deg) scale(x, y) skew(x-deg, y-deg);
}
```

#### 平移 translate

```css
div {
  /* 可以设置两个方向的值，如果括号内只有一个值，则会当作在X轴上平移 */
  transform: translate(x, y);
  /* 在X轴上平移 */
  transform: translateX(x);
  /* 在Y轴上平移 */
  transform: translateY(y);
}
```

#### 缩放 scale

```css
div {
  /* 取值为两个值的时候，第一个代表水平方向，第二个垂直方向 */
  transform: scale(x, y);
  /* 取值为一个值的时候，代表XY两个方向 */
  transform: scale(x);
  /* 在X轴上缩放 */
  transform: scaleX(x);
  /* 在Y轴上缩放 */
  transform: scaleY(y);
}
```

#### 旋转 rotate

```css
div {
  /* 围绕元素的中心点旋转 */
  transform: rotate(deg);
  /* 在X轴上旋转 */
  transform: rotateX(deg);
  /* 在Y轴上旋转 */
  transform: rotateY(deg);
  /* 在Z轴上旋转 */
  transform: rotateZ(deg);
}
```

#### 倾斜 skew

```css
div {
  /* 沿着x轴和y轴进行倾斜, 参数1和参数2代表的是对应倾斜度数
取一个值只会沿着x轴倾斜 */
  transform: skew(x-deg, y-deg);
  /* 在X轴上倾斜 */
  transform: skewX(x-deg);
  /* 在Y轴上倾斜 */
  transform: skewY(y-deg);
}
```

#### 设置元素中心点位置

transform-origin : x y；

- 元素默认中心点位置为元素的最中心位置
- x 和 y 的取值可以为数值，元素左上角为坐标起点 0.0
- transform-origin：后面也可以写关键词
  - 一个关键词
    - top，会选上边的中点为中心点
    - bottom，会选下边的中点为中心点
    - left，会选左边的中点为中心点
    - right，会选右边的中点为中心点
  - 两个关键词（关键词的顺序没有影响）
    - top left，左上角作为中心点
    - top right，右上角作为中心点
    - bottom left，左下角作为中心点
    - bottom right，右下角作为中心点
  - 三个及以上的关键词以及 center 都会选元素正中心作为中心点

### 关键帧动画

#### 声明动画

```css
@keyframes mymove{
from{初始状态}
to{结束状态}
}
/* 设置多个关键帧 */
@keyframes mymove{
0%{初始状态}
50%{中间状态}
100%{结束状态}
}

```

#### 使用动画

```css
/* 给元素添加动画 */
div {
  animation: name（名字） 5s（执行时长） linear（执行类型） 3s（延时） infinite（执行次数） alternate（是否反向执行） forwards（停留在最后一帧） running（播放状态）;
  /* （复合属性，不用的属性可以省略，但是顺序不能乱） */
}
```

- name : 调用的动画的名字
- 5s : 执行动画所用的时间（动画的时长）
- linear : 动画执行的类型
  - ease : 平滑过渡（默认值）
  - ease-in : 由慢到快
  - ease-out : 由快到慢
  - ease-in-out : 由慢到快再到慢
  - linear : 线性过渡 , 匀速直线运动
  - cubic-bezier(n,n,n,n)：定义自己的贝塞尔曲线
  - step-start：动画立即跳到最终的状态
  - step-end：动画保持在初始状态，动画结束立即跳到最终状态
  - steps(n, start|end)：将两个关键帧之间的动画分为 n 个步骤，步骤之间的间隔固定，选择 start，动画立即跳到终点，选择 end，动画会保持在初始状态，在动画结束的时候跳到终点位置
- 3s : 动画延迟执行
- infinite : 动画制定的次数, 可以为数值, 数值代表执行多少次, infinite 代表的无穷尽的一直执行
- alternate:动画先正常运行再反方向运行，并持续交替运行，直到动画结束这个位置设置动画在循环中是否反向运动
  - normal：正常方向
  - reverse：反方向运行
  - alternate：动画先正常运行再反方向运行，并持续交替运行
  - alternate-reverse：动画先反运行再正方向运行，并持续交替运行
- forwards：播放结束后停留在最后一帧
  - none（默认值），动画播放结束后不影响元素的样式
  - forwards：动画播放结束后保留动画最后一帧的样式
  - backwards：动画开始前元素将应用动画第一帧的样式，直到动画开始播放
  - both：动画开始前应用第一帧的样式，结束后保留最后一帧的样式
- running：设置为播放状态
  - running:运动
  - paused：暂停

##### 单个属性调用动画

```css
div {
  /* animation-name  检索或设置对象所应用的动画名称 */
  /* animation-duration  检索或设置对象动画的持续时间 */
  /* animation-timing-function  检索或设置对象动画的过渡类型 */
  /* animation-delay  检索或设置对象动画延迟的时间 */
  /* animation-iteration-count  检索或设置对象动画的循环次数 */
  /* animation-direction  检索或设置对象动画在循环中是否反向运动 */
  /* animation-play-state  检索或设置对象动画的状态 */
}
```

## 媒体查询

媒体查询可以针对不同的屏幕尺寸、设备类型、打印机等进行不同的样式设置，可以让网页更加适应多种设备。

```css
@media screen and (min-width: 768px) and (max-width: 1024px) {
  /* 针对屏幕尺寸为768px-1024px的设备 */
  /* 这里可以写样式 */
}

@media print {
  /* 针对打印机 */
  /* 这里可以写样式 */
}
```

## 其他

### 隐藏元素的方法

```css
div {
  display: none;
  /* 隐藏元素不占页面空间 */
  visibility: hidden;
  /* 隐藏元素占页面空间 */
  width: 0px;
  height: 0px;
  /* 隐藏元素,不占页面空间 */
  opacity: 0;
  /* 隐藏元素,占页面空间 */
  transform: scale(0);
  /* 隐藏元素; 占页面空间 */
}
```

### 列表相关属性

```css
ul {
  list-style-type: disc; /* 列表项目符号类型 */
  list-style-image: url(); /* 列表项目符号图片 */
  list-style-position: inside; /* 列表项目符号位置 */
  /* 符合属性 */
  list-style: disc inside url(); /* 列表项目符号类型、位置、图片 */
}
```

### 渐变色

```css
/* 线性渐变 */
div {
  /* background:linear-gradient(角度，颜色1 起始位置，颜色2 起始位置，颜色3 起始位置···) */
  /* 线性渐变（角度可以控制渐变的方向） */
  /* 默认从上到下（相当于180deg），加to left 从右到左，其他同理 */
  /* 设置颜色起始点可以用百分比或者数值 */
}
/* 径向渐变 */
div {
  /* background:radial-gradient(形状 大小 at 位置，颜色1 起始位置，颜色2 起始位置，颜色3 起始位置...) */
  /* 形状：circle圆形渐变、ellipse椭圆形渐变（默认值） */
  /* 大小
  closest-side：渐变的边缘与容器最近的边相切。
  closest-corner：渐变的边缘与容器最近的角相切。
  farthest-side：渐变的边缘与容器最远的边相切。
  farthest-corner：渐变的边缘与容器最远的角相切（默认值）。
  具体尺寸值：如 50% 50% 或 100px 200px。
  */
  /* 位置：
  关键字：center（默认值）, top, left, right, bottom。
  具体坐标值：如 50% 50% 或 100px 200px
  */
  /* 起始位置：设置颜色起始点可以用百分比或者数值 */
}
```

### 字体渐变色

```css
/* 渐变体现在文字上，注意文字父元素宽度为渐变的范围 */
.gradient-text {
  background: -webkit-linear-gradient(left, #5ff4ff, #ff6f00);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}
```

### 使用自己下载的字体

1. 下载字体文件并放入项目目录。
2. 使用`@font-face`定义字体。
3. 在 CSS 中通过`font-family`引用字体。
4. 确保字体文件路径正确，并提供多种字体格式以兼容不同浏览器

，通常字体文件格式包括：

- `.ttf` (TrueType Font)
- `.otf` (OpenType Font)
- `.woff` (Web Open Font Format)
- `.woff2` (Web Open Font Format 2)

```css
/* 在CSS文件中，使用`@font-face`规则来定义字体 */
@font-face {
  font-family: 'YourFontName'; /* 自定义字体名称 */
  src: url('../fonts/your-font.woff2') format('woff2'), /* 字体文件路径 */ url('../fonts/your-font.woff') format('woff'); /* 备选字体文件 */
  font-weight: normal; /* 字体粗细 */
  font-style: normal; /* 字体样式 */
}
```

```css
/* 为了兼容不同的浏览器，建议提供多种字体格式 */
@font-face {
  font-family: 'YourFontName';
  src: url('../fonts/your-font.woff2') format('woff2'), /* 现代浏览器优先 */ url('../fonts/your-font.woff') format('woff'), /* 兼容性更好 */ url('../fonts/your-font.ttf') format('truetype'); /* 旧版浏览器 */
  font-weight: normal;
  font-style: normal;
}
```

# 软件开发专业术语

## 1. 内存管理相关

### GC (Garbage Collection) - 垃圾回收

GC 是一种自动管理内存的机制，用于自动清除不再被引用的对象，从而释放内存空间。主要应用于 Java、C#和 Python 等高层次语言。

#### 主要目标

1. 释放未被使用的内存
2. 减少内存泄漏
3. 提高程序的稳定性

#### JavaScript 垃圾回收机制

JavaScript 使用两种主要算法进行内存管理：

1. **标记-清除（Mark-and-Sweep）算法**

   - 定期扫描所有对象
   - 标记可达（被引用）对象
   - 清除未被标记的对象
   - 分为标记阶段和清除阶段

2. **引用计数（Reference Counting）算法**
   - 为每个对象维护引用计数器
   - 当计数为 0 时进行回收
   - 存在循环引用问题

#### 分代垃圾回收策略

- **年轻代**：新创建对象，频繁回收
- **老年代**：长期存活对象，低频回收

## 2. 开发工具

### IDE (Integrated Development Environment) - 集成开发环境

IDE 是为程序员提供的综合开发工具，包含代码编辑器、编译器/解释器、调试器等。

#### 常见 IDE

1. Eclipse - Java 开发
2. Visual Studio - Windows 应用开发
3. PyCharm - Python 开发
4. IntelliJ IDEA - Java 等多语言开发
5. Xcode - iOS 和 macOS 开发

## 3. 系统分类

### 管理系统类型

1. **toB（面向企业）系统**

   - OA（办公自动化系统）
   - CRM（客户关系管理系统）
   - ERP（企业资源管理系统）
   - SCM（供应链管理系统）
   - HIS（医院相关系统）
   - BIM（生产管理系统）
   - GIS（地理信息化系统）

2. **toC（面向消费者）系统**
   - 各类 App 应用

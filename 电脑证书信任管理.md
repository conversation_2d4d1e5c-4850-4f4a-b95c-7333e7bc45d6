# 手动管理电脑信任证书库的完整指南

## 操作系统证书管理工具

### Windows 系统管理

1. **打开证书管理器**：

   - 按 `Win + R` 输入 `certmgr.msc` (当前用户证书) 或 `certlm.msc` (本地计算机证书)
   - 或使用 PowerShell：`Get-ChildItem -Path cert:\LocalMachine\Root`

2. **证书库结构**：

   ```mermaid
   graph LR
   A[证书管理器] --> B[受信任的根证书颁发机构]
   A --> C[中间证书颁发机构]
   A --> D[不信任的证书]
   A --> E[个人证书]
   ```

3. **关键操作**：
   - **添加证书**：右键"受信任的根证书颁发机构" → 所有任务 → 导入
   - **删除证书**：右键证书 → 删除
   - **导出证书**：右键证书 → 所有任务 → 导出
   - **查看属性**：双击证书 → 详细信息

### macOS 系统管理

1. **钥匙串访问工具**：

   - 打开"应用程序" → "实用工具" → "钥匙串访问"
   - 或使用终端：`security find-identity -p basic -v`

2. **钥匙串分类**：

   - **系统钥匙串**：影响所有用户（需要管理员权限）
   - **登录钥匙串**：仅影响当前用户
   - **系统根证书**：`/System/Library/Keychains/SystemRootCertificates.keychain`

3. **关键操作**：
   - 添加证书：拖放.crt 文件到钥匙串窗口
   - 修改信任：双击证书 → 展开"信任" → 设置"使用此证书时"
   - 删除证书：右键 → 删除"证书名称"

## 命令行管理工具

### Windows (PowerShell)

```powershell
# 列出所有受信任的根证书
Get-ChildItem -Path Cert:\LocalMachine\Root | Format-List Subject, Thumbprint

# 导入新证书
Import-Certificate -FilePath "C:\ssl\mycert.crt" -CertStoreLocation Cert:\LocalMachine\Root

# 删除证书
$thumbprint = "A1B2C3D4E5F6..." # 替换为实际指纹
Remove-Item -Path "Cert:\LocalMachine\Root\$thumbprint"
```

### macOS/Linux (Terminal)

```bash
# 查看信任库
security dump-trust-settings

# 添加证书到系统钥匙串
sudo security add-trusted-cert -d -r trustRoot -k /Library/Keychains/System.keychain ~/ssl/mycert.crt

# 删除证书
sudo security delete-certificate -c "My Certificate Name" /Library/Keychains/System.keychain

# 查看证书详情
openssl x509 -in mycert.crt -text -noout
```

## 最佳管理实践

### 1. 证书库存管理

创建证书登记表：

```markdown
| 证书名称     | 用途     | 颁发者      | 有效期至   | 指纹(SHA-256) | 存储位置     |
| ------------ | -------- | ----------- | ---------- | ------------- | ------------ |
| Dev API Cert | 本地开发 | Self-Signed | 2025-12-31 | A1:B2:C3:...  | LocalMachine |
| Company Root | 内部应用 | MyOrg CA    | 2030-01-01 | D4:E5:F6:...  | CurrentUser  |
```

### 2. 定期审查计划

- 每月检查：`expiry_date > now() + 30 days`
- 每季度全面审计
- 证书更新流程：
  ```mermaid
  graph TD
    A[检查到期证书] --> B[生成新证书]
    B --> C[部署到测试环境]
    C --> D[验证兼容性]
    D --> E[部署到生产]
    E --> F[撤销旧证书]
  ```

### 3. 安全策略配置

- **信任范围控制**：
  ```bash
  # macOS 限制证书使用范围
  sudo security set-trust-settings -d -t ssl -p basic ~/ssl/mycert.crt
  ```
- **证书钉扎** (高级安全)：
  ```javascript
  // 在应用代码中固定证书指纹
  const tls = require('tls')
  tls.checkServerIdentity = (host, cert) => {
    const expected = 'A1:B2:C3:...'
    if (cert.fingerprint256 !== expected) {
      throw new Error('Certificate pinning failure')
    }
  }
  ```

### 4. 备份与恢复

**备份证书库**：

```powershell
# Windows 导出所有证书
Backup-Certificate -FilePath C:\backup\allcerts.pfx -Password (ConvertTo-SecureString -String "BackupPass" -Force) -CertStoreLocation Cert:\LocalMachine\Root
```

**恢复证书库**：

```bash
# macOS 从备份恢复
sudo security import backup.keychain -k /Library/Keychains/System.keychain -t cert
```

## 开发者专用管理技巧

### 1. 创建专用开发证书库

```bash
# 创建独立钥匙串 (macOS)
security create-keychain -p devpass dev-certs.keychain

# 设置默认钥匙串
security default-keychain -s dev-certs.keychain

# 添加证书
security import dev-cert.crt -k dev-certs.keychain -t cert
```

### 2. 自动化管理脚本

```bash
#!/bin/bash
# 证书管理助手脚本

ACTION=$1
CERT=$2

case $ACTION in
  list)
    security find-certificate -a -p /Library/Keychains/System.keychain | openssl x509 -text | grep "Subject:"
    ;;
  add)
    sudo security add-trusted-cert -d -r trustRoot -k /Library/Keychains/System.keychain $CERT
    ;;
  remove)
    sudo security delete-certificate -c "$CERT" /Library/Keychains/System.keychain
    ;;
  verify)
    openssl verify -CApath /etc/ssl/certs $CERT
    ;;
  *)
    echo "Usage: certman [list|add|remove|verify] [cert-file]"
    ;;
esac
```

### 3. 浏览器特定管理

- **Chrome**：`chrome://settings/certificates`
- **Firefox**：`about:preferences#privacy` → 证书 → 查看证书

## 安全警告与注意事项

1. **永不信任的证书类型**：

   - 密钥长度 < 2048 位的证书
   - SHA-1 签名的证书
   - 来源不明的商业证书

2. **证书冲突解决**：

   ```bash
   # 查找重复证书
   security find-certificate -a -c "Common Name" | grep -A 10 "SHA-1"

   # 比较指纹后删除旧证书
   ```

3. **企业环境管理**：
   - 使用组策略(GPO)分发证书
   - 配置证书自动注册
   - 部署 SCEP(简单证书注册协议)服务器

## 推荐管理工具

| 工具名称              | 平台       | 特点               |
| --------------------- | ---------- | ------------------ |
| **KeyStore Explorer** | 跨平台     | 可视化 PKI 管理    |
| **OpenSSL**           | 跨平台     | 命令行标准工具     |
| **Microsoft PKI**     | Windows    | 企业级 CA 解决方案 |
| **cert-manager**      | Kubernetes | 云原生证书管理     |

> **最佳实践**：对于开发环境，建议创建专用的自签名 CA 证书，然后由其签发所有开发证书。这样只需信任 CA 证书一次，即可自动信任所有由其签发的证书：
>
> ```bash
> # 创建私有CA
> openssl req -x509 -newkey rsa:4096 -days 3650 -keyout ca.key -out ca.crt -subj "/CN=MyDevCA"
>
> # 用CA签发服务器证书
> openssl req -newkey rsa:2048 -nodes -keyout server.key -out server.csr -subj "/CN=dev.example.com"
> openssl x509 -req -in server.csr -CA ca.crt -CAkey ca.key -CAcreateserial -out server.crt -days 365
> ```

通过系统化的管理，你可以确保只信任必要的证书，同时保持开发和生产环境的安全性。定期审查(建议每季度)是维护健康证书生态系统的关键。

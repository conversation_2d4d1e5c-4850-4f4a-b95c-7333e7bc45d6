# HTML 超文本标记语言

## 目录

1. [基础概念](#基础概念)

   - [HTML 简介](#html-简介)
   - [文档结构](#文档结构)
   - [注释规范](#注释规范)

2. [文本标签](#文本标签)

   - [标题标签](#标题标签)
   - [段落标签](#段落标签)
   - [文本格式化](#文本格式化)
   - [特殊文本](#特殊文本)

3. [链接与图片](#链接与图片)

   - [链接标签](#链接标签)
   - [图片标签](#图片标签)
   - [路径说明](#路径说明)

4. [列表与表格](#列表与表格)

   - [列表标签](#列表标签)
   - [表格标签](#表格标签)

5. [表单元素](#表单元素)

   - [表单标签](#表单标签)
   - [输入控件](#输入控件)
   - [选择控件](#选择控件)
   - [文本域](#文本域)
   - [按钮](#按钮)

6. [多媒体元素](#多媒体元素)

   - [音频](#音频)
   - [视频](#视频)

7. [语义化标签](#语义化标签)

   - [页面结构](#页面结构)
   - [内容组织](#内容组织)

8. [特殊属性](#特殊属性)
   - [全局属性](#全局属性)
   - [事件属性](#事件属性)

## 基础概念

### HTML 简介

HTML (HyperText Markup Language) 是一种用于创建网页的标准标记语言。它描述了网页的结构，由一系列的元素组成，这些元素告诉浏览器如何展示内容。

### 文档结构

```html
<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0" />
    <title>页面标题</title>
  </head>
  <body>
    <!-- 页面内容 -->
  </body>
</html>
```

### 注释规范

```html
<!-- 单行注释 -->
<!--
    多行注释
    可以包含多行内容
-->
```

## 文本标签

### 标题标签

```html
<h1>一级标题</h1>
<h2>二级标题</h2>
<h3>三级标题</h3>
<h4>四级标题</h4>
<h5>五级标题</h5>
<h6>六级标题</h6>
```

### 段落标签

```html
<p>这是一个段落</p>
<br />
<!-- 换行 -->
<hr />
<!-- 分割线 -->
```

### 文本格式化

```html
<strong>加粗文本</strong>
<em>斜体文本</em>
<del>删除线文本</del>
<ins>下划线文本</ins>
<mark>高亮文本</mark>
```

### 特殊文本

```html
<sup>上标文本</sup>
<sub>下标文本</sub>
<code>代码文本</code>
<pre>预格式化文本</pre>
```

## 链接与图片

### 链接标签

```html
<a
  href="url"
  target="_blank"
  >链接文本</a
>
```

属性：

- href：链接地址
- target：打开方式
  - \_blank：新窗口
  - \_self：当前窗口
  - \_parent：父框架
  - \_top：顶层窗口
- download：下载链接
- rel：链接关系

### 图片标签

```html
<img
  src="url"
  alt="替代文本"
  width="宽度"
  height="高度" />
```

属性：

- src：图片地址
- alt：替代文本
- width：宽度
- height：高度
- loading：加载方式
  - lazy：懒加载
  - eager：立即加载

### 路径说明

1. 相对路径

   - 同级：`./file.html`
   - 上级：`../file.html`
   - 根目录：`/file.html`

2. 绝对路径
   - 完整 URL：`https://example.com/file.html`

## 列表与表格

### 列表标签

```html
<!-- 有序列表 -->
<ol>
  <li>第一项</li>
  <li>第二项</li>
</ol>

<!-- 无序列表 -->
<ul>
  <li>第一项</li>
  <li>第二项</li>
</ul>

<!-- 定义列表 -->
<dl>
  <dt>术语</dt>
  <dd>描述</dd>
</dl>
```

### 表格标签

```html
<table>
  <thead>
    <tr>
      <th>表头1</th>
      <th>表头2</th>
    </tr>
  </thead>
  <tbody>
    <tr>
      <td>单元格1</td>
      <td>单元格2</td>
    </tr>
  </tbody>
  <tfoot>
    <tr>
      <td>页脚1</td>
      <td>页脚2</td>
    </tr>
  </tfoot>
</table>
```

## 表单元素

### 表单标签

```html
<form
  action="url"
  method="post">
  <!-- 表单内容 -->
</form>
```

属性：

- action：提交地址
- method：提交方式
  - get：URL 参数
  - post：表单数据
- enctype：编码方式
  - application/x-www-form-urlencoded
  - multipart/form-data
  - text/plain

### 输入控件

```html
<input
  type="text"
  name="username"
  value="默认值" />
```

类型：

- text：文本输入
- password：密码输入
- number：数字输入
- email：邮箱输入
- tel：电话输入
- url：URL 输入
- date：日期选择
- time：时间选择
- datetime-local：日期时间
- month：月份选择
- week：周选择
- color：颜色选择
- file：文件上传
- hidden：隐藏字段
- radio：单选按钮
- checkbox：复选框
- submit：提交按钮
- reset：重置按钮
- button：普通按钮
- image：图片按钮

### 选择控件

```html
<select name="country">
  <option value="cn">中国</option>
  <option value="us">美国</option>
</select>
```

### 文本域

```html
<textarea
  name="message"
  rows="4"
  cols="50"></textarea>
```

### 按钮

```html
<button type="submit">提交</button>
<button type="reset">重置</button>
<button type="button">普通按钮</button>
```

## 多媒体元素

### 音频

```html
<audio controls>
  <source
    src="audio.mp3"
    type="audio/mpeg" />
  您的浏览器不支持音频播放。
</audio>
```

属性：

- controls：显示控件
- autoplay：自动播放
- loop：循环播放
- muted：静音
- preload：预加载

### 视频

```html
<video controls>
  <source
    src="video.mp4"
    type="video/mp4" />
  您的浏览器不支持视频播放。
</video>
```

属性：

- controls：显示控件
- autoplay：自动播放
- loop：循环播放
- muted：静音
- preload：预加载
- poster：封面图片
- width：宽度
- height：高度

## 语义化标签

### 页面结构

```html
<header>页头</header>
<nav>导航</nav>
<main>主要内容</main>
<aside>侧边栏</aside>
<footer>页脚</footer>
```

### 内容组织

```html
<article>文章</article>
<section>章节</section>
<figure>
  <img
    src="image.jpg"
    alt="图片" />
  <figcaption>图片说明</figcaption>
</figure>
<details>
  <summary>详细信息</summary>
  <p>详细内容</p>
</details>
```

## 特殊属性

### 全局属性

- class：类名
- id：唯一标识
- style：内联样式
- title：提示文本
- lang：语言
- dir：文本方向
- data-\*：自定义数据
- hidden：隐藏元素
- tabindex：Tab 顺序
- accesskey：快捷键

### 事件属性

- onclick：点击事件
- onload：加载事件
- onsubmit：提交事件
- onchange：改变事件
- onmouseover：鼠标移入
- onmouseout：鼠标移出
- onkeydown：按键按下
- onkeyup：按键释放

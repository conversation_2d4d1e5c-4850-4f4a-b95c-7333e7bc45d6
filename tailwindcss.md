# Tailwind CSS 速成指南

## 1. 类名速查表

### 1.1 布局类名

#### 容器

```html
<!-- 响应式容器 -->
<div class="container mx-auto">
  <div class="my-auto">垂直居中内容</div>
</div>
```

- `container`: 响应式容器
- `mx-auto`: 水平居中
- `my-auto`: 垂直居中

#### Flex 布局

```html
<!-- 基础 Flex 布局 -->
<div class="flex flex-row flex-wrap">
  <div class="flex-1">弹性增长</div>
  <div class="flex-auto">自动弹性</div>
  <div class="flex-none">不弹性</div>
  <div class="flex-initial">初始弹性</div>
</div>
```

- `flex`: 弹性布局
- `flex-row`: 水平排列
- `flex-col`: 垂直排列
- `flex-wrap`: 允许换行
- `flex-nowrap`: 不换行
- `flex-1`: 弹性增长
- `flex-auto`: 自动弹性
- `flex-none`: 不弹性
- `flex-initial`: 初始弹性

#### Flex 对齐

```html
<!-- Flex 对齐示例 -->
<div class="flex justify-between items-center">
  <div>左侧内容</div>
  <div>右侧内容</div>
</div>
```

- `justify-start`: 主轴起点对齐
- `justify-end`: 主轴终点对齐
- `justify-center`: 主轴居中对齐
- `justify-between`: 主轴两端对齐
- `justify-around`: 主轴环绕对齐
- `items-start`: 交叉轴起点对齐
- `items-end`: 交叉轴终点对齐
- `items-center`: 交叉轴居中对齐
- `items-baseline`: 交叉轴基线对齐
- `items-stretch`: 交叉轴拉伸对齐

#### Grid 布局

```html
<!-- Grid 布局示例 -->
<div class="grid grid-cols-3 gap-4">
  <div class="col-span-2">占据两列</div>
  <div>单列</div>
</div>
```

- `grid`: 网格布局
- `grid-cols-{n}`: 列数
- `grid-rows-{n}`: 行数
- `grid-flow-row`: 行优先
- `grid-flow-col`: 列优先
- `gap-{size}`: 网格间距
- `col-span-{n}`: 跨越列数
- `row-span-{n}`: 跨越行数

### 1.2 间距类名

#### Padding

```html
<!-- Padding 示例 -->
<div class="p-4">四周内边距</div>
<div class="px-4 py-2">水平垂直内边距</div>
<div class="pt-4">上内边距</div>
```

- `p-{size}`: 所有方向内边距
- `px-{size}`: 水平内边距
- `py-{size}`: 垂直内边距
- `pt-{size}`: 上内边距
- `pr-{size}`: 右内边距
- `pb-{size}`: 下内边距
- `pl-{size}`: 左内边距

#### Margin

```html
<!-- Margin 示例 -->
<div class="m-4">四周外边距</div>
<div class="mx-auto">水平居中</div>
<div class="mt-4">上外边距</div>
```

- `m-{size}`: 所有方向外边距
- `mx-{size}`: 水平外边距
- `my-{size}`: 垂直外边距
- `mt-{size}`: 上外边距
- `mr-{size}`: 右外边距
- `mb-{size}`: 下外边距
- `ml-{size}`: 左外边距

### 1.3 尺寸类名

#### 宽度

```html
<!-- 宽度示例 -->
<div class="w-64">固定宽度</div>
<div class="w-full">全宽</div>
<div class="w-1/2">50%宽度</div>
<div class="w-screen">视口宽度</div>
```

- `w-{size}`: 宽度
- `w-full`: 100% 宽度
- `w-screen`: 视口宽度
- `w-min`: 最小宽度
- `w-max`: 最大宽度
- `w-auto`: 自动宽度
- `w-1/2`: 50% 宽度
- `w-1/3`: 33.33% 宽度
- `w-2/3`: 66.66% 宽度
- `w-1/4`: 25% 宽度
- `w-3/4`: 75% 宽度

#### 高度

```html
<!-- 高度示例 -->
<div class="h-64">固定高度</div>
<div class="h-full">全高</div>
<div class="h-screen">视口高度</div>
<div class="min-h-screen">最小全屏高度</div>
```

- `h-{size}`: 高度
- `h-full`: 100% 高度
- `h-screen`: 视口高度
- `h-min`: 最小高度
- `h-max`: 最大高度
- `h-auto`: 自动高度

### 1.4 颜色类名

#### 背景色

```html
<!-- 背景色示例 -->
<div class="bg-blue-500">纯色背景</div>
<div class="bg-gradient-to-r from-blue-500 to-purple-500">渐变背景</div>
<div class="bg-transparent">透明背景</div>
```

- `bg-{color}-{shade}`: 背景色
- `bg-transparent`: 透明背景
- `bg-current`: 当前文字颜色
- `bg-gradient-to-{direction}`: 渐变背景方向
- `from-{color}-{shade}`: 渐变起始颜色
- `to-{color}-{shade}`: 渐变结束颜色

#### 文字颜色

```html
<!-- 文字颜色示例 -->
<p class="text-blue-500">蓝色文字</p>
<p class="text-gray-600">灰色文字</p>
<p class="text-transparent">透明文字</p>
```

- `text-{color}-{shade}`: 文字颜色
- `text-transparent`: 透明文字
- `text-current`: 当前颜色

#### 边框颜色

```html
<!-- 边框颜色示例 -->
<div class="border border-blue-500">蓝色边框</div>
<div class="border border-transparent">透明边框</div>
```

- `border-{color}-{shade}`: 边框颜色
- `border-transparent`: 透明边框
- `border-current`: 当前颜色边框

### 1.5 排版类名

#### 文字大小

```html
<!-- 文字大小示例 -->
<p class="text-xs">超小文字</p>
<p class="text-sm">小文字</p>
<p class="text-base">基础文字</p>
<p class="text-lg">大文字</p>
<p class="text-2xl">特大文字</p>
```

- `text-xs`: 超小文字
- `text-sm`: 小文字
- `text-base`: 基础文字
- `text-lg`: 大文字
- `text-xl`: 超大文字
- `text-2xl`: 特大文字
- `text-3xl`: 超大文字
- `text-4xl`: 特大文字
- `text-5xl`: 超大文字
- `text-6xl`: 特大文字

#### 字重

```html
<!-- 字重示例 -->
<p class="font-light">细体</p>
<p class="font-normal">正常</p>
<p class="font-bold">粗体</p>
<p class="font-black">最粗</p>
```

- `font-thin`: 最细
- `font-extralight`: 超细
- `font-light`: 细体
- `font-normal`: 正常
- `font-medium`: 中等
- `font-semibold`: 半粗
- `font-bold`: 粗体
- `font-extrabold`: 超粗
- `font-black`: 最粗

#### 文字对齐

```html
<!-- 文字对齐示例 -->
<p class="text-left">左对齐</p>
<p class="text-center">居中对齐</p>
<p class="text-right">右对齐</p>
<p class="text-justify">两端对齐</p>
```

- `text-left`: 左对齐
- `text-center`: 居中对齐
- `text-right`: 右对齐
- `text-justify`: 两端对齐

### 1.6 边框类名

#### 边框样式

```html
<!-- 边框样式示例 -->
<div class="border">基础边框</div>
<div class="border-2 border-blue-500">蓝色粗边框</div>
<div class="rounded-lg">圆角边框</div>
<div class="rounded-full">完全圆角</div>
```

- `border`: 边框
- `border-{width}`: 边框宽度
- `border-{style}`: 边框样式
- `border-{color}-{shade}`: 边框颜色
- `rounded`: 圆角
- `rounded-{size}`: 圆角大小
- `rounded-full`: 完全圆角

### 1.7 响应式类名

#### 断点前缀

```html
<!-- 响应式示例 -->
<div class="w-full md:w-1/2 lg:w-1/3">响应式宽度</div>
<div class="hidden md:block">中等屏幕以上显示</div>
```

- `sm:`: 640px 以上
- `md:`: 768px 以上
- `lg:`: 1024px 以上
- `xl:`: 1280px 以上
- `2xl:`: 1536px 以上

#### 暗色模式

```html
<!-- 暗色模式示例 -->
<div class="bg-white dark:bg-gray-800 text-gray-900 dark:text-white">支持暗色模式的内容</div>
```

- `dark:`: 暗色模式

### 1.8 交互类名

#### 状态

```html
<!-- 交互状态示例 -->
<button
  class="hover:bg-blue-600 focus:ring-2 focus:ring-blue-500 active:bg-blue-700 disabled:opacity-50">
  交互按钮
</button>
```

- `hover:`: 悬停状态
- `focus:`: 焦点状态
- `active:`: 激活状态
- `disabled:`: 禁用状态
- `group-hover:`: 组悬停状态

#### 过渡

```html
<!-- 过渡效果示例 -->
<div class="transition-all duration-300 hover:scale-105">悬停时放大</div>
<div class="transition-colors duration-500 hover:bg-blue-500">颜色过渡</div>
```

- `transition`: 过渡效果
- `transition-all`: 所有属性过渡
- `transition-colors`: 颜色过渡
- `transition-opacity`: 透明度过渡
- `transition-transform`: 变换过渡
- `duration-{time}`: 过渡时长
- `ease-{type}`: 过渡类型

### 1.9 其他类名

#### 显示

```html
<!-- 显示示例 -->
<div class="block">块级元素</div>
<div class="inline-block">行内块元素</div>
<div class="inline">行内元素</div>
<div class="hidden">隐藏元素</div>
<div class="visible">显示元素</div>
```

- `block`: 块级元素
- `inline-block`: 行内块元素
- `inline`: 行内元素
- `hidden`: 隐藏元素
- `visible`: 显示元素

#### 定位

```html
<!-- 定位示例 -->
<div class="relative">
  <div class="absolute top-0 right-0">绝对定位</div>
  <div class="fixed bottom-0 right-0">固定定位</div>
  <div class="sticky top-0">粘性定位</div>
</div>
```

- `static`: 静态定位
- `relative`: 相对定位
- `absolute`: 绝对定位
- `fixed`: 固定定位
- `sticky`: 粘性定位

#### 溢出

```html
<!-- 溢出示例 -->
<div class="overflow-auto">自动滚动</div>
<div class="overflow-hidden">隐藏溢出</div>
<div class="overflow-scroll">强制滚动</div>
<div class="overflow-visible">显示溢出</div>
```

- `overflow-auto`: 自动滚动
- `overflow-hidden`: 隐藏溢出
- `overflow-scroll`: 强制滚动
- `overflow-visible`: 显示溢出

#### 阴影

```html
<!-- 阴影示例 -->
<div class="shadow">基础阴影</div>
<div class="shadow-lg">大阴影</div>
<div class="shadow-inner">内阴影</div>
<div class="shadow-none">无阴影</div>
```

- `shadow`: 基础阴影
- `shadow-sm`: 小阴影
- `shadow-md`: 中等阴影
- `shadow-lg`: 大阴影
- `shadow-xl`: 超大阴影
- `shadow-2xl`: 特大阴影
- `shadow-inner`: 内阴影
- `shadow-none`: 无阴影

[toc]

# 准备

学习准备:安装 node.js

## 全局安装 create-react-app

```bash
# 由 React Router 维护的基本模板
npx create-react-router@latest my-react-router-app

# 安装 create-react-app脚手架
npm i -g create-react-app
# 查看脚手架版本
create-react-app -V
# 创建一个新项目
create-react-app my-app
# 进入项目目录
cd my-app
# 安装依赖
npm i
# 启动项目
npm start
# 测试项目
npm test
# 打包项目
npm run build
```

项目创建

```bash
npx create-react-app my-app --template typescript
npm install prettier eslint-config-prettier eslint-plugin-prettier eslint-plugin-react-hooks --save-dev
```

src 内部的文件可以全部删除,只保留 index.js 文件。
src 文件夹内必须有一个 index.js 文件,作为项目的入口文件。

```js
// index.js 文件 作为项目的入口文件
// 初始react
// 引入
import React from "react"
import ReactDOM from "react-dom/client"
import "./index.css"
// 获取dom
const root = ReactDOM.createRoot(document.getElementById("root"))
// 写入组件或dom节点,在页面中渲染
root.render(
  <div>
    1<div>2</div>
  </div>
)
```

## 快捷方式

```js
// vscode 安装 react 扩展后
// rcc 创建组件快捷键
```

## JSX 语法

JSX 是一种 JavaScript 的语法扩展,它允许我们在 JavaScript 代码中嵌入 XML 元素。

JSX 语法的基本规则：

- JSX 代码必须包含在 `{}` 中,以便 JavaScript 引擎能够正确解析 JSX 代码。

## 组件

### 组件创建

通览

```js
// 组件必须大写开头
import React, { Component } from "react"
// 类式声明
class A extends Component {
  render() {
    return <div>A</div>
  }
}
// 函数式声明
function B() {
  return <div>B</div>
}
// 箭头函数声明(强烈推荐)
const C = () => <div>C</div>
export default class App extends Component {
  render() {
    return (
      <div>
        <A></A>
        <B></B>
        <C></C>
      </div>
    )
  }
}
```

![1731064714478](images/assets/1731064714478.png)

#### 类组件

##### 定义组件

```js
// 引入React
import React from "react"
// 定义组件
// 组件名必须以大写字母开头
class App extends React.Component {
  render() {
    // 返回 JSX 元素
    // 组件只能有一个根目录元素
    return (
      // 当我们需要有多个根目录时,我们可以使用jsx的语法糖,用一个空标签当作根目录,并在里面包裹多个元素,该空标签不会被渲染
      <>
        <div>这是我创建的组件</div>
        <div>这是第二个目录元素</div>
      </>
    )
  }
}
// 导出组件
export default App
```

##### 使用组件

```js
import React from "react"
import ReactDOM from "react-dom/client"
import "./index.css"
// 导入组件
import App from "./zujian"

const root = ReactDOM.createRoot(document.getElementById("root"))
// 使用组件以标签的形式渲染到页面中
root.render(<App></App>)
```

![1731063261355](images/assets/1731063261355.png)

#### 函数式组件

##### 定义组件

```js
// App.js 文件
export default function App() {
  return (
    <div>
      <h1>函数式组件声明</h1>
    </div>
  )
}
```

##### 使用组件

```js
// 引入
import React from "react"
import ReactDOM from "react-dom/client"
import "./index.css"
// 导入组件
import App from "./App"

// 获取dom
const root = ReactDOM.createRoot(document.getElementById("root"))
// 写入组件或dom节点,在页面中渲染
root.render(<App></App>)
```

### 组件嵌套

组件之间可以互相嵌套,形成一个组件树。
孙组件放在子组件内,子组件放在夫组件内就可以实现嵌套

```js
import React, { Component } from "react"

class A extends Component {
  render() {
    return <div>A</div>
  }
}
function B() {
  return (
    <>
      <div>B</div>
      <C></C>
    </>
  )
}
const C = () => <div>C</div>
export default class App extends Component {
  render() {
    return (
      <div>
        <A></A>
        <B></B>
      </div>
    )
  }
}
```

![1731064714478](images/assets/1731064714478.png)

### 组件的状态以及插值语法

```jsx
import React, { Component } from "react"

class A extends Component {
  render() {
    const A = "A"
    return <div>{A}</div>
  }
}
function B() {
  const B = "B"
  return (
    <>
      <div>{B}</div>
      <div>{1 + 1}</div>
      <C></C>
    </>
  )
}
const C = () => {
  const C = "C"
  return <div>{C}</div>
}
export default class App extends Component {
  render() {
    return (
      <div>
        <A></A>
        <B></B>
      </div>
    )
  }
}
```

### 组件的属性

```jsx
// 以样式为例
import React, { Component } from "react"
// 导入样式文件
import "./index.css"

class A extends Component {
  render() {
    const A = {
      backgroundColor: "red",
      color: "pink",
    }
    // class要写成className
    return (
      <>
        <div
          style={A}
          className="box">
          {A.backgroundColor}
        </div>
        <div
          style={{ backgroundColor: "red", color: "pink" }}
          className="box">
          {A.backgroundColor}
        </div>
        {/* 点标签聚焦输入框 for属性要写成htmlFor */}
        <label htmlFor="color">颜色：</label>
        <input
          type="color"
          id="color"
        />
      </>
    )
  }
}

export default class App extends Component {
  render() {
    return (
      <div>
        <A></A>
      </div>
    )
  }
}
```

## 事件处理

```jsx
import React, { Component } from "react"

export default class App extends Component {
  a(str, e) {
    console.log(typeof str == "string" ? "修改this绑定" : "绑定的是类中function声明的方法")
    console.log("this", this)
    console.log("事件对象", typeof str == "string" ? (e ? e : "无") : str)
  }
  b = (str) => {
    console.log(typeof str == "string" || "绑定的是类中箭头函数声明的方法")
    console.log("this", this)
    console.log("事件对象", typeof str == "string" ? "无" : str)
  }
  render() {
    function xuanran(str, e) {
      console.log(typeof str == "string" ? "修改this绑定" : "绑定的是render函数内声明的方法")
      console.log("this", this)
      console.log("事件对象", typeof str == "string" ? (e ? e : "无") : str)
    }
    return (
      <div>
        <input></input>
        <br />
        {/* 绑定函数不要加括号,加括号后就变成了运行函数,只能运行这一次,之后就无法再运行了 */}
        {/* 但是这种方式可以拿到触发事件的事件对象 */}
        <button onClick={xuanran}>绑定的是render函数内声明的方法</button>
        <button onClick={xuanran.bind(this, "修改this绑定")}>修改this绑定</button>
        <br />
        <button onClick={this.a}>绑定的是类中function声明的方法</button>
        <button onClick={this.a.bind(this, "修改this绑定")}>修改this绑定</button>
        <br />
        <button onClick={this.b}>绑定的是类中箭头函数声明的方法</button>
        <br />
        <button
          onClick={(e) => {
            console.log("绑定一个箭头函数")
            console.log(this)
            console.log("事件对象", e)
          }}>
          绑定一个箭头函数
        </button>
        <br />
        <button
          onClick={() => {
            xuanran("在箭头函数中调用render函数内声明的方法")
          }}>
          在箭头函数中调用render函数内声明的方法
        </button>
        <button
          onClick={() => {
            xuanran.bind(this, "修改this指向")()
          }}>
          修改this指向
        </button>
        <br />
        <button
          onClick={() => {
            this.a("在箭头函数中调用类中声明的方法")
          }}>
          在箭头函数中调用类中声明的方法
        </button>
        <button
          onClick={() => {
            this.a.bind(this, "修改this指向")()
          }}>
          修改this指向
        </button>
      </div>
    )
  }
}
```

![1731118753011](images/assets/1731118753011.png)

![1731118767713](images/assets/1731118767713.png)

![1731119646075](images/assets/1731119646075.png)

![1731119662073](images/assets/1731119662073.png)

![1731119684260](images/assets/1731119684260.png)

## ref 获取 dom 节点

### 已淘汰用法

```jsx
import React, { Component } from "react"

export default class App extends Component {
  mytxt = React.createRef()
  render() {
    return (
      <div>
        <input ref="mytxt" />
        <button
          onClick={() => {
            console.log(this.refs.mytxt)
            console.log(this.refs.mytxt.value)
          }}>
          确认
        </button>
      </div>
    )
  }
}
```

![1731120415388](images/assets/1731120415388.png)

### 推荐用法

```jsx
import React, { Component } from "react"

export default class App extends Component {
  mytxt = React.createRef()
  render() {
    return (
      <div>
        <input ref={this.mytxt} />
        <button
          onClick={() => {
            console.log(this.mytxt)
            console.log(this.mytxt.value)
            console.log(this.mytxt.current)
            console.log(this.mytxt.current.value)
          }}>
          确认
        </button>
      </div>
    )
  }
}
```

![1731120855865](images/assets/1731120855865.png)

## 响应式数据(状态)

```jsx
import React, { Component } from "react"

export default class App extends Component {
  // 声明状态
  state = {
    mytxt: 0,
  }
  render() {
    return (
      <button
        onClick={() => {
          {
            /* 为状态赋值,会触发组件的重新渲染 */
            /* 注意！！！不要直接修改状态,而是通过setState方法修改状态 */
            /* 注意！！！类式编程不是覆盖,可以直接添加修改属性 */
          }
          this.setState({ mytxt: this.state.mytxt + 1 })
        }}>
        {this.state.mytxt}
      </button>
    )
  }
}
```

### 同步异步更新

在 react18 之前

1. 当 steState 处在同步逻辑中时,异步更新状态,更新真实 dom,异步更新真实 dom
2. 当 setState 处在异步逻辑中时,同步更新状态,更新虚拟 dom,同步更新真实 dom

！！！**react18 版本之后不管在哪里都是异步操作**

- **同步代码**

```jsx
// setState可以接受一个回调函数,在更新状态后执行该函数
// 异步更新状态
import React, { Component } from "react"

export default class App extends Component {
  state = {
    mytxt: 0,
  }

  handleClick = () => {
    this.setState(
      {
        mytxt: this.state.mytxt + 1,
      },
      () => {
        console.log("异步更新真实dom")
        console.log(this.state.mytxt)
      }
    )
    console.log("更新状态后执行的同步函数")
    console.log(this.state.mytxt)
  }

  render() {
    return (
      <div>
        <button onClick={this.handleClick}>点击我</button>
        <p>当前值: {this.state.mytxt}</p>
      </div>
    )
  }
}
```

![1731212077333](images/assets/1731212077333.png)

- **异步代码**

```jsx
import React, { Component } from "react"

export default class App extends Component {
  state = {
    mytxt: 0,
  }

  handleClick = () => {
    setTimeout(() => {
      this.setState(
        {
          mytxt: this.state.mytxt + 1,
        },
        () => {
          console.log("异步更新真实dom")
          console.log(this.state.mytxt)
        }
      )
      console.log("更新状态后执行的同步函数")
      console.log(this.state.mytxt)
    }, 1000)
  }

  render() {
    return (
      <div>
        <button onClick={this.handleClick}>点击我</button>
        <p>当前值: {this.state.mytxt}</p>
      </div>
    )
  }
}
```

![1731212342354](images/assets/1731212342354.png)

## 循环遍历与条件渲染案例 todoList

```jsx
import React, { Component } from "react"

export default class App extends Component {
  state = {
    arr: [
      { id: 1, txt: "1" },
      { id: 2, txt: "2" },
      { id: 3, txt: "3" },
    ],
  }
  mytxt = React.createRef()
  // 渲染列表
  renderlist() {
    if (this.state.arr.length !== 0) {
      return this.state.arr.map((v) => (
        <li key={v.id}>
          <span>{v.id}</span>
          <b>{v.txt}</b>
          <button
            onClick={() => {
              this.del(v.id)
            }}>
            删除我
          </button>
        </li>
      ))
    } else {
      return "啥也没有了"
    }
  }
  // 添加待办事项
  add() {
    let arrcopy = [...this.state.arr]
    arrcopy.push({
      id: Date.now(),
      txt: this.mytxt.current.value,
    })
    this.mytxt.current.value = ""
    this.setState({
      arr: arrcopy,
    })
  }
  // 删除待办事项
  del(id) {
    this.setState({
      arr: this.state.arr.filter((v) => v.id !== id),
    })
  }
  render() {
    return (
      <>
        <input ref={this.mytxt}></input>
        <button
          onClick={() => {
            this.add()
          }}>
          发布
        </button>
        <ul>{this.renderlist()}</ul>
      </>
    )
  }
}
```

![1731144540270](images/assets/1731144540270.png)

![1731144555090](images/assets/1731144555090.png)

![1731144571705](images/assets/1731144571705.png)

## 富文本属性:相当于 innerHTML

```jsx
import React, { Component } from "react"

export default class App extends Component {
  state = {
    html: "<div>六六六花花</div>",
  }
  render() {
    return (
      <div>
        <b
          dangerouslySetInnerHTML={{
            __html: this.state.html,
          }}></b>
      </div>
    )
  }
}
```

# React

## Hooks

Hooks ——以 use 开头的函数——只能在组件或自定义 Hook 的最顶层调用。 你不能在条件语句、循环语句或其他嵌套函数内调用 Hook。Hook 是函数,但将它们视为关于组件需求的无条件声明会很有帮助。在组件顶部 “use” React 特性,类似于在文件顶部“导入”模块

### 1. useState

- useState 主要用于在函数组件中添加状态管理。通过 useState,我们可以在函数组件中实现状态的存储和更新
- useState 接受一个初始状态作为参数,这个初始状态可以是任意类型,如字符串、数字、数组、对象等。
- useState 返回一个数组,数组的第一个元素是当前状态,第二个元素是一个函数,用于更新状态。

```jsx
const [count, setCount] = useState(0)

// 更新状态的函数
const increment = () => {
  setCount(count + 1) // 更新状态
}

// 在组件中使用
return (
  <div>
    <p>当前计数: {count}</p>
    <button onClick={increment}>增加</button>
  </div>
)
```

### 2. useReducer

- useReducer 主要用于在组件中处理复杂的状态逻辑。与 useState 相比,useReducer 更适合处理多个状态值之间逻辑复杂的情况,特别是当状态依赖于先前的状态时
- useReducer 接受一个 reducer 函数和初始状态作为参数,这个 reducer 函数是一个纯函数,接收两个参数：当前状态和要执行的动作。
- useReducer 返回一个数组,数组的第一个元素是当前状态,第二个元素是一个函数,用于触发动作。

```jsx
const initialState = { count: 0 }

function reducer(state, action) {
  switch (action.type) {
    case "increment":
      return { count: state.count + 1 }
    case "decrement":
      return { count: state.count - 1 }
    default:
      throw new Error()
  }
}

const [state, dispatch] = useReducer(reducer, initialState)

// 在组件中使用
return (
  <div>
    <p>当前计数: {state.count}</p>
    <button onClick={() => dispatch({ type: "increment" })}>增加</button>
    <button onClick={() => dispatch({ type: "decrement" })}>减少</button>
  </div>
)
```

### 3. useContext

- useContext 主要用于在组件中访问 React 上下文（Context）对象。它使得我们可以在组件树中共享数据,而无需手动通过每一层组件的 props 进行传递。通常用于主题、用户信息等全局状态的管理
- useContext 接受一个上下文对象作为参数,这个对象是通过 React.createContext() 创建的。创建上下文时,通常会定义一个默认值。
- useContext 会返回当前上下文的值。这个值是最近一次与该上下文相关的 Provider 提供的值。如果当前没有匹配的 Provider,则返回 createContext 时提供的默认值。

```jsx
// 在这个例子中,ThemedComponent 可以访问 ThemeContext 的值,并且因为它被包裹在 ThemeContext.Provider 中,所以会显示 dark 主题。
const ThemeContext = React.createContext("light")

function App() {
  return (
    <ThemeContext.Provider value="dark">
      <ThemedComponent />
    </ThemeContext.Provider>
  )
}

function ThemedComponent() {
  const theme = useContext(ThemeContext)

  return <div className={theme}>当前主题: {theme}</div>
}
```

### 4. useRef

- useRef 主要用于创建一个可变的引用,其值在组件的整个生命周期中保持不变。useRef 可以用于访问 DOM 元素或者存储任何不需要引起组件重新渲染的数据。
- useRef 接受一个初始值作为参数,并返回一个包含 current 属性的对象。这个 current 属性可以用来存储希望保持的值。
- useRef 返回一个可变的对象,其 current 属性可以保持对任何值的引用。这个对象在组件渲染之间保持不变。

```jsx
// 访问 DOM 元素,useRef 常用于获取 DOM 元素,例如：
// 在这个例子中,inputRef 被用来直接访问 input 元素,并在点击按钮时使其获得焦点
function FocusInput() {
  const inputRef = useRef(null)

  const focusInput = () => {
    inputRef.current.focus() // 使用 ref 获取元素并调用 focus 方法
  }

  return (
    <div>
      <input
        ref={inputRef}
        type="text"
      />
      <button onClick={focusInput}>聚焦输入框</button>
    </div>
  )
}
```

```jsx
// 存储不需要重新渲染的数据,useRef 还可以用来存储不需要触发重新渲染的值。例如,用于存储计时器 ID
// 在这个例子中,使用 useRef 存储计时器 ID,这样在清理时可以正确地关闭定时器,而不会导致组件重新渲染
function Timer() {
  const timerRef = useRef(null)

  useEffect(() => {
    timerRef.current = setInterval(() => {
      console.log("定时器正在运行")
    }, 1000)

    return () => clearInterval(timerRef.current) // 清理定时器
  }, [])

  return <div>定时器已启动</div>
}
```

### 5. useCallback

- useCallback 主要用于返回一个记忆化的回调函数。它的主要目的是优化性能,防止在每次组件渲染时创建新的函数实例。特别是在将回调函数传递给子组件时,能够避免不必要的重新渲染。
- useCallback 接受一个函数和依赖项数组作为参数,函数参数可以是任何类型。
- useCallback 返回一个新的函数,这个函数是原始函数的记忆化版本,只有当依赖项数组中的值发生变化时才会更新。

```jsx
// 在这个例子中,handleClick 被包裹在 useCallback 中,这样即使父组件重新渲染,只有当 count 发生变化时,handleClick 的引用才会更新,这样 ChildComponent 就不会因为函数引用的变化而重新渲染,从而优化性能。
// 何时使用 useCallback
// 当有性能问题,尤其是在大型列表或复杂组件中频繁更新时。
// 当将回调函数传递给优化后的子组件（例如使用 React.memo）时,可以有效减少不必要的重新渲染。
function ParentComponent() {
  const [count, setCount] = useState(0)

  const handleClick = useCallback(() => {
    setCount(count + 1)
  }, [count])

  return (
    <div>
      <ChildComponent onClick={handleClick} />
      <p>当前计数: {count}</p>
    </div>
  )
}

function ChildComponent({ onClick }) {
  console.log("ChildComponent 渲染")

  return <button onClick={onClick}>增加</button>
}
```

### 6. useMemo

- useMemo 主要用于在函数组件中进行性能优化。它通过记忆化计算结果,防止在每次渲染时都重复执行开销大的计算。useMemo 适用于需要进行复杂计算,并且该计算结果在某些依赖项未变化的情况下可以复用的情况。
- useMemo 接受一个计算函数和依赖项数组作为参数,函数参数可以是任何类型。
- useMemo 返回一个 memoizedValue,这个 memoizedValue 是计算函数的返回值,只有当依赖项数组中的值发生变化时才会更新。

```jsx
// 在这个例子中,useMemo 用于记忆 computedValue 的计算结果,只有当 num 发生变化时,才会重新执行开销大的计算。这样可以避免在每次渲染时都进行昂贵的计算,提高应用性能。
function ExpensiveComputation({ num }) {
  const computedValue = useMemo(() => {
    // 模拟开销大的计算
    let result = 0
    for (let i = 0; i < 1000000000; i++) {
      result += num * Math.random()
    }
    return result
  }, [num]) // 仅在 num 改变时重新计算

  return <div>计算结果: {computedValue}</div>
}
```

### 7. useEffect

- useEffect 主要用于在函数组件中处理副作用（side effects）。副作用可以是数据获取、订阅、手动操作 DOM、设置计时器等。useEffect 使得我们能够在组件渲染后执行这些副作用,而不影响组件的渲染过程。
- useEffect 接受一个函数和依赖项数组作为参数,函数参数可以是任何类型。
- useEffect 返回一个函数,这个函数会在组件渲染或依赖项变化后执行副作用。

![1732541103431](images/React/1732541103431.png)

```jsx
// 在这个例子中,useEffect 用于设置一个定时器,每秒更新时间。当组件卸载时,返回的清理函数会被调用,从而清除定时器,避免内存泄漏。
// 何时使用 useEffect
// 当有数据获取或异步操作需要在组件渲染后执行时。
// 当需要订阅或进行 DOM 操作时。
// 在组件卸载或依赖项变化时需要清理工作时。
function Timer() {
  const [count, setCount] = useState(0)

  useEffect(() => {
    const timer = setInterval(() => {
      setCount((prevCount) => prevCount + 1)
    }, 1000)

    // 清理函数
    return () => {
      clearInterval(timer)
    }
  }, []) // 仅在组件挂载时设置定时器

  return <div>计时: {count} 秒</div>
}
```

### 8. useLayoutEffect

- useLayoutEffect 的功能与 useEffect 类似,但它在 DOM 更新后但在浏览器绘制之前执行。这意味着在 useLayoutEffect 中执行的代码会阻塞浏览器的绘制,确保计算的值能够及时反映在界面上。通常用于需要进行 DOM 操作并确保界面可以立即表现出来的情况。
- useLayoutEffect 接受一个函数和依赖项数组作为参数,函数参数可以是任何类型。
- useLayoutEffect 返回一个函数,这个函数会在组件渲染或依赖项变化后执行副作用。

```jsx
// 在这个例子中,useLayoutEffect 被用来在 DOM 更新后计算容器的高度,并确保这个计算在浏览器绘制之前完成。这样,可以及时得到布局信息,用于后续的渲染或动画逻辑。
// 何时使用 useLayoutEffect
// 当需要进行 DOM 操作且需要在浏览器绘制前确定元素布局或测量时。
// 当需要在更新后立即调整布局时,确保不会有视觉上的闪烁或布局错位。
function UseLayoutExample() {
  const [count, setCount] = useState(0)
  const containerRef = useRef(null)

  useLayoutEffect(() => {
    const container = containerRef.current
    // 在布局更新后测量元素的高度
    const height = container.getBoundingClientRect().height
    console.log("容器高度:", height)
  }, [count]) // 依赖于 count,当 count 改变时重新测量

  return (
    <div ref={containerRef}>
      <p>计数: {count}</p>
      <button onClick={() => setCount(count + 1)}>增加</button>
    </div>
  )
}
```

### 9. useDebugValue

- useDebugValue 主要用于在自定义 Hook 中显示调试信息。它在 React DevTools 中提供自定义 Hook 的额外信息,使得开发者可以更方便地调试和理解自定义 Hook 的状态或行为。
- useDebugValue 接受一个字符串作为参数,这个字符串会显示在 React DevTools 中。

```jsx
// 在这个例子中,useDebugValue 用于显示当前用户的加载状态。如果用户已加载,它会显示“用户已加载”,否则显示“加载中”。这有助于在 React DevTools 中快速了解自定义 Hook 的当前状态。
// 何时使用 useDebugValue
// 当创建自定义 Hook 时,为了提供更好的调试信息。
// 当希望在 React DevTools 中显示自定义状态信息,以便于开发者理解 Hook 的工作情况。
import { useState, useEffect, useDebugValue } from "react"

function useUser(userId) {
  const [user, setUser] = useState(null)

  useEffect(() => {
    // 模拟 API 请求
    const fetchUser = async () => {
      const response = await fetch(`/api/users/${userId}`)
      const data = await response.json()
      setUser(data)
    }

    fetchUser()
  }, [userId])

  // 使用 useDebugValue 来显示用户状态
  useDebugValue(user ? "用户已加载" : "加载中")

  return user
}
```

## 第一个组件

```jsx
// 引入
import React from "react"
import ReactDOM from "react-dom/client"
// 声明一个组件
// 组件名必须大写
function App() {
  return <div>六六六花花</div>
}

// 获取dom
const root = ReactDOM.createRoot(document.getElementById("root"))
// 写入组件或dom节点,在页面中渲染
root.render(<App></App>)
```

```jsx
// 导出组件
export default function App() {
  return <div>六六六花花</div>
}
```

## 组件的导入与导出

```jsx
// 导入组件
import App from "./App"

// 导出组件
export default function App() {
  return <div>六六六花花</div>
}
```

### JSX 语法

#### 只能返回一个根元素

```jsx
// 错误
function App() {
  return (
      <div>1</div>
      <div>2</div>
  );
// 正确

function App() {
  return (
    <>
      <div>1</div>
      <div>2</div>
    </>
  );
}
```

#### 标签必须闭合

像 `<img>` 这样的自闭合标签必须书写成 `<img />`,而像 `<li>oranges` 这样只有开始标签的元素必须带有闭合标签,需要改为 `<li>oranges</li>`

#### 使用驼峰式命名法给大部分属性命名

需要用 strokeWidth 代替 stroke-width。由于 class 是一个保留字,所以在 React 中需要用 className 来代替

由于历史原因,aria-_ 和 data-_ 属性是以带 - 符号的 HTML 格式书写的。

#### JSX 转化器

React 官方提供了 JSX 转化器,可以将 HTML 代码转化为纯 JSX 代码。

可以在线转化：
https://transform.tools/html-to-jsx

### 在 JSX 中通过大括号使用 JavaScript

- 在 JSX 中,只能在以下两种场景中使用大括号：
  - 用作 JSX 标签内的文本：`<h1>{name}'s To Do List</h1>` 是有效的,但是 `<{tag}>Gregorio Y. Zara's To Do List</{tag}>` 无效。
  - 用作紧跟在 = 符号后的 属性：`src={avatar}` 会读取 avatar 变量,但是 `src="{avatar}"` 只会传一个字符串 `{avatar}`
- JSX 引号内的值会作为字符串传递给属性。
- 大括号让你可以将 JavaScript 的逻辑和变量带入到标签中。
- 它们会在 JSX 标签中的内容区域或紧随属性的 = 后起作用。
- {{ 和 }} 并不是什么特殊的语法：它只是包在 JSX 大括号内的 JavaScript 对象

在 React 中使用大括号插值时,你可以插入以下类型的数据：

- 表达式：可以放置任何有效的 JavaScript 表达式,包括变量、函数调用等。例如：`<h1>{title}</h1>`
- 字符串：你可以直接插入字符串。如果需要拼接字符串,可以使用模板字符串或字符串连接：`<p>{`欢迎 ${username}！`}</p>`
- 数字：可以直接插入数字,如果需要显示在文本中,可以与其他字符串一起使用：`<span>{count}</span>`
- 布尔值：布尔值需要小心使用,直接插入布尔值不会在 UI 中显示。如果想条件渲染组件,可以结合三元操作符或逻辑与（&&）：`{isLoggedIn && <p>欢迎回来！</p>}`
- 数组：可以插入数组,React 会将数组中的元素渲染成对应的多个子元素：`const items = ['苹果', '香蕉', '橘子'];<ul>{items.map(item => <li key={item}>{item}</li>)}</ul>`
- 对象：直接插入对象是无效的,通常需要从对象中提取出需要的属性或使用函数返回 JSX 元素：`const user = { name: '小明', age: 25 };<p>{user.name},年龄{user.age}</p>`

```jsx
import { useState } from "react"

export default function App() {
  const [count, setCount] = useState(0)
  const name = "六六六花花"
  return (
    <>
      {/* jsx大括号里面可以写任何有效的JavaScript代码 */}
      <button
        onClick={() => {
          setCount(count + 1)
        }}>
        Click me
      </button>
      <div>{count}</div>
      {/* 双大括号就是一个大括号内包着一个对象 */}
      <div style={{ color: "red" }}>{name + count}</div>
    </>
  )
}
```

## 将 Props 传递给组件

- 要传递 props,请将它们添加到 JSX,就像使用 HTML 属性一样。
- 要读取 props,请使用 `function Avatar({ person, size })` 解构语法。
- 你可以指定一个默认值,如 size = 100,用于缺少值或值为 undefined 的 props 。
- 你可以使用 `<Avatar {...props} />` JSX 展开语法转发所有 props,但不要过度使用它！
- 像 `<Card><Avatar /></Card> `这样的嵌套 JSX,将被视为 Card 组件的 children prop。
- Props 是只读的时间快照：每次渲染都会收到新版本的 props。
- 你不能改变 props。当你需要交互性时,你可以设置 state

### 组件传值

```jsx
// index.jxs
import { StrictMode } from "react"
import { createRoot } from "react-dom/client"
import App from "./App.jsx"

createRoot(document.getElementById("root")).render(
  <StrictMode>
    <App
      person={{ name: "六六六花花", age: 521 }}
      text="我爱你"
    />
  </StrictMode>
)

// App.jsx
/* eslint-disable react/prop-types */

function SixFlower({ person, text, love = "love" }) {
  return (
    <>
      <div>{person.name}</div>
      <div>{person.age}</div>
      <div>{text}</div>
      {/* 默认值 */}
      <div>{love}</div>
    </>
  )
}

export default function App(props) {
  // 展开运算
  return (
    <>
      {/* 对象解构,展开运算符快速逐层传递参数 */}
      <SixFlower {...props}></SixFlower>
      <div>{props.six}</div>
    </>
  )
}
```

![1732327770610](images/React/1732327770610.png)

### 组件嵌套

```jsx
// index.jxs
import { StrictMode } from "react"
import { createRoot } from "react-dom/client"
import App from "./App.jsx"
import { SixFlower } from "./App.jsx"

createRoot(document.getElementById("root")).render(
  <StrictMode>
    <App
      person={{ name: "六六六花花", age: 521 }}
      text="我爱你">
      <SixFlower
        person={{ name: "六六六花花", age: 521 }}
        text="我爱你"></SixFlower>
    </App>
  </StrictMode>
)
// App.jsx
/* eslint-disable react/prop-types */

export function SixFlower({ person, text, love = "love" }) {
  return (
    <>
      <div>{person.name}</div>
      <div>{person.age}</div>
      <div>{text}</div>
      {/* 默认值 */}
      <div>{love}</div>
    </>
  )
}

export default function App(props) {
  // 展开运算
  return (
    <>
      {/* 对象解构,展开运算符快速逐层传递参数 */}
      <SixFlower {...props}></SixFlower>
      <hr />
      {props.children}
    </>
  )
}
```

![1732328165554](images/React/1732328165554.png)

## 条件渲染

- 在 React,你可以使用 JavaScript 来控制分支逻辑。
- 你可以使用 if 语句来选择性地返回 JSX 表达式。
- 你可以选择性地将一些 JSX 赋值给变量,然后用大括号将其嵌入到其他 JSX 中。
- 在 JSX 中,`{cond ? <A /> : <B />}` 表示 “当 cond 为真值时, 渲染 `<A />,否则 <B />`”。
- 在 JSX 中,`{cond && <A />}` 表示 “当 cond 为真值时, 渲染 `<A />`,否则不进行渲染”。
- 快捷的表达式很常见,但如果你更倾向于使用 if,你也可以不使用它们

```jsx
// App.jsx
import { useState } from "react"

/* eslint-disable react/prop-types */
function Is1({ flag }) {
  if (flag) {
    return <h1>六六六花花</h1>
  } else {
    return <h1>我喜欢你</h1>
  }
}
function Is2({ flag }) {
  return flag ? <h1>六六六花花</h1> : <h1>我喜欢你</h1>
}
function Is3({ flag }) {
  return (flag && <h1>六六六花花</h1>) || <h1>我喜欢你</h1>
}

export default function App() {
  const [isGo, setIsGo] = useState(false)
  return (
    <>
      <Is1 flag={isGo} />
      <Is2 flag={isGo} />
      <Is3 flag={isGo} />
      <button
        onClick={() => {
          setIsGo(!isGo)
        }}>
        切换
      </button>
    </>
  )
}
```

![1732331520094](images/React/1732331520094.png)

## 列表渲染

- 从组件中抽离出数据,并把它们放入像数组、对象这样的数据结构中。
- 使用 JavaScript 的 map() 方法来生成一组相似的组件。
- 使用 JavaScript 的 filter() 方法来筛选数组。
- 以及如何给集合中的每个组件设置一个 key 值：它使 React 能追踪这些组件,即便后者的位置或数据发生了变化
- 记住,使用 Fragment 语法（通常写作 <> </>）来包裹 JSX 节点可以避免引入额外的 `<div>` 元素！

```jsx
export default function App() {
  const liuhaArray = ["姓名:小鸟游六花", "性别:女性", "年龄:16岁", "学校:樱花高中", "兴趣:魔法,幻想", "性格特点:天真,认真,善良", "昵称:六花", "代表作品:《中二病也要谈恋爱！》", "特技能:拥有幻想魔法的能力", "朋友:小鸟游六花与富冈义勇、天野惠等伙伴的关系密切"]

  return (
    <ul>
      {liuhaArray.map((item, index) => {
        return <li key={index}>{item}</li>
      })}
    </ul>
  )
}
```

组件传值可以直接用对象与展开运算符来进行传值

```jsx
// ItemList.jsx

// 自定义组件 Item
const Item = (props) => {
  return (
    <div>
      <h3>{props.title}</h3>
      <p>{props.description}</p>
    </div>
  )
}

// 主组件
const ItemList = () => {
  // 示例数据
  const items = [
    { id: 1, title: "Item 1", description: "Description for Item 1" },
    { id: 2, title: "Item 2", description: "Description for Item 2" },
    { id: 3, title: "Item 3", description: "Description for Item 3" },
  ]

  return (
    <div>
      {items.map((item) => (
        <Item
          key={item.id}
          {...item}
        /> // 使用展开运算符传递属性
      ))}
    </div>
  )
}

export default ItemList
```

## 保持组件纯粹

- 一个组件必须是纯粹的,就意味着：
  - 只负责自己的任务。 它不会更改在该函数调用前就已存在的对象或变量。
  - 输入相同,则输出相同。 给定相同的输入,组件应该总是返回相同的 JSX。
- 渲染随时可能发生,因此组件不应依赖于彼此的渲染顺序。
- 你不应该改变任何用于组件渲染的输入。这包括 props、state 和 context。通过 “设置” state 来更新界面,而不要改变预先存在的对象。
- 努力在你返回的 JSX 中表达你的组件逻辑。当你需要“改变事物”时,你通常希望在事件处理程序中进行。作为最后的手段,你可以使用 useEffect。
- 编写纯函数需要一些练习,但它充分释放了 React 范式的能力。

## 将 UI 视为树

- 树是表示实体之间关系的常见方式,它们经常用于建模 UI。
- 渲染树表示单次渲染中 React 组件之间的嵌套关系。
- 使用条件渲染,渲染树可能会在不同的渲染过程中发生变化。使用不同的属性值,组件可能会渲染不同的子组件。
- 渲染树有助于识别顶级组件和叶子组件。顶级组件会影响其下所有组件的渲染性能,而叶子组件通常会频繁重新渲染。识别它们有助于理解和调试渲染性能问题。
- 依赖树表示 React 应用程序中的模块依赖关系。
- 构建工具使用依赖树来捆绑必要的代码以部署应用程序。
- 依赖树有助于调试大型捆绑包带来的渲染速度过慢的问题,以及发现哪些捆绑代码可以被优化

## 响应事件

- 你可以通过将函数作为 prop 传递给元素如 <button> 来处理事件。
- 必须传递事件处理函数,而非函数调用！ onClick={handleClick} ,不是 onClick={handleClick()}。
- 你可以单独或者内联定义事件处理函数。
- 事件处理函数在组件内部定义,所以它们可以访问 props。
- 你可以在父组件中定义一个事件处理函数,并将其作为 prop 传递给子组件。
- 你可以根据特定于应用程序的名称定义事件处理函数的 prop。
- 事件会向上传播。通过事件的第一个参数调用 e.stopPropagation() 来防止这种情况。
- 事件可能具有不需要的浏览器默认行为。调用 e.preventDefault() 来阻止这种情况。
- 从子组件显式调用事件处理函数 prop 是事件传播的另一种优秀替代方案
- 事件处理函数是执行副作用的最佳位置

### 事件处理函数

```jsx
// App.jsx
export default function App() {
  function handleClick(e) {
    console.log("按钮被点击了")
    // 事件对象
    console.log(e)
  }
  return (
    <>
      <button onClick={handleClick}>事件处理函数</button>
      <button
        onClick={(e) => {
          handleClick(e)
        }}>
        箭头函数
      </button>
      {/* 上面两种方式能接收事件对象 */}
      {/* 上面这两个按钮渲染时不会执行handleClick函数,点击时会执行两次 */}
      {/* 下面这个按钮,渲染时会执行handleClick函数,点击时不会执行 */}
      <button onClick={handleClick()}>调用函数</button>
    </>
  )
}
```

### 在事件处理函数中读取 props

```jsx
// App.jsx
/* eslint-disable react/prop-types */
function LogName({ name }) {
  function handleClick(str) {
    console.log(str)
  }
  return (
    <button
      onClick={() => {
        handleClick(name)
      }}>
      log
    </button>
  )
}

export default function App() {
  return <LogName name="六六六花花"></LogName>
}
```

### 将事件处理函数作为 props 传递

```jsx
// App.jsx
/* eslint-disable react/prop-types */
function LogName({ name, MyClick }) {
  function handleClick(str) {
    console.log(str)
    MyClick()
  }
  return (
    <button
      onClick={() => {
        handleClick(name)
      }}>
      log
    </button>
  )
}

export default function App() {
  function Onclick() {
    console.log("click")
  }
  return (
    <LogName
      name="六六六花花"
      MyClick={Onclick}
    />
  )
}
```

### 事件传播

事件处理函数还将捕获任何来自子组件的事件。通常,我们会说事件会沿着树向上“冒泡”或“传播”：它从事件发生的地方开始,然后沿着树向上传播

在 React 中所有事件都会传播,除了 onScroll,它仅适用于你附加到的 JSX 标签

### 阻止传播

事件处理函数接收一个 事件对象 作为唯一的参数。按照惯例,它通常被称为 e ,代表 “event”（事件）。你可以使用此对象来读取有关事件的信息。

这个事件对象还允许你阻止传播。如果你想阻止一个事件到达父组件,你需要调用 e.stopPropagation()

某些浏览器事件具有与事件相关联的默认行为。例如,点击 <form> 表单内部的按钮会触发表单提交事件,默认情况下将重新加载整个页面,你可以调用事件对象中的 e.preventDefault() 来阻止这种情况发生

## State：组件的记忆

- 当一个组件需要在多次渲染间“记住”某些信息时使用 state 变量。
- State 变量是通过调用 useState Hook 来声明的。
- Hook 是以 use 开头的特殊函数。它们能让你 “hook” 到像 state 这样的 React 特性中。
- Hook 可能会让你想起 import：它们需要在非条件语句中调用。调用 Hook 时,包括 useState,仅在组件或另一个 Hook 的顶层被调用才有效。
- useState Hook 返回一对值：当前 state 和更新它的函数。
- 你可以拥有多个 state 变量。在内部,React 按顺序匹配它们。
- State 是组件私有的。如果你在两个地方渲染它,则每个副本都有独属于自己的 state
- React 会等到事件处理函数中的 所有 代码都运行完毕再处理你的 state 更新

### useState

- useState 是一个内置的 React Hook,它允许你在函数组件中存储状态。
- useState 返回一个数组,数组的第一个元素是当前状态,第二个元素是一个函数,用于更新状态。
- 你可以在函数组件中多次调用 useState 来创建多个 state 变量。
- 状态更新函数可以接收一个值,用于更新状态。
- 如果你渲染同一个组件两次,每个副本都会有完全隔离的 state！改变其中一个不会影响另一个

```jsx
import { useState } from "react"
export default function App() {
  const [index, setIndex] = useState(0)

  return (
    <button
      onClick={() => {
        setIndex(index + 1)
      }}>
      {index}
    </button>
  )
}
```

### 将一系列 state 更新加入队列

- 设置 state 不会更改现有渲染中的变量,但会请求一次新的渲染。
- React 会在事件处理函数执行完成之后处理 state 更新。这被称为批处理。
- 要在一个事件中多次更新某些 state,你可以使用 setNumber(n => n + 1) 更新函数。

```jsx
// App.jsx
import { useState } from "react"

export default function App() {
  const [pending, setPending] = useState(0)
  const [completed, setCompleted] = useState(0)

  async function handleClick() {
    setPending((p) => p + 1)
    await delay(3000)
    setPending((p) => p - 1)
    setCompleted((c) => c + 1)
  }

  return (
    <>
      <h3>等待：{pending}</h3>
      <h3>完成：{completed}</h3>
      <button onClick={handleClick}>购买</button>
    </>
  )
}

function delay(ms) {
  return new Promise((resolve) => {
    setTimeout(resolve, ms)
  })
}
```

### 更新 state 中的对象

```jsx
// App.jsx
import { useState } from "react"

export default function App() {
  const [Sixflower, setSixflower] = useState({
    name: "小鸟游六花",
    age: 16,
    ability: "魔法使",
    personality: "有点傲娇",
  })
  function handleClick() {
    setSixflower({
      ...Sixflower,
      age: Sixflower.age + 1,
    })
  }
  return (
    <>
      <h1>{Sixflower.name}</h1>
      <h4>
        {Sixflower.age}岁,{Sixflower.ability},{Sixflower.personality}
      </h4>
      <button onClick={handleClick}>age++</button>
    </>
  )
}
```

#### 使用 Immer 编写简洁的更新逻辑

- 将 React 中所有的 state 都视为不可直接修改的。
- 当你在 state 中存放对象时,直接修改对象并不会触发重渲染,并会改变前一次渲染“快照”中 state 的值。
- 不要直接修改一个对象,而要为它创建一个 新 版本,并通过把 state 设置成这个新版本来触发重新渲染。
- 你可以使用这样的 {...obj, something: 'newValue'} 对象展开语法来创建对象的拷贝。
- 对象的展开语法是浅层的：它的复制深度只有一层。
- 想要更新嵌套对象,你需要从你更新的位置开始自底向上为每一层都创建新的拷贝。
- 想要减少重复的拷贝代码,可以使用 Immer
- Immer 是一个库,它允许你以不可变的方式更新对象。
- 运行 `pnpm install use-immer` 添加 Immer 依赖
- 用 `import { useImmer } from 'use-immer'` 替换掉 `import { useState } from 'react'`

```jsx
import { useImmer } from "use-immer"

export default function App() {
  const [Sixflower, setSixflower] = useImmer({
    name: "小鸟游六花",
    age: 16,
    ability: "魔法使",
    personality: "有点傲娇",
  })
  function handleClick() {
    // 注: 使用 useImmer 时,既可以直接修改,也可以使用原本的展开赋值方法
    setSixflower((item) => {
      item.age++
    })
  }
  return (
    <>
      <h1>{Sixflower.name}</h1>
      <h4>
        {Sixflower.age}岁,{Sixflower.ability},{Sixflower.personality}
      </h4>
      <button onClick={handleClick}>age++</button>
    </>
  )
}
```

#### 更新 state 中的数组

https://zh-hans.react.dev/learn/updating-arrays-in-state#updating-arrays-without-mutation

![1732353014730](images/React/1732353014730.png)

- 只要一个组件还被渲染在 UI 树的相同位置,React 就会保留它的 state。 如果它被移除,或者一个不同的组件被渲染在相同的位置,那么 React 就会丢掉它的 state
- 对 React 来说重要的是组件在 UI 树中的位置,而不是在 JSX 中的位置！
- 相同位置的相同组件会使得 state 被保留下来
- 相同位置的不同组件会使 state 重置
- 一般来说,如果你想在重新渲染时保留 state,几次渲染中的树形结构就应该相互“匹配”。结构不同就会导致 state 的销毁,因为 React 会在将一个组件从树中移除时销毁它的 state

#### [迁移状态逻辑至 Reducer 中](https://zh-hans.react.dev/learn/extracting-state-logic-into-a-reducer)

- 把 useState 转化为 useReducer：
- 通过事件处理函数 dispatch actions；
- 编写一个 reducer 函数,它接受传入的 state 和一个 action,并返回一个新的 state；
- 使用 useReducer 替换 useState；
- Reducer 可能需要你写更多的代码,但是这有利于代码的调试和测试。
- Reducer 必须是纯净的。
- 每个 action 都描述了一个单一的用户交互。
- 使用 Immer 来帮助你在 reducer 里直接修改状态

Reducer 是处理状态的另一种方式。你可以通过三个步骤将 useState 迁移到 useReducer：

- 将设置状态的逻辑 修改 成 dispatch 的一个 action；

```jsx
// 整合数据处理全部逻辑

function handleAddTask(text) {
  setTasks([
    ...tasks,
    {
      id: nextId++,
      text: text,
      done: false,
    },
  ])
}
function handleChangeTask(task) {
  setTasks(
    tasks.map((t) => {
      if (t.id === task.id) {
        return task
      } else {
        return t
      }
    })
  )
}
function handleDeleteTask(taskId) {
  setTasks(tasks.filter((t) => t.id !== taskId))
}
```

- 编写 一个 reducer 函数；

```jsx
// 将整合的逻辑编写成一个 reducer 函数
function tasksReducer(tasks, action) {
  switch (action.type) {
    case "added": {
      return [
        ...tasks,
        {
          id: action.id,
          text: action.text,
          done: false,
        },
      ]
    }
    case "changed": {
      return tasks.map((t) => {
        if (t.id === action.task.id) {
          return action.task
        } else {
          return t
        }
      })
    }
    case "deleted": {
      return tasks.filter((t) => t.id !== action.id)
    }
    default: {
      throw Error("未知 action: " + action.type)
    }
  }
}
```

- 在你的组件中 使用 reducer

```jsx
// 使用 useReducer 代替 useState
import { useReducer } from "react"
import { tasksReducer } from "./tasksReducer"

let nextId = 1 // 初始化任务ID

export default function App() {
  const [tasks, dispatch] = useReducer(tasksReducer, [])

  function handleAddTask(text) {
    dispatch({ type: "added", id: nextId++, text: text })
  }
  function handleChangeTask(task) {
    dispatch({ type: "changed", task: task })
  }
  function handleDeleteTask(taskId) {
    dispatch({ type: "deleted", id: taskId })
  }

  return (
    <>
      <h1>任务列表</h1>
      <ul>
        {tasks.map((task) => (
          <li key={task.id}>
            <input
              type="checkbox"
              checked={task.done}
              onChange={() => handleChangeTask({ ...task, done: !task.done })}
            />
            {task.text}
            <button onClick={() => handleDeleteTask(task.id)}>删除</button>
          </li>
        ))}
      </ul>
      <input
        type="text"
        placeholder="添加任务"
        onChange={(e) => handleAddTask(e.target.value)}
      />
    </>
  )
}
```

最后,你需要将 tasksReducer 导入到组件中。记得先从 React 中导入 useReducer Hook：

import { useReducer } from 'react';
接下来,你就可以替换掉之前的 useState:

const [tasks, setTasks] = useState(initialTasks);
只需要像下面这样使用 useReducer:

const [tasks, dispatch] = useReducer(tasksReducer, initialTasks);
tasksReducer 是一个函数,它接收两个参数：当前的 state 和 action,
然后,你就可以在事件处理程序中调用 dispatch 函数来分发 action：

#### 使用 Context 深层传递参数

- 你可以将 reducer 与 context 相结合,让任何组件读取和更新它的状态。
- 为子组件提供 state 和 dispatch 函数：
- 创建两个 context (一个用于 state,一个用于 dispatch 函数)。
- 让组件的 context 使用 reducer。
- 使用组件中需要读取的 context。
- 你可以通过将所有传递信息的代码移动到单个文件中来进一步整理组件。
- 你可以导出一个像 TasksProvider 可以提供 context 的组件。
- 你也可以导出像 useTasks 和 useTasksDispatch 这样的自定义 Hook。
- 你可以在你的应用程序中大量使用 context 和 reducer 的组合

```jsx
import { useContext } from "react"
import { useImmer } from "use-immer"
import { createContext } from "react"
// 1. 创建 context
// createContext 只需默认值这么一个参数。在这里 null 没有用,但是你可以传递任何类型的值（甚至可以传入一个对象）
const SixContext = createContext(null)

// eslint-disable-next-line react/prop-types
function Appp({ Sixflower, children, handleClick }) {
  return (
    // 提供 context 把它们用 context.Provider 包裹起来 以提供 SixContext 给它们
    // 如果有多个需要传递状态或函数,可以用对象包起来,也可以声明多个context,然后用多个Provider嵌套起来
    // 如<SixContext.Provider ...><SixContext.Provider ...>{children}</SixContext.Provider></SixContext.Provider>
    <SixContext.Provider value={{ Sixflower, handleClick }}>{children}</SixContext.Provider>
  )
}
// eslint-disable-next-line react/prop-types
function Apppp({ children }) {
  return <div>{children}</div>
}
function Sixflowers() {
  // 使用 Context 接收数据
  const { Sixflower, handleClick } = useContext(SixContext)
  return (
    <>
      <h1>{Sixflower.name}</h1>
      <h4>
        {Sixflower.age}岁,{Sixflower.ability},{Sixflower.personality}
      </h4>
      <button onClick={handleClick}>age++</button>
    </>
  )
}
export default function App() {
  const [Sixflower, setSixflower] = useImmer({
    name: "小鸟游六花",
    age: 16,
    ability: "魔法使",
    personality: "有点傲娇",
  })
  function handleClick() {
    setSixflower((item) => {
      item.age++
    })
  }
  return (
    <>
      <Appp
        handleClick={handleClick}
        Sixflower={Sixflower}>
        <Sixflowers />
        {/* 可以在提供 context 的组件和使用它的组件之间的层级插入任意数量的组件。这包括像 <div> 这样的内置组件和你自己创建的组件,中间的组件会被穿透 */}
        <div>
          <Apppp>
            <div>
              <Sixflowers />
            </div>
          </Apppp>
        </div>
      </Appp>
    </>
  )
}
```

由于 context 让你可以从上层的组件读取信息,当一个向下传递状态的组件进行嵌套时,可以传递状态加 1,这样就能获取组件的深度信息。

![1732365806594](images/React/1732365806594.png)

## 使用 ref 引用值

- ref 是一种脱围机制,用于保留不用于渲染的值。 你不会经常需要它们。
- ref 是一个普通的 JavaScript 对象,具有一个名为 current 的属性,你可以对其进行读取或设置。
- 你可以通过调用 useRef Hook 来让 React 给你一个 ref。
- 与 state 一样,ref 允许你在组件的重新渲染之间保留信息。
- 与 state 不同,设置 ref 的 current 值不会触发重新渲染。
- 不要在渲染过程中读取或写入 ref.current。这使你的组件难以预测

![1732537432988](images/React/1732537432988.png)

```jsx
import { useRef, useState } from "react"

export default function App() {
  const [count1, setCount1] = useState(0)
  const count2 = useRef(0)
  function handlerClick1() {
    setCount1(count1 + 1)
    console.log("count1:", count1)
  }
  function handlerClick2() {
    count2.current = count2.current + 1
    console.log("count2:", count2.current)
  }

  return (
    <>
      <div>{count1}</div>
      <div>{count2.current}</div>
      <button onClick={handlerClick1}>count1+1</button>
      <button onClick={handlerClick2}>count2+1</button>
    </>
  )
}
```

## 使用 refs 处理 DOM 节点

- Refs 是一个通用概念,但大多数情况下你会使用它们来保存 DOM 元素。myRef.current
- 你通过传递 `<div ref={myRef}>` 指示 React 将 DOM 节点放入 。
- 通常,你会将 refs 用于非破坏性操作,例如聚焦、滚动或测量 DOM 元素。
- 默认情况下,组件不暴露其 DOM 节点。你可以通过使用 并将第二个参数传递给特定节点来暴露 DOM 节点。forwardRefref
- 避免更改由 React 管理的 DOM 节点。
- 如果你确实修改了 React 管理的 DOM 节点,请修改 React 没有理由更新的部分。

```jsx
import { useRef, useState } from "react"

export default function App() {
  const dddiv = useRef(null)
  const [flag, setFlag] = useState(true)
  return (
    <>
      <div ref={dddiv}>Hello world!</div>
      <button
        onClick={() => {
          setFlag(!flag)
          dddiv.current.innerHTML = flag ? "Hello world!" : "你好 世界!"
        }}>
        Click me
      </button>
    </>
  )
}
```

### 获取其他组件的 refs

```jsx
import { forwardRef, useRef } from "react"

const MyInput = forwardRef((props, ref) => {
  return (
    <input
      {...props}
      ref={ref}
    />
  )
})

export default function Form() {
  const inputRef = useRef(null)

  function handleClick() {
    inputRef.current.focus()
  }

  return (
    <>
      <MyInput ref={inputRef} />
      <button onClick={handleClick}>聚焦输入框</button>
    </>
  )
}
```

## useEffect

- 与事件不同,Effect 由渲染本身引起,而非特定的交互。
- Effect 允许你将组件与某些外部系统（第三方 API、网络等）同步。
- 默认情况下,Effect 在每次渲染（包括初始渲染）后运行。
- 如果所有依赖项都与上一次渲染时相同,React 会跳过本次 Effect。
- 你不能“选择”依赖项,它们是由 Effect 内部的代码所决定的。
- 空的依赖数组（）对应于组件的“挂载”,即组件被添加到页面上时。[]
- 仅在严格模式下的开发环境中,React 会挂载两次组件,以对 Effect 进行压力测试。
- 如果你的 Effect 因为重新挂载而出现问题,那么你需要实现一个清理函数。
- React 会在 Effect 再次运行之前和在组件卸载时调用你的清理函数。
- 如果你可以在渲染期间计算某些内容,则不需要使用 Effect。
- 想要缓存昂贵的计算,请使用 useMemo 而不是 useEffect。
- 想要重置整个组件树的 state,请传入不同的 key。
- 想要在 prop 变化时重置某些特定的 state,请在渲染期间处理。
- 组件 显示 时就需要执行的代码应该放在 Effect 中,否则应该放在事件处理函数中。
- 如果你需要更新多个组件的 state,最好在单个事件处理函数中处理。
- 当你尝试在不同组件中同步 state 变量时,请考虑状态提升。
- 你可以使用 Effect 获取数据,但你需要实现清除逻辑以避免竞态条件。

![1732541103431](images/React/1732541103431.png)

```jsx
import { useEffect, useState } from "react"

export default function App() {
  const [flag, setFlag] = useState(true)
  const [count, setCount] = useState(0)
  // useEffect(() => {
  //   connect();
  //   return () => disconnect();
  // });// 组件只要重新渲染就执行useEffect里面的函数
  /* --------------------------------------------------- */
  // useEffect(() => {
  //   connect();
  //   return () => disconnect();
  // }, []);// 组件只有第一次渲染的时候才执行useEffect里面的函数
  useEffect(() => {
    connect()
    return () => disconnect()
  }, [flag, count]) // 组件渲染时候或者依赖项改变的时候都会执行useEffect里面的函数
  return (
    <>
      <h1>欢迎来到聊天室！</h1>
      <button
        onClick={() => {
          setFlag(!flag)
        }}>
        改变状态{flag + ""}
      </button>
      <button
        onClick={() => {
          setCount(count + 1)
          console.log(count)
        }}>
        +1
      </button>
    </>
  )
}

function connect() {
  console.log("✅ 连接中……")
}
function disconnect() {
  console.log("❌ 连接断开。")
}
```

## useMemo

```jsx
const memoizedValue = useMemo(() => {
  // 计算逻辑
  return computedValue
}, [dependency1, dependency2])
```

- 当依赖项没有变化时,useMemo 可以返回上次计算的结果,从而避免在每次渲染时都执行开销大的计算。例如,在处理大量数据或复杂运算时,使用 useMemo 可以显著减少组件渲染的计算成本。
- 在将 memoized 值传递给子组件时,如果该子组件使用 React.memo 进行优化,子组件将不会重新渲染,前提是传入的 props（包括利用 useMemo 生成的值）没有发生变化。

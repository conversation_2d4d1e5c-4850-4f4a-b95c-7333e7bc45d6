<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0" />
    <title>我的学习笔记库</title>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/pdf-lib@1.17.1/dist/pdf-lib.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/github-markdown-css/5.2.0/github-markdown.min.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        background-color: #f8f9fa;
        height: 100vh;
        overflow: hidden;
      }

      .container {
        display: flex;
        height: 100vh;
      }

      /* 侧边栏样式 */
      .sidebar {
        width: 300px;
        background: #fff;
        border-right: 1px solid #e1e4e8;
        display: flex;
        flex-direction: column;
        box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
      }

      .sidebar-header {
        padding: 20px;
        border-bottom: 1px solid #e1e4e8;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
      }

      .sidebar-header h1 {
        font-size: 1.5rem;
        margin-bottom: 8px;
      }

      .sidebar-header p {
        opacity: 0.9;
        font-size: 0.9rem;
      }

      .search-box {
        padding: 15px;
        border-bottom: 1px solid #e1e4e8;
      }

      .search-input {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #d1d5da;
        border-radius: 6px;
        font-size: 14px;
      }

      .search-input:focus {
        outline: none;
        border-color: #0366d6;
        box-shadow: 0 0 0 3px rgba(3, 102, 214, 0.1);
      }

      .file-tree {
        flex: 1;
        overflow-y: auto;
        padding: 10px 0;
      }

      .category {
        margin-bottom: 10px;
      }

      .category-header {
        padding: 8px 20px;
        background: #f6f8fa;
        border-left: 3px solid #0366d6;
        font-weight: 600;
        color: #24292e;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .category-header:hover {
        background: #e1e4e8;
      }

      .category-header i {
        transition: transform 0.2s;
      }

      .category.collapsed .category-header i {
        transform: rotate(-90deg);
      }

      .file-list {
        display: block;
        transition: all 0.3s ease;
      }

      .category.collapsed .file-list {
        display: none;
      }

      .file-item {
        padding: 8px 20px 8px 40px;
        cursor: pointer;
        display: flex;
        align-items: center;
        transition: background-color 0.2s;
        border-left: 3px solid transparent;
      }

      .file-item:hover {
        background: #f6f8fa;
        border-left-color: #0366d6;
      }

      .file-item.active {
        background: #e3f2fd;
        border-left-color: #0366d6;
        color: #0366d6;
      }

      .file-icon {
        margin-right: 8px;
        width: 16px;
        text-align: center;
      }

      .file-name {
        flex: 1;
        font-size: 14px;
      }

      /* 主内容区域 */
      .main-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        background: #fff;
      }

      /* 内容区域布局 */
      .content-container {
        flex: 1;
        display: flex;
        overflow: hidden;
      }

      /* 目录侧边栏 */
      .toc-sidebar {
        min-width: 200px;
        max-width: 400px;
        width: auto;
        background: #f8f9fa;
        border-right: 1px solid #e1e4e8;
        display: none;
        flex-direction: column;
        overflow: hidden;
        resize: horizontal;
      }

      .toc-sidebar.show {
        display: flex;
      }

      .toc-header {
        padding: 15px;
        background: #fff;
        border-bottom: 1px solid #e1e4e8;
        font-weight: 600;
        color: #24292e;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .toc-content {
        flex: 1;
        overflow-y: auto;
        padding: 10px 0;
      }

      .toc-item {
        padding: 4px 15px;
        cursor: pointer;
        font-size: 13px;
        line-height: 1.4;
        color: #586069;
        border-left: 3px solid transparent;
        transition: all 0.2s;
        display: flex;
        align-items: center;
        text-decoration: none;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .toc-item:hover {
        background: #f6f8fa;
        color: #0366d6;
        border-left-color: #0366d6;
      }

      .toc-item.active {
        background: #e3f2fd;
        color: #0366d6;
        border-left-color: #0366d6;
        font-weight: 500;
      }

      .toc-item.collapsed + .toc-children {
        display: none;
      }

      .toc-children {
        display: block;
      }

      .toc-toggle {
        margin-right: 6px;
        width: 12px;
        height: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
        transition: transform 0.2s;
        color: #586069;
      }

      .toc-toggle.collapsed {
        transform: rotate(-90deg);
      }

      .toc-text {
        flex: 1;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .toc-item.level-1 {
        padding-left: 15px;
        font-weight: 500;
      }
      .toc-item.level-2 {
        padding-left: 25px;
      }
      .toc-item.level-3 {
        padding-left: 35px;
      }
      .toc-item.level-4 {
        padding-left: 45px;
      }
      .toc-item.level-5 {
        padding-left: 55px;
      }
      .toc-item.level-6 {
        padding-left: 65px;
      }

      .content-header {
        padding: 15px 20px;
        border-bottom: 1px solid #e1e4e8;
        background: #fff;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .content-title {
        font-size: 1.2rem;
        font-weight: 600;
        color: #24292e;
      }

      .content-actions {
        display: flex;
        gap: 10px;
      }

      .btn {
        padding: 6px 12px;
        border: 1px solid #d1d5da;
        border-radius: 6px;
        background: #fff;
        color: #24292e;
        text-decoration: none;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.2s;
      }

      .btn:hover {
        background: #f6f8fa;
        border-color: #0366d6;
      }

      .content-viewer {
        flex: 1;
        overflow: auto;
        padding: 20px;
      }

      /* Markdown 样式 */
      .markdown-content {
        max-width: none;
        padding: 0;
      }

      /* PDF 查看器样式 */
      .pdf-viewer {
        display: flex;
        flex-direction: column;
        height: 100%;
      }

      .pdf-controls {
        padding: 10px;
        background: #f6f8fa;
        border-bottom: 1px solid #e1e4e8;
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .pdf-canvas-container {
        flex: 1;
        overflow: auto;
        text-align: center;
        padding: 20px;
      }

      #pdf-canvas {
        border: 1px solid #e1e4e8;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      }

      /* 欢迎页面 */
      .welcome {
        text-align: center;
        padding: 60px 20px;
        color: #586069;
      }

      .welcome i {
        font-size: 4rem;
        margin-bottom: 20px;
        color: #d1d5da;
      }

      .welcome h2 {
        margin-bottom: 10px;
        color: #24292e;
      }

      /* 响应式设计 */
      @media (max-width: 768px) {
        .sidebar {
          width: 250px;
        }

        .content-viewer {
          padding: 15px;
        }
      }

      /* 滚动条样式 */
      ::-webkit-scrollbar {
        width: 8px;
      }

      ::-webkit-scrollbar-track {
        background: #f1f1f1;
      }

      ::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 4px;
      }

      ::-webkit-scrollbar-thumb:hover {
        background: #a1a1a1;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <!-- 侧边栏 -->
      <div class="sidebar">
        <div class="sidebar-header">
          <h1><i class="fas fa-book"></i> 学习笔记库</h1>
          <p>技术文档与学习资料</p>
        </div>

        <div class="search-box">
          <input
            type="text"
            class="search-input"
            placeholder="搜索文档..."
            id="search-input" />
        </div>

        <div
          class="file-tree"
          id="file-tree">
          <!-- 文件树将通过 JavaScript 生成 -->
        </div>
      </div>

      <!-- 主内容区域 -->
      <div class="main-content">
        <div class="content-header">
          <div
            class="content-title"
            id="content-title">
            选择一个文档开始阅读
          </div>
          <div class="content-actions">
            <button
              class="btn"
              id="toc-toggle-btn"
              style="display: none">
              <i class="fas fa-list"></i> 目录
            </button>
            <button
              class="btn"
              id="download-btn"
              style="display: none">
              <i class="fas fa-download"></i> 下载
            </button>
            <button
              class="btn"
              id="fullscreen-btn"
              style="display: none">
              <i class="fas fa-expand"></i> 全屏
            </button>
          </div>
        </div>

        <div class="content-container">
          <!-- 目录侧边栏 -->
          <div
            class="toc-sidebar"
            id="toc-sidebar">
            <div class="toc-header">
              <span><i class="fas fa-list"></i> 目录</span>
              <button
                class="btn"
                id="toc-close-btn"
                style="padding: 2px 6px; font-size: 12px">
                <i class="fas fa-times"></i>
              </button>
            </div>
            <div
              class="toc-content"
              id="toc-content">
              <!-- 目录将通过 JavaScript 生成 -->
            </div>
          </div>

          <!-- 主内容查看器 -->
          <div
            class="content-viewer"
            id="content-viewer">
            <div class="welcome">
              <i class="fas fa-file-alt"></i>
              <h2>欢迎使用文档查看器</h2>
              <p>从左侧选择一个文档开始阅读</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <script>
      // 文件分类配置
      const fileCategories = {
        前端技术: {
          icon: 'fab fa-js-square',
          files: [
            'docs/HTML.md',
            'docs/CSS.md',
            'docs/JaveScript.md',
            'docs/Typescript.md',
            'docs/Vue.md',
            'docs/React.md',
            'docs/Nuxt.md',
            'docs/tailwindcss.md',
            'docs/cropperjs.md',
          ],
        },
        后端技术: {
          icon: 'fas fa-server',
          files: [
            'docs/Node.js.md',
            'docs/Nest.md',
            'docs/Python.md',
            'docs/MongoDB文档查询技巧.md',
            'pdfs/MongoDB.pdf',
            'pdfs/Redis.pdf',
            'pdfs/SQL.pdf',
          ],
        },
        开发工具: {
          icon: 'fas fa-tools',
          files: [
            'docs/GIT.md',
            'pdfs/Git.pdf',
            'pdfs/Docker.pdf',
            'pdfs/Nginx.pdf',
            'docs/linux.md',
            'docs/OpenSSL.md',
          ],
        },
        移动开发: {
          icon: 'fas fa-mobile-alt',
          files: ['docs/HarmonyOS.md'],
        },
        开发规范: {
          icon: 'fas fa-clipboard-list',
          files: [
            'docs/JavaScript 风格指南.md',
            'pdfs/Web前端开发规范手册.pdf',
            'others/W3C.chm',
            'others/css手册4.2.3.chm',
          ],
        },
        项目管理: {
          icon: 'fas fa-project-diagram',
          files: [
            'docs/项目创建流程与相关库配置.md',
            'docs/前后端交互.md',
            'docs/TS配置文件详解.md',
          ],
        },
        学习资料: {
          icon: 'fas fa-graduation-cap',
          files: [
            'docs/专业术语.md',
            'docs/数据库.md',
            'docs/电脑证书信任管理.md',
            'docs/README.md',
          ],
        },
        其他资源: {
          icon: 'fas fa-folder',
          files: ['images/CSS层叠样式表.png', 'images/学会提问.png'],
        },
      }

      // 当前选中的文件
      let currentFile = null
      let currentPdfDoc = null
      let currentPageNum = 1

      // 初始化应用
      function initApp() {
        renderFileTree()
        setupEventListeners()
      }

      // 渲染文件树
      function renderFileTree() {
        const fileTree = document.getElementById('file-tree')
        fileTree.innerHTML = ''

        Object.entries(fileCategories).forEach(([categoryName, categoryData]) => {
          const categoryDiv = document.createElement('div')
          categoryDiv.className = 'category'

          const categoryHeader = document.createElement('div')
          categoryHeader.className = 'category-header'
          categoryHeader.innerHTML = `
                    <span><i class="${categoryData.icon}"></i> ${categoryName}</span>
                    <i class="fas fa-chevron-down"></i>
                `

          const fileList = document.createElement('div')
          fileList.className = 'file-list'

          categoryData.files.forEach((filePath) => {
            const fileItem = document.createElement('div')
            fileItem.className = 'file-item'
            fileItem.dataset.fileName = filePath

            // 从路径中提取文件名
            const fileName = filePath.split('/').pop()
            const fileExtension = fileName.split('.').pop().toLowerCase()
            let fileIcon = 'fas fa-file'

            if (fileExtension === 'md') {
              fileIcon = 'fab fa-markdown'
            } else if (fileExtension === 'pdf') {
              fileIcon = 'fas fa-file-pdf'
            } else if (['png', 'jpg', 'jpeg', 'gif'].includes(fileExtension)) {
              fileIcon = 'fas fa-image'
            } else if (fileExtension === 'chm') {
              fileIcon = 'fas fa-book'
            }

            fileItem.innerHTML = `
                        <i class="file-icon ${fileIcon}"></i>
                        <span class="file-name">${fileName}</span>
                    `

            fileItem.addEventListener('click', () => openFile(filePath))
            fileList.appendChild(fileItem)
          })

          categoryHeader.addEventListener('click', () => {
            categoryDiv.classList.toggle('collapsed')
          })

          categoryDiv.appendChild(categoryHeader)
          categoryDiv.appendChild(fileList)
          fileTree.appendChild(categoryDiv)
        })
      }

      // 设置事件监听器
      function setupEventListeners() {
        // 搜索功能
        const searchInput = document.getElementById('search-input')
        searchInput.addEventListener('input', handleSearch)

        // 目录切换按钮
        const tocToggleBtn = document.getElementById('toc-toggle-btn')
        tocToggleBtn.addEventListener('click', toggleToc)

        // 目录关闭按钮
        const tocCloseBtn = document.getElementById('toc-close-btn')
        tocCloseBtn.addEventListener('click', hideToc)

        // 下载按钮
        const downloadBtn = document.getElementById('download-btn')
        downloadBtn.addEventListener('click', downloadCurrentFile)

        // 全屏按钮
        const fullscreenBtn = document.getElementById('fullscreen-btn')
        fullscreenBtn.addEventListener('click', toggleFullscreen)
      }

      // 搜索处理
      function handleSearch(event) {
        const searchTerm = event.target.value.toLowerCase()
        const fileItems = document.querySelectorAll('.file-item')

        fileItems.forEach((item) => {
          const filePath = item.dataset.fileName.toLowerCase()
          const fileName = filePath.split('/').pop() // 只搜索文件名，不包括路径
          const isVisible = fileName.includes(searchTerm)
          item.style.display = isVisible ? 'flex' : 'none'
        })

        // 展开包含搜索结果的分类
        if (searchTerm) {
          document.querySelectorAll('.category').forEach((category) => {
            const hasVisibleFiles =
              category.querySelectorAll('.file-item[style*="flex"]').length > 0
            if (hasVisibleFiles) {
              category.classList.remove('collapsed')
            }
          })
        }
      }

      // 打开文件
      async function openFile(fileName) {
        // 更新选中状态
        document.querySelectorAll('.file-item').forEach((item) => {
          item.classList.remove('active')
        })
        document.querySelector(`[data-file-name="${fileName}"]`).classList.add('active')

        currentFile = fileName
        const contentTitle = document.getElementById('content-title')
        const contentViewer = document.getElementById('content-viewer')
        const downloadBtn = document.getElementById('download-btn')
        const fullscreenBtn = document.getElementById('fullscreen-btn')
        const tocToggleBtn = document.getElementById('toc-toggle-btn')

        // 只显示文件名，不显示路径
        const displayName = fileName.split('/').pop()
        contentTitle.textContent = displayName
        downloadBtn.style.display = 'block'
        fullscreenBtn.style.display = 'block'

        // 隐藏目录侧边栏
        hideToc()

        const fileExtension = fileName.split('.').pop().toLowerCase()

        try {
          if (fileExtension === 'md') {
            tocToggleBtn.style.display = 'block'
            await loadMarkdownFile(fileName, contentViewer)
          } else if (fileExtension === 'pdf') {
            tocToggleBtn.style.display = 'none'
            await loadPdfFile(fileName, contentViewer)
          } else if (['png', 'jpg', 'jpeg', 'gif'].includes(fileExtension)) {
            tocToggleBtn.style.display = 'none'
            loadImageFile(fileName, contentViewer)
          } else {
            tocToggleBtn.style.display = 'none'
            contentViewer.innerHTML = `
                        <div class="welcome">
                            <i class="fas fa-exclamation-triangle"></i>
                            <h2>不支持的文件格式</h2>
                            <p>暂不支持预览 .${fileExtension} 格式的文件</p>
                            <a href="${fileName}" target="_blank" class="btn">在新窗口中打开</a>
                        </div>
                    `
          }
        } catch (error) {
          console.error('加载文件失败:', error)
          contentViewer.innerHTML = `
                    <div class="welcome">
                        <i class="fas fa-exclamation-triangle"></i>
                        <h2>加载失败</h2>
                        <p>无法加载文件: ${fileName}</p>
                    </div>
                `
        }
      }

      // 加载 Markdown 文件
      async function loadMarkdownFile(fileName, container) {
        try {
          const response = await fetch(fileName)
          if (!response.ok) throw new Error('文件不存在')

          const text = await response.text()
          const html = marked.parse(text)

          container.innerHTML = `<div class="markdown-body markdown-content">${html}</div>`

          // 为代码块添加复制功能
          addCodeCopyButtons()

          // 生成目录
          generateTableOfContents()

          // 设置滚动监听
          setupScrollSpy()
        } catch (error) {
          throw new Error(`加载 Markdown 文件失败: ${error.message}`)
        }
      }

      // 加载 PDF 文件
      async function loadPdfFile(fileName, container) {
        try {
          // 设置 PDF.js worker
          pdfjsLib.GlobalWorkerOptions.workerSrc =
            'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js'

          const loadingTask = pdfjsLib.getDocument(fileName)
          currentPdfDoc = await loadingTask.promise
          currentPageNum = 1

          container.innerHTML = `
                    <div class="pdf-viewer">
                        <div class="pdf-controls">
                            <button class="btn" id="prev-page">
                                <i class="fas fa-chevron-left"></i> 上一页
                            </button>
                            <span id="page-info">第 1 页 / ${currentPdfDoc.numPages} 页</span>
                            <button class="btn" id="next-page">
                                <i class="fas fa-chevron-right"></i> 下一页
                            </button>
                            <button class="btn" id="zoom-out">
                                <i class="fas fa-search-minus"></i>
                            </button>
                            <span id="zoom-level">100%</span>
                            <button class="btn" id="zoom-in">
                                <i class="fas fa-search-plus"></i>
                            </button>
                        </div>
                        <div class="pdf-canvas-container">
                            <canvas id="pdf-canvas"></canvas>
                        </div>
                    </div>
                `

          // 设置PDF控制事件
          setupPdfControls()
          await renderPdfPage(currentPageNum)
        } catch (error) {
          throw new Error(`加载 PDF 文件失败: ${error.message}`)
        }
      }

      // 设置PDF控制器
      function setupPdfControls() {
        let scale = 1.0

        document.getElementById('prev-page').addEventListener('click', async () => {
          if (currentPageNum > 1) {
            currentPageNum--
            await renderPdfPage(currentPageNum)
            updatePageInfo()
          }
        })

        document.getElementById('next-page').addEventListener('click', async () => {
          if (currentPageNum < currentPdfDoc.numPages) {
            currentPageNum++
            await renderPdfPage(currentPageNum)
            updatePageInfo()
          }
        })

        document.getElementById('zoom-in').addEventListener('click', async () => {
          scale = Math.min(scale + 0.25, 3.0)
          await renderPdfPage(currentPageNum, scale)
          document.getElementById('zoom-level').textContent = Math.round(scale * 100) + '%'
        })

        document.getElementById('zoom-out').addEventListener('click', async () => {
          scale = Math.max(scale - 0.25, 0.5)
          await renderPdfPage(currentPageNum, scale)
          document.getElementById('zoom-level').textContent = Math.round(scale * 100) + '%'
        })

        function updatePageInfo() {
          document.getElementById(
            'page-info'
          ).textContent = `第 ${currentPageNum} 页 / ${currentPdfDoc.numPages} 页`
        }
      }

      // 渲染PDF页面
      async function renderPdfPage(pageNum, scale = 1.0) {
        const page = await currentPdfDoc.getPage(pageNum)
        const canvas = document.getElementById('pdf-canvas')
        const context = canvas.getContext('2d')

        const viewport = page.getViewport({ scale })
        canvas.height = viewport.height
        canvas.width = viewport.width

        const renderContext = {
          canvasContext: context,
          viewport: viewport,
        }

        await page.render(renderContext).promise
      }

      // 加载图片文件
      function loadImageFile(fileName, container) {
        container.innerHTML = `
                <div style="text-align: center; padding: 20px;">
                    <img src="${fileName}" alt="${fileName}" style="max-width: 100%; max-height: 80vh; border: 1px solid #e1e4e8; border-radius: 6px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
                </div>
            `
      }

      // 为代码块添加复制按钮
      function addCodeCopyButtons() {
        const codeBlocks = document.querySelectorAll('pre code')
        codeBlocks.forEach((codeBlock) => {
          const pre = codeBlock.parentElement
          const copyButton = document.createElement('button')
          copyButton.className = 'btn'
          copyButton.style.cssText = `
                    position: absolute;
                    top: 8px;
                    right: 8px;
                    font-size: 12px;
                    padding: 4px 8px;
                `
          copyButton.innerHTML = '<i class="fas fa-copy"></i> 复制'

          copyButton.addEventListener('click', async () => {
            try {
              await navigator.clipboard.writeText(codeBlock.textContent)
              copyButton.innerHTML = '<i class="fas fa-check"></i> 已复制'
              setTimeout(() => {
                copyButton.innerHTML = '<i class="fas fa-copy"></i> 复制'
              }, 2000)
            } catch (err) {
              console.error('复制失败:', err)
            }
          })

          pre.style.position = 'relative'
          pre.appendChild(copyButton)
        })
      }

      // 下载当前文件
      function downloadCurrentFile() {
        if (currentFile) {
          const link = document.createElement('a')
          link.href = currentFile
          link.download = currentFile
          link.click()
        }
      }

      // 切换全屏
      function toggleFullscreen() {
        const contentViewer = document.getElementById('content-viewer')
        if (!document.fullscreenElement) {
          contentViewer.requestFullscreen().catch((err) => {
            console.error('无法进入全屏模式:', err)
          })
        } else {
          document.exitFullscreen()
        }
      }

      // 生成目录
      function generateTableOfContents() {
        const contentViewer = document.getElementById('content-viewer')
        const headings = contentViewer.querySelectorAll('h1, h2, h3, h4, h5, h6')
        const tocContent = document.getElementById('toc-content')

        if (headings.length === 0) {
          tocContent.innerHTML =
            '<div style="padding: 15px; color: #586069; text-align: center;">此文档没有标题</div>'
          return
        }

        // 为每个标题添加ID
        headings.forEach((heading, index) => {
          if (!heading.id) {
            heading.id = `heading-${index}`
          }
        })

        // 构建层级结构
        const tocStructure = buildTocStructure(headings)

        // 生成目录HTML
        const tocHtml = generateTocHtml(tocStructure)
        tocContent.innerHTML = tocHtml

        // 设置目录宽度自适应
        adjustTocWidth()

        // 为目录项添加点击事件
        setupTocEvents()
      }

      // 构建目录层级结构
      function buildTocStructure(headings) {
        const structure = []
        const stack = []

        headings.forEach((heading) => {
          const level = parseInt(heading.tagName.charAt(1))
          const text = heading.textContent.trim()
          const id = heading.id

          const item = {
            level,
            text,
            id,
            children: [],
          }

          // 找到合适的父级
          while (stack.length > 0 && stack[stack.length - 1].level >= level) {
            stack.pop()
          }

          if (stack.length === 0) {
            structure.push(item)
          } else {
            stack[stack.length - 1].children.push(item)
          }

          stack.push(item)
        })

        return structure
      }

      // 生成目录HTML
      function generateTocHtml(structure, parentLevel = 0) {
        let html = ''

        structure.forEach((item) => {
          const hasChildren = item.children.length > 0
          const toggleIcon = hasChildren
            ? '<i class="fas fa-chevron-down toc-toggle"></i>'
            : '<span class="toc-toggle"></span>'

          html += `
            <div class="toc-item-container">
              <a href="#${item.id}" class="toc-item level-${item.level}" data-target="${
            item.id
          }" data-has-children="${hasChildren}">
                ${toggleIcon}
                <span class="toc-text" title="${item.text}">${item.text}</span>
              </a>
              ${
                hasChildren
                  ? `<div class="toc-children">${generateTocHtml(item.children, item.level)}</div>`
                  : ''
              }
            </div>
          `
        })

        return html
      }

      // 调整目录宽度
      function adjustTocWidth() {
        const tocSidebar = document.getElementById('toc-sidebar')
        const tocItems = tocSidebar.querySelectorAll('.toc-text')

        let maxWidth = 200 // 最小宽度

        tocItems.forEach((item) => {
          // 创建临时元素测量文本宽度
          const temp = document.createElement('span')
          temp.style.visibility = 'hidden'
          temp.style.position = 'absolute'
          temp.style.fontSize = '13px'
          temp.style.fontFamily = getComputedStyle(item).fontFamily
          temp.textContent = item.textContent
          document.body.appendChild(temp)

          const textWidth = temp.offsetWidth + 60 // 加上padding和图标空间
          maxWidth = Math.max(maxWidth, textWidth)

          document.body.removeChild(temp)
        })

        // 限制最大宽度
        maxWidth = Math.min(maxWidth, 400)
        tocSidebar.style.width = maxWidth + 'px'
      }

      // 设置目录事件
      function setupTocEvents() {
        const tocItems = document.querySelectorAll('.toc-item')

        tocItems.forEach((item) => {
          item.addEventListener('click', (e) => {
            e.preventDefault()

            const hasChildren = item.dataset.hasChildren === 'true'
            const toggle = item.querySelector('.toc-toggle')

            // 如果点击的是有子项的标题，处理折叠
            if (hasChildren && e.target === toggle) {
              const container = item.closest('.toc-item-container')
              const children = container.querySelector('.toc-children')

              if (children.style.display === 'none') {
                children.style.display = 'block'
                toggle.classList.remove('collapsed')
              } else {
                children.style.display = 'none'
                toggle.classList.add('collapsed')
              }
              return
            }

            // 正常的跳转逻辑
            const targetId = item.dataset.target
            const targetElement = document.getElementById(targetId)
            if (targetElement) {
              targetElement.scrollIntoView({
                behavior: 'smooth',
                block: 'start',
              })

              // 更新活动状态
              tocItems.forEach((t) => t.classList.remove('active'))
              item.classList.add('active')
            }
          })

          // 为折叠图标添加单独的点击事件
          const toggle = item.querySelector('.toc-toggle')
          if (toggle && item.dataset.hasChildren === 'true') {
            toggle.addEventListener('click', (e) => {
              e.stopPropagation()
              e.preventDefault()

              const container = item.closest('.toc-item-container')
              const children = container.querySelector('.toc-children')

              if (children.style.display === 'none') {
                children.style.display = 'block'
                toggle.classList.remove('collapsed')
              } else {
                children.style.display = 'none'
                toggle.classList.add('collapsed')
              }
            })
          }
        })
      }

      // 设置滚动监听
      function setupScrollSpy() {
        const contentViewer = document.getElementById('content-viewer')
        const headings = contentViewer.querySelectorAll('h1, h2, h3, h4, h5, h6')
        const tocItems = document.querySelectorAll('.toc-item')

        if (headings.length === 0) return

        const observer = new IntersectionObserver(
          (entries) => {
            entries.forEach((entry) => {
              if (entry.isIntersecting) {
                const id = entry.target.id
                const activeItem = document.querySelector(`.toc-item[data-target="${id}"]`)

                if (activeItem) {
                  tocItems.forEach((item) => item.classList.remove('active'))
                  activeItem.classList.add('active')
                }
              }
            })
          },
          {
            rootMargin: '-20% 0px -70% 0px',
            threshold: 0,
          }
        )

        headings.forEach((heading) => {
          observer.observe(heading)
        })
      }

      // 切换目录显示
      function toggleToc() {
        const tocSidebar = document.getElementById('toc-sidebar')
        tocSidebar.classList.toggle('show')
      }

      // 隐藏目录
      function hideToc() {
        const tocSidebar = document.getElementById('toc-sidebar')
        tocSidebar.classList.remove('show')
      }

      // 页面加载完成后初始化
      document.addEventListener('DOMContentLoaded', initApp)
    </script>
  </body>
</html>

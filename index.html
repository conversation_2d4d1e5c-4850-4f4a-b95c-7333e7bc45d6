<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width, initial-scale=1.0" />
    <title>我的学习笔记库</title>
    <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/pdf-lib@1.17.1/dist/pdf-lib.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js"></script>
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/github-markdown-css/5.2.0/github-markdown.min.css" />
    <link
      rel="stylesheet"
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" />
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        background-color: #f8f9fa;
        height: 100vh;
        overflow: hidden;
      }

      .container {
        display: flex;
        height: 100vh;
      }

      /* 侧边栏样式 */
      .sidebar {
        width: 300px;
        background: #fff;
        border-right: 1px solid #e1e4e8;
        display: flex;
        flex-direction: column;
        box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
      }

      .sidebar-header {
        padding: 20px;
        border-bottom: 1px solid #e1e4e8;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
      }

      .sidebar-header h1 {
        font-size: 1.5rem;
        margin-bottom: 8px;
      }

      .sidebar-header p {
        opacity: 0.9;
        font-size: 0.9rem;
      }

      .search-box {
        padding: 15px;
        border-bottom: 1px solid #e1e4e8;
      }

      .search-input {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #d1d5da;
        border-radius: 6px;
        font-size: 14px;
      }

      .search-input:focus {
        outline: none;
        border-color: #0366d6;
        box-shadow: 0 0 0 3px rgba(3, 102, 214, 0.1);
      }

      .file-tree {
        flex: 1;
        overflow-y: auto;
        padding: 10px 0;
      }

      .category {
        margin-bottom: 10px;
      }

      .category-header {
        padding: 8px 20px;
        background: #f6f8fa;
        border-left: 3px solid #0366d6;
        font-weight: 600;
        color: #24292e;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .category-header:hover {
        background: #e1e4e8;
      }

      .category-header i {
        transition: transform 0.2s;
      }

      .category.collapsed .category-header i {
        transform: rotate(-90deg);
      }

      .file-list {
        display: block;
        transition: all 0.3s ease;
      }

      .category.collapsed .file-list {
        display: none;
      }

      .file-item {
        padding: 8px 20px 8px 40px;
        cursor: pointer;
        display: flex;
        align-items: center;
        transition: background-color 0.2s;
        border-left: 3px solid transparent;
      }

      .file-item:hover {
        background: #f6f8fa;
        border-left-color: #0366d6;
      }

      .file-item.active {
        background: #e3f2fd;
        border-left-color: #0366d6;
        color: #0366d6;
      }

      .file-icon {
        margin-right: 8px;
        width: 16px;
        text-align: center;
      }

      .file-name {
        flex: 1;
        font-size: 14px;
      }

      /* 主内容区域 */
      .main-content {
        flex: 1;
        display: flex;
        flex-direction: column;
        background: #fff;
      }

      .content-header {
        padding: 15px 20px;
        border-bottom: 1px solid #e1e4e8;
        background: #fff;
        display: flex;
        align-items: center;
        justify-content: space-between;
      }

      .content-title {
        font-size: 1.2rem;
        font-weight: 600;
        color: #24292e;
      }

      .content-actions {
        display: flex;
        gap: 10px;
      }

      .btn {
        padding: 6px 12px;
        border: 1px solid #d1d5da;
        border-radius: 6px;
        background: #fff;
        color: #24292e;
        text-decoration: none;
        font-size: 12px;
        cursor: pointer;
        transition: all 0.2s;
      }

      .btn:hover {
        background: #f6f8fa;
        border-color: #0366d6;
      }

      .content-viewer {
        flex: 1;
        overflow: auto;
        padding: 20px;
      }

      /* Markdown 样式 */
      .markdown-content {
        max-width: none;
        padding: 0;
      }

      /* PDF 查看器样式 */
      .pdf-viewer {
        display: flex;
        flex-direction: column;
        height: 100%;
      }

      .pdf-controls {
        padding: 10px;
        background: #f6f8fa;
        border-bottom: 1px solid #e1e4e8;
        display: flex;
        align-items: center;
        gap: 10px;
      }

      .pdf-canvas-container {
        flex: 1;
        overflow: auto;
        text-align: center;
        padding: 20px;
      }

      #pdf-canvas {
        border: 1px solid #e1e4e8;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      }

      /* 欢迎页面 */
      .welcome {
        text-align: center;
        padding: 60px 20px;
        color: #586069;
      }

      .welcome i {
        font-size: 4rem;
        margin-bottom: 20px;
        color: #d1d5da;
      }

      .welcome h2 {
        margin-bottom: 10px;
        color: #24292e;
      }

      /* 响应式设计 */
      @media (max-width: 768px) {
        .sidebar {
          width: 250px;
        }

        .content-viewer {
          padding: 15px;
        }
      }

      /* 滚动条样式 */
      ::-webkit-scrollbar {
        width: 8px;
      }

      ::-webkit-scrollbar-track {
        background: #f1f1f1;
      }

      ::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 4px;
      }

      ::-webkit-scrollbar-thumb:hover {
        background: #a1a1a1;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <!-- 侧边栏 -->
      <div class="sidebar">
        <div class="sidebar-header">
          <h1><i class="fas fa-book"></i> 学习笔记库</h1>
          <p>技术文档与学习资料</p>
        </div>

        <div class="search-box">
          <input
            type="text"
            class="search-input"
            placeholder="搜索文档..."
            id="search-input" />
        </div>

        <div
          class="file-tree"
          id="file-tree">
          <!-- 文件树将通过 JavaScript 生成 -->
        </div>
      </div>

      <!-- 主内容区域 -->
      <div class="main-content">
        <div class="content-header">
          <div
            class="content-title"
            id="content-title">
            选择一个文档开始阅读
          </div>
          <div class="content-actions">
            <button
              class="btn"
              id="download-btn"
              style="display: none">
              <i class="fas fa-download"></i> 下载
            </button>
            <button
              class="btn"
              id="fullscreen-btn"
              style="display: none">
              <i class="fas fa-expand"></i> 全屏
            </button>
          </div>
        </div>

        <div
          class="content-viewer"
          id="content-viewer">
          <div class="welcome">
            <i class="fas fa-file-alt"></i>
            <h2>欢迎使用文档查看器</h2>
            <p>从左侧选择一个文档开始阅读</p>
          </div>
        </div>
      </div>
    </div>
    <script>
      // 文件分类配置
      const fileCategories = {
        前端技术: {
          icon: 'fab fa-js-square',
          files: [
            'HTML.md',
            'CSS.md',
            'JaveScript.md',
            'Typescript.md',
            'Vue.md',
            'React.md',
            'Nuxt.md',
            'tailwindcss.md',
            'cropperjs.md',
          ],
        },
        后端技术: {
          icon: 'fas fa-server',
          files: [
            'Node.js.md',
            'Nest.md',
            'Python.md',
            'MongoDB文档查询技巧.md',
            'MongoDB.pdf',
            'Redis.pdf',
            'SQL.pdf',
          ],
        },
        开发工具: {
          icon: 'fas fa-tools',
          files: ['GIT.md', 'Git.pdf', 'Docker.pdf', 'Nginx.pdf', 'linux.md', 'OpenSSL.md'],
        },
        移动开发: {
          icon: 'fas fa-mobile-alt',
          files: ['HarmonyOS.md'],
        },
        开发规范: {
          icon: 'fas fa-clipboard-list',
          files: [
            'JavaScript 风格指南.md',
            'Web前端开发规范手册.pdf',
            'W3C.chm',
            'css手册4.2.3.chm',
          ],
        },
        项目管理: {
          icon: 'fas fa-project-diagram',
          files: ['项目创建流程与相关库配置.md', '前后端交互.md', 'TS配置文件详解.md'],
        },
        学习资料: {
          icon: 'fas fa-graduation-cap',
          files: ['专业术语.md', '数据库.md', '电脑证书信任管理.md', 'README.md'],
        },
        其他资源: {
          icon: 'fas fa-folder',
          files: ['CSS层叠样式表.png', '学会提问.png'],
        },
      }

      // 当前选中的文件
      let currentFile = null
      let currentPdfDoc = null
      let currentPageNum = 1

      // 初始化应用
      function initApp() {
        renderFileTree()
        setupEventListeners()
      }

      // 渲染文件树
      function renderFileTree() {
        const fileTree = document.getElementById('file-tree')
        fileTree.innerHTML = ''

        Object.entries(fileCategories).forEach(([categoryName, categoryData]) => {
          const categoryDiv = document.createElement('div')
          categoryDiv.className = 'category'

          const categoryHeader = document.createElement('div')
          categoryHeader.className = 'category-header'
          categoryHeader.innerHTML = `
                    <span><i class="${categoryData.icon}"></i> ${categoryName}</span>
                    <i class="fas fa-chevron-down"></i>
                `

          const fileList = document.createElement('div')
          fileList.className = 'file-list'

          categoryData.files.forEach((fileName) => {
            const fileItem = document.createElement('div')
            fileItem.className = 'file-item'
            fileItem.dataset.fileName = fileName

            const fileExtension = fileName.split('.').pop().toLowerCase()
            let fileIcon = 'fas fa-file'

            if (fileExtension === 'md') {
              fileIcon = 'fab fa-markdown'
            } else if (fileExtension === 'pdf') {
              fileIcon = 'fas fa-file-pdf'
            } else if (['png', 'jpg', 'jpeg', 'gif'].includes(fileExtension)) {
              fileIcon = 'fas fa-image'
            } else if (fileExtension === 'chm') {
              fileIcon = 'fas fa-book'
            }

            fileItem.innerHTML = `
                        <i class="file-icon ${fileIcon}"></i>
                        <span class="file-name">${fileName}</span>
                    `

            fileItem.addEventListener('click', () => openFile(fileName))
            fileList.appendChild(fileItem)
          })

          categoryHeader.addEventListener('click', () => {
            categoryDiv.classList.toggle('collapsed')
          })

          categoryDiv.appendChild(categoryHeader)
          categoryDiv.appendChild(fileList)
          fileTree.appendChild(categoryDiv)
        })
      }

      // 设置事件监听器
      function setupEventListeners() {
        // 搜索功能
        const searchInput = document.getElementById('search-input')
        searchInput.addEventListener('input', handleSearch)

        // 下载按钮
        const downloadBtn = document.getElementById('download-btn')
        downloadBtn.addEventListener('click', downloadCurrentFile)

        // 全屏按钮
        const fullscreenBtn = document.getElementById('fullscreen-btn')
        fullscreenBtn.addEventListener('click', toggleFullscreen)
      }

      // 搜索处理
      function handleSearch(event) {
        const searchTerm = event.target.value.toLowerCase()
        const fileItems = document.querySelectorAll('.file-item')

        fileItems.forEach((item) => {
          const fileName = item.dataset.fileName.toLowerCase()
          const isVisible = fileName.includes(searchTerm)
          item.style.display = isVisible ? 'flex' : 'none'
        })

        // 展开包含搜索结果的分类
        if (searchTerm) {
          document.querySelectorAll('.category').forEach((category) => {
            const hasVisibleFiles =
              category.querySelectorAll('.file-item[style*="flex"]').length > 0
            if (hasVisibleFiles) {
              category.classList.remove('collapsed')
            }
          })
        }
      }

      // 打开文件
      async function openFile(fileName) {
        // 更新选中状态
        document.querySelectorAll('.file-item').forEach((item) => {
          item.classList.remove('active')
        })
        document.querySelector(`[data-file-name="${fileName}"]`).classList.add('active')

        currentFile = fileName
        const contentTitle = document.getElementById('content-title')
        const contentViewer = document.getElementById('content-viewer')
        const downloadBtn = document.getElementById('download-btn')
        const fullscreenBtn = document.getElementById('fullscreen-btn')

        contentTitle.textContent = fileName
        downloadBtn.style.display = 'block'
        fullscreenBtn.style.display = 'block'

        const fileExtension = fileName.split('.').pop().toLowerCase()

        try {
          if (fileExtension === 'md') {
            await loadMarkdownFile(fileName, contentViewer)
          } else if (fileExtension === 'pdf') {
            await loadPdfFile(fileName, contentViewer)
          } else if (['png', 'jpg', 'jpeg', 'gif'].includes(fileExtension)) {
            loadImageFile(fileName, contentViewer)
          } else {
            contentViewer.innerHTML = `
                        <div class="welcome">
                            <i class="fas fa-exclamation-triangle"></i>
                            <h2>不支持的文件格式</h2>
                            <p>暂不支持预览 .${fileExtension} 格式的文件</p>
                            <a href="${fileName}" target="_blank" class="btn">在新窗口中打开</a>
                        </div>
                    `
          }
        } catch (error) {
          console.error('加载文件失败:', error)
          contentViewer.innerHTML = `
                    <div class="welcome">
                        <i class="fas fa-exclamation-triangle"></i>
                        <h2>加载失败</h2>
                        <p>无法加载文件: ${fileName}</p>
                    </div>
                `
        }
      }

      // 加载 Markdown 文件
      async function loadMarkdownFile(fileName, container) {
        try {
          const response = await fetch(fileName)
          if (!response.ok) throw new Error('文件不存在')

          const text = await response.text()
          const html = marked.parse(text)

          container.innerHTML = `<div class="markdown-body markdown-content">${html}</div>`

          // 为代码块添加复制功能
          addCodeCopyButtons()
        } catch (error) {
          throw new Error(`加载 Markdown 文件失败: ${error.message}`)
        }
      }

      // 加载 PDF 文件
      async function loadPdfFile(fileName, container) {
        try {
          // 设置 PDF.js worker
          pdfjsLib.GlobalWorkerOptions.workerSrc =
            'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js'

          const loadingTask = pdfjsLib.getDocument(fileName)
          currentPdfDoc = await loadingTask.promise
          currentPageNum = 1

          container.innerHTML = `
                    <div class="pdf-viewer">
                        <div class="pdf-controls">
                            <button class="btn" id="prev-page">
                                <i class="fas fa-chevron-left"></i> 上一页
                            </button>
                            <span id="page-info">第 1 页 / ${currentPdfDoc.numPages} 页</span>
                            <button class="btn" id="next-page">
                                <i class="fas fa-chevron-right"></i> 下一页
                            </button>
                            <button class="btn" id="zoom-out">
                                <i class="fas fa-search-minus"></i>
                            </button>
                            <span id="zoom-level">100%</span>
                            <button class="btn" id="zoom-in">
                                <i class="fas fa-search-plus"></i>
                            </button>
                        </div>
                        <div class="pdf-canvas-container">
                            <canvas id="pdf-canvas"></canvas>
                        </div>
                    </div>
                `

          // 设置PDF控制事件
          setupPdfControls()
          await renderPdfPage(currentPageNum)
        } catch (error) {
          throw new Error(`加载 PDF 文件失败: ${error.message}`)
        }
      }

      // 设置PDF控制器
      function setupPdfControls() {
        let scale = 1.0

        document.getElementById('prev-page').addEventListener('click', async () => {
          if (currentPageNum > 1) {
            currentPageNum--
            await renderPdfPage(currentPageNum)
            updatePageInfo()
          }
        })

        document.getElementById('next-page').addEventListener('click', async () => {
          if (currentPageNum < currentPdfDoc.numPages) {
            currentPageNum++
            await renderPdfPage(currentPageNum)
            updatePageInfo()
          }
        })

        document.getElementById('zoom-in').addEventListener('click', async () => {
          scale = Math.min(scale + 0.25, 3.0)
          await renderPdfPage(currentPageNum, scale)
          document.getElementById('zoom-level').textContent = Math.round(scale * 100) + '%'
        })

        document.getElementById('zoom-out').addEventListener('click', async () => {
          scale = Math.max(scale - 0.25, 0.5)
          await renderPdfPage(currentPageNum, scale)
          document.getElementById('zoom-level').textContent = Math.round(scale * 100) + '%'
        })

        function updatePageInfo() {
          document.getElementById(
            'page-info'
          ).textContent = `第 ${currentPageNum} 页 / ${currentPdfDoc.numPages} 页`
        }
      }

      // 渲染PDF页面
      async function renderPdfPage(pageNum, scale = 1.0) {
        const page = await currentPdfDoc.getPage(pageNum)
        const canvas = document.getElementById('pdf-canvas')
        const context = canvas.getContext('2d')

        const viewport = page.getViewport({ scale })
        canvas.height = viewport.height
        canvas.width = viewport.width

        const renderContext = {
          canvasContext: context,
          viewport: viewport,
        }

        await page.render(renderContext).promise
      }

      // 加载图片文件
      function loadImageFile(fileName, container) {
        container.innerHTML = `
                <div style="text-align: center; padding: 20px;">
                    <img src="${fileName}" alt="${fileName}" style="max-width: 100%; max-height: 80vh; border: 1px solid #e1e4e8; border-radius: 6px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
                </div>
            `
      }

      // 为代码块添加复制按钮
      function addCodeCopyButtons() {
        const codeBlocks = document.querySelectorAll('pre code')
        codeBlocks.forEach((codeBlock) => {
          const pre = codeBlock.parentElement
          const copyButton = document.createElement('button')
          copyButton.className = 'btn'
          copyButton.style.cssText = `
                    position: absolute;
                    top: 8px;
                    right: 8px;
                    font-size: 12px;
                    padding: 4px 8px;
                `
          copyButton.innerHTML = '<i class="fas fa-copy"></i> 复制'

          copyButton.addEventListener('click', async () => {
            try {
              await navigator.clipboard.writeText(codeBlock.textContent)
              copyButton.innerHTML = '<i class="fas fa-check"></i> 已复制'
              setTimeout(() => {
                copyButton.innerHTML = '<i class="fas fa-copy"></i> 复制'
              }, 2000)
            } catch (err) {
              console.error('复制失败:', err)
            }
          })

          pre.style.position = 'relative'
          pre.appendChild(copyButton)
        })
      }

      // 下载当前文件
      function downloadCurrentFile() {
        if (currentFile) {
          const link = document.createElement('a')
          link.href = currentFile
          link.download = currentFile
          link.click()
        }
      }

      // 切换全屏
      function toggleFullscreen() {
        const contentViewer = document.getElementById('content-viewer')
        if (!document.fullscreenElement) {
          contentViewer.requestFullscreen().catch((err) => {
            console.error('无法进入全屏模式:', err)
          })
        } else {
          document.exitFullscreen()
        }
      }

      // 页面加载完成后初始化
      document.addEventListener('DOMContentLoaded', initApp)
    </script>
  </body>
</html>

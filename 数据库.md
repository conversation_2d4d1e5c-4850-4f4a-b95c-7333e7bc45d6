# MongoDB

## 简介

MongoDB 是一个基于分布式文件存储的数据库。

[安装](https://www.runoob.com/mongodb/mongodb-window-install.html)

## 命令行交互

### 数据库命令

1. 显示所有数据库：`show dbs`
2. 切换到指定数据库：`use 数据库名称`
3. 显示当前数据库：`db`
4. 删除当前数据库：`db.dropDatabase()`

### 集合命令

1. 显示当前数据库的所有集合：`show collections`
2. 创建集合：`db.createCollection("集合名称")`
3. 删除集合：`db.集合名称.drop()`
4. 重命名集合：`db.集合名称.renameCollection("新集合名称")`

### 文档命令

1. 插入文档：`db.集合名称.insert(文档对象)`
2. 查询文档：`db.集合名称.find(查询条件)`
3. 更新文档：`db.集合名称.update(查询条件, 更新内容)` 例如: `db.集合名称.update({name: "张三"}, {$set: {age: 20})`(不加$set, 则为覆盖式更新)
4. 删除文档：`db.集合名称.remove(查询条件)`

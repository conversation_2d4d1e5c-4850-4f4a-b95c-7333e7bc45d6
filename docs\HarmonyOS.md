# ArkTs 组件
## 组件通用信息
### 通用事件
#### onClick 点击事件
```ets
@Entry
@Component
struct test {
  build() {
    Button("点我")
      .onClick((e)=>{
        console.log(String(e.windowX))
      })
  }
}
```
##### event:ClickEvent 参数1-事件对象
###### x
点击位置相对于被点击元素左边缘的X坐标。
###### y
点击位置相对于被点击元素原始区域左上角的Y坐标。
###### target
触发事件的元素对象显示区域。
```ets
    Button("点我")
      .onClick((e)=>{
        console.log(String(e.target.area.width))
        console.log(String(e.target.area.height))
        console.log(String(e.target.area.position))
        console.log(String(e.target.area.globalPosition))
      })
```
###### windowX
点击位置相对于应用窗口左上角的X坐标。
###### windowY
点击位置相对于应用窗口左上角的Y坐标。
###### displayX
点击位置相对于应用屏幕左上角的X坐标。
###### displayY
点击位置相对于应用屏幕左上角的Y坐标。
###### preventDefault
阻止默认事件。
##### distanceThreshold:number 参数2-点击事件移动阈值。
- 点击事件移动阈值。当设置的值小于等于0时，会被转化为默认值。
- 默认值：2^31-1
- 说明： 当手指的移动距离超出开发者预设的移动阈值时，点击识别失败。如果初始化为默认阈值时，手指移动超过组件热区范围，点击识别失败。
### 通用属性
#### 尺寸设置
##### width
要设置的组件宽度。
单位：vp
```ets
   Text("六六六花花")
     .width(100)
```
##### height
要设置的组件高度。
单位：vp
```ets
   Text("六六六花花")
     .height(300)
```
##### size
设置高宽尺寸。
```ets
   Text("六六六花花")
     .size({width:100,height:100})
```
##### padding
设置组件的内边距。

参数为Length类型时，四个方向内边距同时生效。

默认值：0

单位：vp

padding设置百分比时，上下左右内边距均以父容器的width作为基础值。
```ets
   Text("六六六花花")
     .padding(20)
```
##### margin
设置组件的外边距。

参数为Length类型时，四个方向外边距同时生效。

默认值：0

单位：vp

margin设置百分比时，上下左右外边距均以父容器的width作为基础值。在Row、Column、Flex交叉轴上布局时，子组件交叉轴的大小与margin的和为整体。

例如Column容器宽100，其中子组件宽50，margin left为10，right为20，子组件水平方向偏移10。
```ets
   Text("六六六花花")
     .margin(20)
```
##### layoutWeight
父容器尺寸确定时，设置了layoutWeight属性的子元素与兄弟元素占主轴尺寸按照权重进行分配，忽略元素本身尺寸设置，表示自适应占满剩余空间。

仅在Row/Column/Flex布局中生效。

可选值为大于等于0的数字，或者可以转换为数字的字符串。

如果容器中有子元素设置了layoutWeight属性，且设置的属性值大于0，则所有子元素不会再基于flexShrink和flexGrow布局。
```ets
 Column(){
     Text("六六六花花")
       .margin(20)
       .layoutWeight(1)
     Text("六六六花花")
       .margin(20)
       .layoutWeight(2)
     Text("六六六花花")
       .margin(20)
       .layoutWeight(3)
   }
```
##### constraintSize
设置约束尺寸。constraintSize的优先级高于Width和Height。取值结果参考constraintSize取值对width/height影响。

默认值：`{
minWidth: 0,
maxWidth: Infinity,
minHeight: 0,
maxHeight: Infinity
}`

单位：vp
```ets
Text("六六六花花")
        .height(1000)
        .constraintSize({ maxHeight: 100 })
```
#### 位置设置
##### align
设置容器元素绘制区域内的子元素的对齐方式。

只在Stack、Button、Marquee、StepperItem、Text、TextArea、TextInput、FolderStack中生效，其中和文本相关的组件Marquee、Text、TextArea、TextInput的align结果参考textAlign。

不支持textAlign属性的组件则无法设置水平方向的文字对齐。

默认值：Alignment.Center

说明：

在Stack中该属性与alignContent效果一致，只能设置子组件在容器内的对齐方式。
```ets
Column() {
      Text("六六六花花")
        .width(300)
        .height(300)
        .align(Alignment.BottomStart)
      Text("六六六花花")
        .width(300)
        .height(300)
        .align(Alignment.TopStart)
    }
```
##### direction 
设置容器元素内**主轴方向**上的布局。

属性配置为auto的时候，按照系统语言方向进行布局。

该属性在Column组件上不生效。

默认值：Direction.Auto
```ets
    Row() {
      Text("六六六花花")
        .height(300)
        .align(Alignment.BottomStart)
      Text("六六六花花")
        .height(300)
        .align(Alignment.TopStart)
    }.direction(Direction.Rtl)
```
##### position
绝对定位，确定子组件相对父组件的位置。当父容器为Row/Column/Flex时，设置position的子组件不占位。

Position类型基于父组件左上角确定位置;Edges类型基于父组件四边确定位置，top/left/right/bottom分别为组件各边距离父组件相应边的边距，通过边距来确定组件相对于父组件的位置;LocalizedEdges类型基于父组件四边确定位置，支持镜像模式。

适用于置顶显示、悬浮按钮等组件在父容器中位置固定的场景。

不支持在宽高为零的布局容器上设置。

当父容器为RelativeContainer, 且子组件设置了alignRules属性, 则子组件的position属性不生效。
```ets
Text("六六六花花")
      .position({
        top: 100,
        left: 100,
        // right: 100,
        // bottom: 100
        // x: 100,
        // y: 100
      })
```
##### markAnchor
设置元素在位置定位时的锚点，从position或offset的位置上，进一步偏移。

设置.position({x: value1, y: value2}).markAnchor({x: value3, y: value4})，效果等于设置.position({x: value1 - value3, y: value2 - value4})，offset同理。

单独使用markAnchor，设置.markAnchor({x: value1, y: value2})，效果等于设置.offset({x: -value1, y: -value2})。

API version 9及以前，默认值为：`{x: 0,y: 0}`

API version 10：无默认值。
```ets
Text("六六六花花")
      .position({
        x: 0,
        y: 0
      })
      .markAnchor({
        x: -100,
        y: -100
      })
```
##### offset
相对偏移，组件相对原本的布局位置进行偏移。offset属性不影响父容器布局，仅在绘制时调整位置。

Position类型基于组件自身左上角偏移，Edges类型基于组件自身四边偏移。 offset属性设置 {x: x, y: y} 与设置 {left: x, top: y} 以及 {right: -x, bottom: -y} 效果相同, 类型LocalizedEdges支持镜像模式：LTR模式下start 等同于x，RTL模式下等同于-x

API version 9及以前，默认值为：`{x: 0,y: 0}`

默认单位：vp

API version 10：无默认值。
```ets
 Column() {
      Text("六六六花花")
        .offset({
          x: 100,
          y: 100
        })
      Text("六六六花花")
    }
```
##### alignRules
指定设置在相对容器中子组件的对齐规则，仅当父容器为RelativeContainer时生效。该方法水平方向上以start和end分别替代原方法的left和right，以便在RTL模式下能镜像显示，建议使用该方法指定设置在相对容器中子组件的对齐规则。
### 手势处理
## 行列与堆叠
## 栅格与分栏
## 滚动与滑动
## 导航与切换
## 按钮与选择
## 文本与输入
## 图片与视频
## 信息展示
## 空白与分隔
## 画布绘制
## 图形绘制
## 渲染绘制
## 标题栏与工具栏
## 菜单
## 动画
## 弹窗
## 卡片
## 安全
## 主题
## 元服务
## 自定义占位组件
## 自定义组件
## 状态管理与渲染控制
## 公共定义
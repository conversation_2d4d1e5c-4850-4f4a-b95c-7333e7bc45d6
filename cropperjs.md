# Cropper.js

## 介绍

Cropper.js 是一个功能强大的 JavaScript 图片裁剪库，提供像素级精度的图像处理能力。支持裁剪、旋转、缩放、翻转等操作，适用于头像编辑、电商图片处理等场景。

## 安装

```bash
pnpm install cropperjs
```

## 节点详解

### CROPPER_CANVAS 编辑器画布

#### 属性

| 属性名     | 类型    | 默认值 | 可选值 | 描述                                         |
| ---------- | ------- | ------ | ------ | -------------------------------------------- |
| background | boolean | false  | -      | 指示此元素是否有网格背景                     |
| disabled   | boolean | false  | -      | 指示此元素是否被禁用                         |
| scaleStep  | number  | 0.1    | -      | 通过滚轮缩放时的缩放因子步进间隔，必须为正数 |
| themeColor | string  | "#39f" | -      | 指示此元素及其子元素的主色                   |

#### 方法

##### $setAction

- **语法**：`$setAction(action)`
- **参数**：
  - `action`：
    - 类型：`string`
    - 要设置的新操作类型。
- **返回值**：
  - 类型：`CropperCanvas`
  - 返回元素实例以支持链式调用。
- **功能**：
  将当前操作切换为指定的新操作。

##### $toCanvas

- **语法**：
  - `$toCanvas()`
  - `$toCanvas(options)`
- **参数**：
  - `options`（可选）：
    - 类型：`Object`
    - 可配置的选项对象，包含以下属性：
      - `width`：
        - 类型：`number`
        - 生成的画布宽度。
      - `height`：
        - 类型：`number`
        - 生成的画布高度。
      - `beforeDraw`：
        - 类型：`Function`
        - 在图像绘制到画布前的回调函数。
        - 函数签名：`beforeDraw(context, canvas)`
        - 参数说明：
          - `context`：
            类型：`CanvasRenderingContext2D`
            画布的 2D 渲染上下文。
          - `canvas`：
            类型：`HTMLCanvasElement`
            画布元素本身。
        - 示例：
          ```javascript
          function(context) {
            context.filter = 'grayscale(100%)';
          }
          ```
- **返回值**：
  - 类型：`Promise`
  - 解析为生成的画布元素（`HTMLCanvasElement`）的 Promise 对象。
- **功能**：
  将当前内容转换为画布元素，支持自定义绘制参数。

#### 事件

##### 1. `action` 事件

**触发时机**: 当画布上的指针状态（鼠标/触摸）发生变化时触发。
**事件属性**:
| 属性 | 类型 | 说明 | 触发条件 |
| --------------------- | -------------------------------------------------- | ------------------------------ | ------------------------------------------------------------------------- |
| `bubbles` | `boolean` | 事件是否冒泡 | 固定值 `true` |
| `cancelable` | `boolean` | 事件是否可取消 | 固定值 `false` |
| `composed` | `boolean` | 事件是否能跨越 Shadow DOM 边界 | 固定值 `true` |
| `detail.action` | `string` | 当前执行的动作类型 | 可能值：`select`, `move`, `scale`, `rotate`, `transform`, `[方向]-resize` |
| `detail.relatedEvent` | `PointerEvent｜TouchEvent｜MouseEvent｜WheelEvent` | 触发此事件的原生事件对象 | 总是存在 |
| `detail.scale` | `number` | 缩放因子 | 仅当 `action` 为 `scale` 或 `transform` 时存在 |
| `detail.rotate` | `number` | 旋转角度 | 仅当 `action` 为 `rotate` 或 `transform` 时存在 |
| `detail.startX` | `number` | 起始点 X 坐标（基于页面） | 仅当 `relatedEvent` 为指针/触摸/鼠标事件时存在 |
| `detail.startY` | `number` | 起始点 Y 坐标（基于页面） | 仅当 `relatedEvent` 为指针/触摸/鼠标事件时存在 |
| `detail.endX` | `number` | 结束点 X 坐标（基于页面） | 仅当 `relatedEvent` 为指针/触摸/鼠标事件时存在 |
| `detail.endY` | `number` | 结束点 Y 坐标（基于页面） | 仅当 `relatedEvent` 为指针/触摸/鼠标事件时存在 |

##### 2. `actionstart` 事件

**触发时机**：指针开始激活时（如鼠标按下/触摸开始）
**事件属性**：
| 属性 | 类型 | 说明 |
|--------------------|-------------------------------------------|----------------------------------------------------------------------|
| `bubbles` | `boolean` | 事件是否冒泡（固定值 `true`） |
| `cancelable` | `boolean` | 事件是否可取消（固定值 `true`） |
| `composed` | `boolean` | 事件是否能跨越 Shadow DOM（固定值 `true`） |
| `detail.action` | `string` | 动作类型（同 `action` 事件，**排除 `"scale"` 类型**） |
| `detail.relatedEvent` | `PointerEvent｜TouchEvent｜MouseEvent` | 触发此事件的原生事件对象 |

---

##### 3. `actionmove` 事件

**触发时机**：指针坐标移动时（如鼠标拖动/触摸移动）
**事件属性**：
| 属性 | 类型 | 说明 |
|--------------------|-------------------------------------------|----------------------------------------------------------------------|
| `bubbles` | `boolean` | 事件是否冒泡（固定值 `true`） |
| `cancelable` | `boolean` | 事件是否可取消（固定值 `true`） |
| `composed` | `boolean` | 事件是否能跨越 Shadow DOM（固定值 `true`） |
| `detail.action` | `string` | 动作类型（同 `action` 事件，**排除 `"scale"` 类型**） |
| `detail.relatedEvent` | `PointerEvent｜TouchEvent｜MouseEvent` | 触发此事件的原生事件对象 |

---

##### 4. `actionend` 事件

**触发时机**：指针结束激活时（如鼠标释放/触摸结束）
**事件属性**：
| 属性 | 类型 | 说明 |
|--------------------|-------------------------------------------|----------------------------------------------------------------------|
| `bubbles` | `boolean` | 事件是否冒泡（固定值 `true`） |
| `cancelable` | `boolean` | 事件是否可取消（固定值 `true`） |
| `composed` | `boolean` | 事件是否能跨越 Shadow DOM（固定值 `true`） |
| `detail.action` | `string` | 动作类型（同 `action` 事件，**排除 `"scale"` 类型**） |
| `detail.relatedEvent` | `PointerEvent｜TouchEvent｜MouseEvent` | 触发此事件的原生事件对象 |

---

##### 使用示例

```html
<cropper-canvas id="canvas"></cropper-canvas>

<script>
  const canvas = document.querySelector('#canvas')

  // 1.监听动作事件
  canvas.addEventListener('action', function (event) {
    console.log('动作类型:', event.detail.action)
    if (event.detail.scale) {
      console.log('缩放比例:', event.detail.scale)
    }
    if (event.detail.rotate) {
      console.log('旋转角度:', event.detail.rotate)
    }
    if (event.detail.startX !== undefined) {
      console.log('起始坐标:', event.detail.startX, event.detail.startY)
      console.log('结束坐标:', event.detail.endX, event.detail.endY)
    }
  })

  // 2. 指针激活时
  canvas.addEventListener('actionstart', (event) => {
    console.log('动作开始:', event.detail.action)
  })

  // 3. 指针移动时
  canvas.addEventListener('actionmove', (event) => {
    console.log('移动中:', event.detail.action)
  })

  // 4. 指针释放时
  canvas.addEventListener('actionend', (event) => {
    console.log('动作结束:', event.detail.action)
  })
</script>
```

### CROPPER_CROSSHAIR 十字准星

| 属性名     | 类型   | 默认值                     | 可选值 | 说明             |
| ---------- | ------ | -------------------------- | ------ | ---------------- |
| centered   | 布尔值 | false                      | -      | 元素是否居中显示 |
| slottable  | 布尔值 | false                      | -      | 元素是否可插槽   |
| themeColor | 字符串 | "rgba(238, 238, 238, 0.5)" | -      | 十字准线颜色     |

### CROPPER_GIRD 辅助网格

| 属性名     | 类型    | 默认值                     | 可选值 | 描述                       |
| ---------- | ------- | -------------------------- | ------ | -------------------------- |
| rows       | number  | 3                          | -      | 指示网格的行数             |
| columns    | number  | 3                          | -      | 指示网格的列数             |
| bordered   | boolean | false                      | -      | 指示此元素是否有边框       |
| covered    | boolean | false                      | -      | 指示此元素是否覆盖其父元素 |
| slottable  | boolean | false                      | -      | 指示此元素是否支持插槽内容 |
| themeColor | string  | "rgba(238, 238, 238, 0.5)" | -      | 指示网格元素的颜色         |

### CROPPER_HANDLE 控制手柄

| 属性名     | 类型    | 默认值                    | 可选值                                                                                                                                | 描述                               |
| ---------- | ------- | ------------------------- | ------------------------------------------------------------------------------------------------------------------------------------- | ---------------------------------- |
| action     | string  | "none"                    | "select", "move", "scale", "n-resize", "e-resize", "s-resize", "w-resize", "ne-resize", "nw-resize", "se-resize", "sw-resize", "none" | 指示手柄的操作类型                 |
| plain      | boolean | false                     | -                                                                                                                                     | 指示此元素是否为简约样式（无背景） |
| slottable  | boolean | false                     | -                                                                                                                                     | 指示此元素是否支持插槽内容         |
| themeColor | string  | "rgba(51, 153, 255, 0.5)" | -                                                                                                                                     | 指示手柄的颜色                     |

### CROPPER_IMAGE 图片控制

#### 属性

| 属性名              | 类型    | 默认值    | 可选值             | 描述                                   |
| ------------------- | ------- | --------- | ------------------ | -------------------------------------- |
| initial-center-size | string  | "contain" | "contain", "cover" | 指示图片在其父元素中心对齐时的初始尺寸 |
| rotatable           | boolean | false     | -                  | 指示此元素是否可旋转                   |
| scalable            | boolean | false     | -                  | 指示此元素是否可缩放                   |
| skewable            | boolean | false     | -                  | 指示此元素是否可倾斜变形               |
| slottable           | boolean | false     | -                  | 指示此元素是否支持插槽内容             |
| translatable        | boolean | false     | -                  | 指示此元素是否可平移移动               |

#### 方法

##### $ready

- **语法**：
  - `$ready()`
  - `$ready(callback)`
- **参数**：
  - `callback`（可选）：
    - 类型：`Function`
    - 图片加载成功后执行的回调函数。
- **返回值**：
  - 类型：`Promise`
  - 解析为图片元素（`HTMLImageElement`）的 Promise 对象。
- **功能**：
  延迟执行回调函数直至图片加载完成。
- **示例**：
  ```javascript
  const cropperImage = new CropperImage()
  cropperImage.$ready((image) => {
    console.log(image.naturalWidth, image.naturalHeight)
  })
  cropperImage.src = '/cropperjs/picture.jpg'
  ```

##### $center

- **语法**：
  - `$center()`
  - `$center(size)`
- **参数**：
  - `size`（可选）：
    - 类型：`string`
    - 可选值：`"contain"`（包含），`"cover"`（覆盖）
    - 图片尺寸调整模式。
- **返回值**：
  - 类型：`CropperImage`
  - 返回元素实例以支持链式调用。
- **功能**：
  将图片在其父元素中居中对齐。

##### $move

- **语法**：
  - `$move(x)`
  - `$move(x, y)`
- **参数**：
  - `x`：
    - 类型：`number`
    - 水平方向的移动距离。
  - `y`（可选）：
    - 类型：`number`
    - 默认值：`x`（与水平距离相同）
    - 垂直方向的移动距离。
- **返回值**：
  - 类型：`CropperImage`
  - 返回元素实例以支持链式调用。
- **功能**：
  按指定距离移动图片位置。

##### $moveTo

- **语法**：
  - `$moveTo(x)`
  - `$moveTo(x, y)`
- **参数**：
  - `x`：
    - 类型：`number`
    - 水平方向的目标坐标。
  - `y`（可选）：
    - 类型：`number`
    - 默认值：`x`（与水平坐标相同）
    - 垂直方向的目标坐标。
- **返回值**：
  - 类型：`CropperImage`
  - 返回元素实例以支持链式调用。
- **功能**：
  将图片移动到指定坐标位置。

##### $rotate

- **语法**：`$rotate(angle, x, y)`
- **参数**：
  - `angle`：
    - 类型：`number | string`
    - 旋转角度（默认单位为弧度 `rad`）。
  - `x`（可选）：
    - 类型：`number`
    - 默认值：图片水平中心点
    - 水平方向的旋转原点。
  - `y`（可选）：
    - 类型：`number`
    - 默认值：图片垂直中心点
    - 垂直方向的旋转原点。
- **返回值**：
  - 类型：`CropperImage`
  - 返回元素实例以支持链式调用。
- **示例**：
  ```javascript
  $rotate(0.8) // 旋转0.8弧度
  $rotate('45deg') // 旋转45度
  $rotate('0.1turn') // 旋转0.1圈
  $rotate('90deg', 0, 0) // 以左上角为原点旋转90度
  ```

##### $zoom

- **语法**：
  - `$zoom(scale)`
  - `$zoom(scale, x, y)`
- **参数**：
  - `scale`：
    - 类型：`number`
    - 缩放因子（正数放大，负数缩小）。
  - `x`（可选）：
    - 类型：`number`
    - 默认值：图片水平中心点
    - 水平方向的缩放原点。
  - `y`（可选）：
    - 类型：`number`
    - 默认值：图片垂直中心点
    - 垂直方向的缩放原点。
- **返回值**：
  - 类型：`CropperImage`
  - 返回元素实例以支持链式调用。
- **示例**：
  ```javascript
  $zoom(0.1) // 放大10%
  $zoom(-0.1) // 缩小10%
  $zoom(0.1, 0, 0) // 从左上角放大10%
  ```

##### $scale

- **语法**：
  - `$scale(x)`
  - `$scale(x, y)`
- **参数**：
  - `x`：
    - 类型：`number`
    - 水平方向的缩放因子。
  - `y`（可选）：
    - 类型：`number`
    - 默认值：`x`（与水平缩放相同）
    - 垂直方向的缩放因子。
- **返回值**：
  - 类型：`CropperImage`
  - 返回元素实例以支持链式调用。
- **示例**：
  ```javascript
  $scale(1.1) // 水平/垂直均放大10%
  $scale(0.9) // 水平/垂直均缩小10%
  $scale(-1, 1) // 水平翻转（镜像效果）
  $scale(1, -1) // 垂直翻转（倒影效果）
  ```

##### $skew

- **语法**：
  - `$skew(x)`
  - `$skew(x, y)`
- **参数**：
  - `x`：
    - 类型：`number | string`
    - 水平方向的倾斜角度（默认单位为弧度 `rad`）。
  - `y`（可选）：
    - 类型：`number | string`
    - 默认值：`x`（与水平倾斜相同）
    - 垂直方向的倾斜角度（默认单位为弧度 `rad`）。
- **返回值**：
  - 类型：`CropperImage`
  - 返回元素实例以支持链式调用。
- **功能**：
  倾斜图片（类似 CSS 的 `skew()` 函数）。
- **示例**：
  ```javascript
  $skew(0.8) // 水平/垂直倾斜0.8弧度
  $skew('45deg') // 水平/垂直倾斜45度
  $skew(0, '0.1turn') // 仅垂直倾斜0.1圈
  ```

##### $translate

- **语法**：
  - `$translate(x)`
  - `$translate(x, y)`
- **参数**：
  - `x`：
    - 类型：`number`
    - 水平方向的平移距离。
  - `y`（可选）：
    - 类型：`number`
    - 默认值：`x`（与水平平移相同）
    - 垂直方向的平移距离。
- **返回值**：
  - 类型：`CropperImage`
  - 返回元素实例以支持链式调用。
- **功能**：
  平移图片位置（类似 CSS 的 `translate()` 函数）。

##### $transform

- **语法**：`$transform(a, b, c, d, e, f)`
- **参数**：
  | 参数 | 类型 | 说明 |
  |------|----------|--------------------------|
  | `a` | `number` | 水平缩放因子 |
  | `b` | `number` | 垂直倾斜角度 |
  | `c` | `number` | 水平倾斜角度 |
  | `d` | `number` | 垂直缩放因子 |
  | `e` | `number` | 水平平移距离 |
  | `f` | `number` | 垂直平移距离 |
- **返回值**：
  - 类型：`CropperImage`
  - 返回元素实例以支持链式调用。
- **功能**：
  应用完整的 2D 变换矩阵（类似 CSS 的 `matrix()` 函数）。

##### $setTransform

- **语法**：
  - `$setTransform(a)`
  - `$setTransform(a, b, c, d, e, f)`
- **参数**：
  - `a`：
    - 类型：`number | Array`
    - 水平缩放因子 或 完整的变换矩阵数组
  - `b`（可选）：
    - 类型：`number`
    - 垂直倾斜角度
  - `c`（可选）：
    - 类型：`number`
    - 水平倾斜角度
  - `d`（可选）：
    - 类型：`number`
    - 垂直缩放因子
  - `e`（可选）：
    - 类型：`number`
    - 水平平移距离
  - `f`（可选）：
    - 类型：`number`
    - 垂直平移距离
- **返回值**：
  - 类型：`CropperImage`
  - 返回元素实例以支持链式调用。
- **功能**：
  设置新的变换矩阵（覆盖当前变换）。

##### $getTransform

- **语法**：`$getTransform()`
- **返回值**：
  - 类型：`Array`
  - 当前应用的变换矩阵数组。
- **功能**：
  获取当前变换矩阵（类似 `CanvasRenderingContext2D.getTransform()`）。

##### $resetTransform

- **语法**：`$resetTransform()`
- **等效操作**：
  ```javascript
  $setTransform(1, 0, 0, 1, 0, 0)
  $setTransform([1, 0, 0, 1, 0, 0])
  ```
- **返回值**：
  - 类型：`CropperImage`
  - 返回元素实例以支持链式调用。
- **功能**：
  重置所有变换（恢复图片原始状态）。

#### 事件

##### `transform` 事件

**触发时机**：
当元素的 `transform` CSS 属性即将发生变化时触发（变换发生前）。
**事件属性**：
| 属性 | 类型 | 说明 |
| ------------------ | --------- | ------------------------------------------ |
| `bubbles` | `boolean` | 事件是否冒泡（固定值 `true`） |
| `cancelable` | `boolean` | 事件是否可取消（固定值 `true`） |
| `composed` | `boolean` | 事件是否能跨越 Shadow DOM（固定值 `true`） |
| `detail.matrix` | `Array` | **新的变换矩阵**（即将应用的变换状态） |
| `detail.oldMatrix` | `Array` | **旧的变换矩阵**（当前应用的变换状态） |

**变换矩阵说明**：

- 表示元素的 2D/3D 变换状态
- 典型格式：`[a, b, c, d, tx, ty]`（2D 变换矩阵）
  - `a`：水平缩放
  - `b`：垂直倾斜
  - `c`：水平倾斜
  - `d`：垂直缩放
  - `tx`：水平位移
  - `ty`：垂直位移

##### 使用示例

```html
<cropper-canvas id="canvas"></cropper-canvas>

<script>
  document.querySelector('#canvas').addEventListener('transform', function (event) {
    const { matrix, oldMatrix } = event.detail

    console.log('旧变换矩阵:', oldMatrix)
    console.log('新变换矩阵:', matrix)

    // 示例：计算位移变化量
    const deltaX = matrix[4] - oldMatrix[4]
    const deltaY = matrix[5] - oldMatrix[5]
    console.log(`位移变化: X=${deltaX}px, Y=${deltaY}px`)

    // 可阻止变换（需在可取消状态下）
    if (shouldCancelTransform(matrix)) {
      event.preventDefault()
    }
  })

  function shouldCancelTransform(matrix) {
    // 自定义逻辑：例如检测是否超出边界
    return matrix[4] > 1000 // 阻止X轴位移超过1000px的变换
  }
</script>
```

### CROPPER_SELECTION 裁剪框

#### 属性

| 属性名             | 类型    | 默认值 | 可选值 | 描述                                                                |
| ------------------ | ------- | ------ | ------ | ------------------------------------------------------------------- |
| x                  | number  | 0      | -      | 指示选择框的 X 轴坐标                                               |
| y                  | number  | 0      | -      | 指示选择框的 Y 轴坐标                                               |
| width              | number  | 0      | -      | 指示选择框的宽度                                                    |
| height             | number  | 0      | -      | 指示选择框的高度                                                    |
| aspectRatio        | number  | NaN    | -      | 指示选择框的宽高比，必须为正数                                      |
| initialAspectRatio | number  | NaN    | -      | 指示选择框的初始宽高比，必须为正数                                  |
| initialCoverage    | number  | 0.8    | -      | 指示选择框的初始覆盖比例，必须是 0 (0%) 到 1 (100%) 之间的正数      |
| dynamic            | boolean | false  | -      | 指示选择框是否动态变化（随图片变化而调整）                          |
| movable            | boolean | false  | -      | 指示此元素是否可移动                                                |
| resizable          | boolean | false  | -      | 指示此元素是否可调整大小                                            |
| zoomable           | boolean | false  | -      | 指示此元素是否可缩放                                                |
| multiple           | boolean | false  | -      | 指示是否支持多选区域                                                |
| keyboard           | boolean | false  | -      | 指示是否支持键盘控制                                                |
| outlined           | boolean | false  | -      | 指示是否显示轮廓线                                                  |
| precise            | boolean | false  | -      | 指示是否保留 `x`, `y`, `width`, `height` 属性的精确值（非整数坐标） |

#### 方法

以下是根据提供内容整理的 `CropperSelection` 方法中文文档，采用统一 Markdown 格式：

##### $center

- **语法**：`$center()`
- **返回值**：
  - 类型：`CropperSelection`
  - 返回元素实例以支持链式调用。
- **功能**：
  将选择区域在其父元素中居中对齐。

##### $move

- **语法**：
  - `$move(x)`
  - `$move(x, y)`
- **等效操作**：
  ```javascript
  $moveTo(selection.x + x, selection.y + y)
  ```
- **参数**：
  - `x`：
    - 类型：`number`
    - 水平方向的移动距离。
  - `y`（可选）：
    - 类型：`number`
    - 默认值：`x`（与水平距离相同）
    - 垂直方向的移动距离。
- **返回值**：
  - 类型：`CropperSelection`
  - 返回元素实例以支持链式调用。
- **功能**：
  按指定距离移动选择区域位置。

##### $moveTo

- **语法**：
  - `$moveTo(x)`
  - `$moveTo(x, y)`
- **参数**：
  - `x`：
    - 类型：`number`
    - 水平方向的目标坐标。
  - `y`（可选）：
    - 类型：`number`
    - 默认值：`x`（与水平坐标相同）
    - 垂直方向的目标坐标。
- **返回值**：
  - 类型：`CropperSelection`
  - 返回元素实例以支持链式调用。
- **功能**：
  将选择区域移动到指定坐标位置。

##### $resize

- **语法**：
  - `$resize(action)`
  - `$resize(action, offsetX)`
  - `$resize(action, offsetX, offsetY)`
  - `$resize(action, offsetX, offsetY, aspectRatio)`
- **参数**：
  - `action`：
    - 类型：`string`
    - 调整方向，可选值：
      - `"n-resize"`：北侧（上边）
      - `"e-resize"`：东侧（右边）
      - `"s-resize"`：南侧（下边）
      - `"w-resize"`：西侧（左边）
      - `"ne-resize"`：东北角（右上）
      - `"nw-resize"`：西北角（左上）
      - `"se-resize"`：东南角（右下）
      - `"sw-resize"`：西南角（左下）
  - `offsetX`（可选）：
    - 类型：`number`
    - 默认值：`0`
    - 特定边/角的水平偏移量。
  - `offsetY`（可选）：
    - 类型：`number`
    - 默认值：`0`
    - 特定边/角的垂直偏移量。
  - `aspectRatio`（可选）：
    - 类型：`number`
    - 默认值：`this.aspectRatio`（当前宽高比）
    - 本次调整使用的宽高比（需要时计算新尺寸）。
- **返回值**：
  - 类型：`CropperSelection`
  - 返回元素实例以支持链式调用。
- **功能**：
  从指定方向/角调整选择区域大小。

##### $zoom

- **语法**：
  - `$zoom(scale)`
  - `$zoom(scale, x, y)`
- **参数**：
  - `scale`：
    - 类型：`number`
    - 缩放因子（正数放大，负数缩小）。
  - `x`（可选）：
    - 类型：`number`
    - 默认值：选择区域水平中心点
    - 水平方向的缩放原点。
  - `y`（可选）：
    - 类型：`number`
    - 默认值：选择区域垂直中心点
    - 垂直方向的缩放原点。
- **返回值**：
  - 类型：`CropperSelection`
  - 返回元素实例以支持链式调用。
- **功能**：
  缩放选择区域（直接改变像素尺寸）。
- **示例**：
  ```javascript
  $zoom(0.1) // 放大10%
  $zoom(-0.1) // 缩小10%
  ```

##### Schange

- **语法**：
  - `Schange(x, y)`
  - `Schange(x, y, width, height)`
  - `Schange(x, y, width, height, aspectRatio)`
- **参数**：
  - `x`：
    - 类型：`number`
    - 水平方向的新坐标。
  - `y`：
    - 类型：`number`
    - 垂直方向的新坐标。
  - `width`（可选）：
    - 类型：`number`
    - 默认值：`this.width`（当前宽度）
    - 新的宽度。
  - `height`（可选）：
    - 类型：`number`
    - 默认值：`this.height`（当前高度）
    - 新的高度。
  - `aspectRatio`（可选）：
    - 类型：`number`
    - 默认值：`this.aspectRatio`（当前宽高比）
    - 本次变更使用的宽高比。
- **返回值**：
  - 类型：`CropperSelection`
  - 返回元素实例以支持链式调用。
- **功能**：
  变更选择区域的位置和/或尺寸。

##### $reset

- **语法**：`$reset()`
- **返回值**：
  - 类型：`CropperSelection`
  - 返回元素实例以支持链式调用。
- **功能**：
  重置选择区域到初始位置和尺寸。

##### $clear

- **语法**：`$clear()`
- **返回值**：
  - 类型：`CropperSelection`
  - 返回元素实例以支持链式调用。
- **功能**：
  清除当前选择区域。

##### $render

- **语法**：`$render()`
- **返回值**：
  - 类型：`CropperSelection`
  - 返回元素实例以支持链式调用。
- **功能**：
  刷新选择区域的位置或尺寸（重新渲染）。

##### $toCanvas

- **语法**：
  - `$toCanvas()`
  - `$toCanvas(options)`
- **参数**：
  - `options`（可选）：
    - 类型：`Object`
    - 配置选项：
      - `width`：
        - 类型：`number`
        - 生成的画布宽度。
      - `height`：
        - 类型：`number`
        - 生成的画布高度。
      - `beforeDraw`：
        - 类型：`Function`
        - 图像绘制前的回调函数。
        - 函数签名：`beforeDraw(context, canvas)`
        - 参数：
          - `context`：
            类型：`CanvasRenderingContext2D`
            画布 2D 渲染上下文。
          - `canvas`：
            类型：`HTMLCanvasElement`
            画布元素本身。
        - 示例：
          ```javascript
          function(context) {
            context.filter = 'grayscale(100%)';
          }
          ```
- **返回值**：
  - 类型：`Promise`
  - 解析为生成的画布元素（`HTMLCanvasElement`）的 Promise 对象。
- **功能**：
  将当前选择区域内容转换为画布元素。

#### 事件

##### `change` 事件

**触发时机**：
当选择区域（selection）的位置或大小**即将发生变化前**触发。
**事件属性**：
| 属性 | 类型 | 说明 |
| --------------- | --------- | ------------------------------------------ |
| `bubbles` | `boolean` | 事件是否冒泡（固定值 `true`） |
| `cancelable` | `boolean` | 事件是否可取消（固定值 `true`） |
| `composed` | `boolean` | 事件是否能跨越 Shadow DOM（固定值 `true`） |
| `detail.x` | `number` | 选择区域的 **X 轴坐标**（变化后的新值） |
| `detail.y` | `number` | 选择区域的 **Y 轴坐标**（变化后的新值） |
| `detail.width` | `number` | 选择区域的 **宽度**（变化后的新值） |
| `detail.height` | `number` | 选择区域的 **高度**（变化后的新值） |

**关键特性**：

1. **可取消操作**：调用 `event.preventDefault()` 可阻止选择区域变化
2. **变化前触发**：提供变化前的最后拦截机会
3. **完整坐标信息**：包含选择区域的位置和尺寸

##### 使用示例

```html
<cropper-selection id="selection"></cropper-selection>

<script>
  document.querySelector('#selection').addEventListener('change', function (event) {
    const { x, y, width, height } = event.detail

    console.log(`新位置: (${x}, ${y})`)
    console.log(`新尺寸: ${width}×${height}`)

    // 示例：限制最小选择区域
    if (width < 100 || height < 100) {
      console.warn('选择区域不能小于100×100像素')
      event.preventDefault() // 阻止变化生效
    }
  })
</script>
```

### CROPPER_SHADE 遮罩层

#### 属性

| 属性名     | 类型    | 默认值                | 可选值 | 描述                       |
| ---------- | ------- | --------------------- | ------ | -------------------------- |
| x          | number  | 0                     | -      | 指示元素的 X 轴坐标        |
| y          | number  | 0                     | -      | 指示元素的 Y 轴坐标        |
| width      | number  | 0                     | -      | 指示元素的宽度             |
| height     | number  | 0                     | -      | 指示元素的高度             |
| slottable  | boolean | false                 | -      | 指示此元素是否支持插槽内容 |
| themeColor | string  | "rgba(0, 0, 0, 0.65)" | -      | 指示遮罩层的颜色           |

#### 方法

##### Schange

- **语法**：
  - `Schange(x, y)`
  - `Schange(x, y, width, height)`
- **参数**：
  - `x`：
    - 类型：`number`
    - 水平方向的新坐标。
  - `y`：
    - 类型：`number`
    - 垂直方向的新坐标。
  - `width`（可选）：
    - 类型：`number`
    - 默认值：`this.width`（当前宽度）
    - 新的宽度。
  - `height`（可选）：
    - 类型：`number`
    - 默认值：`this.height`（当前高度）
    - 新的高度。
- **返回值**：
  - 类型：`CropperShade`
  - 返回元素实例以支持链式调用。
- **功能**：
  设置遮罩的位置和/或尺寸。

##### $reset

- **语法**：`$reset()`
- **返回值**：
  - 类型：`CropperShade`
  - 返回元素实例以支持链式调用。
- **功能**：
  将遮罩重置到初始位置和尺寸。

##### $render

- **语法**：`$render()`
- **返回值**：
  - 类型：`CropperShade`
  - 返回元素实例以支持链式调用。
- **功能**：
  刷新遮罩的位置或尺寸（重新渲染）。

### CROPPER_VIEWER 预览视图

| 属性名    | 类型   | 默认值     | 可选值                                   | 说明                                                   |
| --------- | ------ | ---------- | ---------------------------------------- | ------------------------------------------------------ |
| resize    | 字符串 | "vertical" | "both", "horizontal", "vertical", "none" | 元素可调整大小的方向（双向/水平/垂直/不可）            |
| selection | 字符串 | ""         | -                                        | 要查看的源选择器（需符合`document.querySelector`规范） |
| slottable | 布尔值 | false      | -                                        | 元素是否可插槽                                         |

## 示例代码

```vue

```

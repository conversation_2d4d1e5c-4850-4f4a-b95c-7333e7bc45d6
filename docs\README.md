# 学习笔记

## 学会提问

![学会提问](./学会提问.png)

## 概述

本仓库包含了我在学习前端开发过程中整理的各种笔记以及代码示例。目的是为了帮助自己更好地掌握前端技术，同时也希望能够帮助其他正在学习前端开发的人。

## 知识点总结

### HTML

- **状态**：学习结束
- **内容**：学习了 HTML 的基本结构、语义化标签、表单元素、多媒体标签等。

### CSS

- **状态**：学习结束
- **内容**：学习了 CSS 的基本语法、选择器、样式表的结构、盒子模型、布局（如 Flexbox 和 Grid）、响应式设计等。

### JavaScript

- **状态**：学习结束
- **内容**：学习了 JavaScript 的基本语法、变量、数据类型、运算符、控制结构、函数、对象、数组、DOM 操作、事件处理等。

### 前后端交互

- **状态**：学习结束
- **内容**：学习了如何使用 AJAX 进行前后端通信，理解了 RESTful API 的设计原则，掌握了 Fetch API 和 Axios 库的使用。

### TypeScript

- **状态**：学习结束
- **内容**：学习了 TypeScript 的基本语法、类型注解、接口、泛型、类、模块等。

### Vue.js 2

- **状态**：学习结束
- **内容**：学习了 Vue.js 2 的核心概念，如组件、指令、计算属性、过滤器、生命周期钩子、状态管理（Vuex）、路由（Vue Router）等。

### Vue.js 3

- **状态**：学习结束
- **内容**：学习了 Vue.js 3 的新特性和改进，如组合式 API（Composition API）、响应式系统、Teleport、Fragments、更好的 TypeScript 支持等。

### React

- **状态**：学习暂停
- **内容**：正在进行中，学习了 React 的基本概念，如组件、JSX 语法、Props 和 State、生命周期方法、Hooks（如 useState、useEffect、useContext 等）。

### 微信小程序

- **状态**：学习结束
- **内容**：正在进行中，学习了微信小程序的基本框架，如 WXML 和 WXSS、页面配置、组件化开发、API 使用、数据绑定等。

### UniApp

- **状态**：学习结束
- **内容**：正在进行中，学习了 UniApp 的基本概念，如跨平台开发、组件化开发、API 使用、数据绑定、路由等。

### Node.js

- **状态**：学习中
- **内容**：正在进行中，学习了 Node.js 的基本概念，如事件驱动、异步编程、模块化、文件系统、网络编程等。

### 面试题大全

- **状态**：学习中
- **内容**：正在进行中，整理了一些常见的前端面试题及其答案，包括 HTML、CSS、JavaScript、TypeScript、Vue.js、React、Node.js、性能优化、工程化、git 等方面的内容。

[TOC]

# Vue2 选项式 API

## 创建项目命令

```bash
# 全局安装vite脚手架
npm install -g @vitejs/create-app

# 创建项目
pnpm create vite@latest my-vue-app --template vue
pnpm create vite
# 官方推荐👇
pnpm create vue
# 进入项目
cd my-vue-app

# 安装依赖
pnpm i
# 启动项目
pnpm dev
# 测试项目
pnpm test
# 打包项目
pnpm build
```

## 配置项

### el:挂载整个 Vue 实例的 DOM 元素。

```vue

```

### data:用于定义 Vue 实例的响应式数据。

```vue
<template>
  <h1>{{ name }}</h1>
</template>
<script>
export default {
  data() {
    return {
      name: "six-flower",
    }
  },
}
</script>
<style></style>
```

### methods:用于定义可以在 Vue 实例中调用的方法。

```vue
<template>
  <h1 @click="add">{{ num }}</h1>
</template>
<script>
export default {
  data() {
    return {
      num: 0,
    }
  },
  methods: {
    add() {
      this.num++
    },
  },
}
</script>
<style></style>
```

### computed:用于定义计算属性,基于响应式数据进行计算。

**属性,方法,计算属性等都无法重名(重名会覆盖)**

当我们需要进行数值计算,并且依赖于其它数据时,应该使用 computed,因为可以利用 computed 的缓存特性,避免每次获取值时,都要重新计算

```vue
<template>
  <h1 @click="add">{{ sum }}</h1>
</template>
<script>
export default {
  data() {
    return {
      num: 0,
      price: 100,
    }
  },
  methods: {
    add() {
      this.num++
    },
  },
  computed: {
    sum() {
      return this.num * this.price
    },
  },
}
</script>
<style></style>
```

**计算属性值会基于其响应式依赖被缓存**。
一个计算属性仅会在其响应式依赖更新时才重新计算。这意味着只要 `num和price` 不改变,无论多少次访问 `sum` 都会立即返回先前的计算结果,而不用重复执行 计算属性 getter 函数

#### 可写计算属性

- 计算属性默认是只读的。
- 当你尝试修改一个计算属性时,你会收到一个运行时警告。
- 只在某些特殊场景中你可能才需要用到“可写”的属性,
- 你可以通过同时提供 getter 和 setter 来创建
- 不推荐直接使用

```vue
<template>
  <div>
    {{ name }}
    <button @click="updateName">修改名字</button>
  </div>
</template>
<script>
export default {
  data() {
    return {
      firstName: "james",
      lastName: "watt",
    }
  },
  computed: {
    name: {
      // getter
      get() {
        return this.firstName + " " + this.lastName
      },
      // setter
      set(newValue) {
        // 注意:我们这里使用的是解构赋值语法
        ;[this.firstName, this.lastName] = newValue.split(" ")
      },
    },
  },
  methods: {
    updateName() {
      this.name = "John Doe"
    },
  },
}
</script>
```

#### Getter 不应有副作用(其他作用)

所谓副作用,就是不要做除了计算,不要做其他事情。

计算属性的 getter 应只做计算而没有任何其他的副作用,这一点非常重要,请务必牢记。

举例来说,**不要在 getter 中做异步请求或者更改 DOM**！

一个计算属性的声明中描述的是如何根据其他值派生一个值。因此 getter 的职责应该仅为计算和返回该值。在之后的学习中我们会讨论如何使用监听器(watch)根据其他响应式状态的变更来创建副作用。

#### 避免直接修改计算属性值

从计算属性返回的值是派生状态。可以把它看作是一个“临时快照”,每当源状态发生变化时,就会创建一个新的快照。更改快照是没有意义的,因此计算属性的返回值应该被视为只读的,并且永远不应该被更改——应该更新它所依赖的源状态以触发新的计算。

### watch:用于观察 Vue 实例的数据变化并执行相应的操作。

**不要使用箭头函数来定义 watch 函数**
(例如 `searchQuery: newValue => this.updateAutocomplete(newValue)`)。理由是箭头函数绑定了父级作用域的上下文,所以 `this` 将不会按照期望指向 Vue 实例,侦听器只能侦听 data 中已经存在的属性！

当我们需要在数据变化时执行异步或开销较大的操作时,应该使用 watch,使用 watch 选项允许我们执行异步操作 ( 访问一个 API ),限制我们执行该操作的频率,并在我们得到最终结果前,设置中间状态。这些都是计算属性无法做到的

```vue
<template>
  <div @click="fn">{{ num }}</div>
</template>
<script>
export default {
  data() {
    return {
      num: 0,
    }
  },
  methods: {
    fn() {
      this.num++
    },
  },
  watch: {
    num(newValue, oldValue) {
      console.log("--------------分割线--------------")
      console.log("旧的:" + oldValue)
      console.log("新的:" + newValue)
    },
  },
}
</script>
```

#### 深层侦听器

谨慎使用,深度侦听需要遍历被侦听对象中的所有嵌套的属性,当用于大型数据结构时,开销很大。因此请只在必要时才使用它,并且要留意性能。

```vue
<template>
  <div @click="num1++">{{ num1 }}</div>
  <div @click="num2.num++">{{ num2 }}</div>
</template>
<script>
export default {
  data() {
    return {
      num1: 0,
      num2: { num: 0 },
    }
  },
  computed: {
    // 利用计算属性来对多个属性进行监听
    many() {
      return [this.num1, this.num2]
    },
  },
  watch: {
    num1(newValue, oldValue) {
      console.log("--------------分割线1--------------")
      console.log("我是num1")
      console.log("旧的:" + oldValue)
      console.log("新的:" + newValue)
    },
    num2: {
      handler(newObj, oldObj) {
        // 注意:复杂数据类型获取到的是地址,不管是new还是old都指向同一个地址,所以newValue等于oldValue
        console.log("--------------分割线2--------------")
        console.log("我是num2")
        // 注意初始时旧的是undefind,所以使用的immediate时,如果取旧值会报错
        console.log("旧的:" + oldObj.num)
        console.log("新的:" + newObj.num)
      },
      // 如果没有深度监听则无法监听对象内部的属性变化
      deep: true,
      // 强制立即执行回调,注意参数立即调用时无法使用参数
      // immediate: true,
      // 回调函数的初次执行就发生在 created 钩子之前。Vue 此时已经处理了 data、computed 和 methods 选项,所以这些属性在第一次调用时就是可用的。
    },
    many: {
      // 注意immediate属性,如果使用该属性解构时,旧属性会报错
      handler([a1, a2], [a3, a4]) {
        console.log("many变了,这里num1和num2都能检测到")
        console.log(`我是监听的many;num旧的:${a1}新的:${a3}`)
        console.log(`我是监听的many;objnum旧的:${a2.num}新的:${a4.num}`)
      },
      // immediate: true,
      deep: true,
    },
  },
}
</script>
```

#### 使用组件实例的 $watch() 方法来命令式地创建一个侦听器

用 `watch` 选项或者 `$watch()` 实例方法声明的侦听器,会在宿主组件卸载时自动停止。因此,在大多数场景下,你无需关心怎么停止它。

在少数情况下,你的确需要在组件卸载之前就停止一个侦听器,这时可以调用 `$watch()` API 返回的函数

```vue
<template>
  <div @click="num++">{{ num }}</div>
</template>
<script>
export default {
  data() {
    return {
      num: 0,
    };
  },
  created() {
    // 用$watch方法创建一个侦听器
    let numwatch = this.$watch(
      "num",
      (newnum, oldnum) => {
        console.log("-----------分割线-----------");
        console.log("旧的:" + oldnum);
        console.log("新的:" + newnum);
      },
      {
        deep: true ,
        immediate: true
      }
      }
    );
    setTimeout(() => {
      console.log("关闭侦听器");
      // 调用返回的函数来关闭侦听器
      numwatch();
    }, 1000);
  },
};
</script>
```

### mounted:生命周期钩子,在 Vue 实例挂载后执行。

```vue
<template></template>
<script>
export default {
  mounted() {
    this.log()
  },
  methods: {
    log() {
      console.log("这是自己打印的")
    },
  },
}
</script>
<style></style>
```

### components:注册子组件。

#### 先写一个子组件

```vue
<template>
  <button @click="fn">粉色的按钮</button>
</template>
<script>
export default {
  methods: {
    fn() {
      console.log("点击了粉色按钮")
    },
  },
}
</script>
<style scoped>
button {
  width: 100px;
  height: 40px;
  background-color: pink;
  border: 2px solid rgb(255, 108, 132);
  border-radius: 10px;
}
</style>
```

#### 全局注册子组件

全局组件注册后,使用时不需要引入就可以直接使用

```javascript
import { createApp } from "vue"
import App from "./App.vue"

// 先导入组件
// 使用component方法注册全局组件,接收两个参数
// 第一个参数 组件的名字
// 第二个参数 组件的值
// 如果要注册多个组件可以用链式写法,如 createApp(App).component("button", button).component("input", input).mount("#app");
import pinkButton from "./son.vue"
createApp(App).component("pinkButton", pinkButton).mount("#app")
```

#### 局部注册子组件

局部注册只能在这个文件内部使用

```vue
<template>
  <pinkButton></pinkButton>
</template>
<script>
// 导入组件
import pinkButton from "./son.vue"
export default {
  components: {
    // 在components配置项中 注册button组件
    pinkButton: pinkButton,
  },
}
</script>
```

### props:在子组件中定义接收的属性。(父向子传数据)

props 接收数据是只读的,(对复杂数据类型来说,地址是只读的,内部数据可以改变,但不建议改变)

#### 调用子组件传数据

```vue
<template>
  <!-- v-bind传入数字或变量 -->
  <JiShiButton :id="1"></JiShiButton>
  <JiShiButton
    name="确定"
    :id="2"></JiShiButton>
</template>
<script>
import JiShiButton from "./components/JiShiButton.vue"
export default {
  components: {
    JiShiButton: JiShiButton,
  },
}
</script>
<style></style>
```

#### 子组件接收数据

```vue
<template>
  <div>
    <button @click="fn">{{ name }}</button>
    <h1>{{ id }}</h1>
  </div>
</template>
<script>
export default {
  // 数组形式写法
  // props: ["name", "id"],
  // 对象形式写法
  props: {
    name: {
      /** type 接收参数的类型 */
      type: String,
      /** required 参数是否必传(默认为false) */
      required: false,
      /** 参数默认值(设置了默认值,在调用子组件不传参时,该变量也有值) */
      default: "默认按钮",
    },
    id: {
      type: Number,
      required: true,
    },
  },
  methods: {
    fn() {
      // 调用传入的参数
      console.log(this.name)
    },
  },
}
</script>
<style>
button {
  width: 100px;
  height: 40px;
  color: rgb(255, 32, 225);
  background-color: pink;
  border: 1px solid rgb(255, 78, 107);
  border-radius: 10px;
}
div {
  text-align: center;
}
</style>
```

### emits:声明组件内能触发的事件 (自定义事件,子向父传值)

#### 子组件的操作

子组件内部在适当的时候,触发一个事件
​- 组件内部通过 this.$emit('my-event',事件参数) 可以触发组件的自定义事件

- `$emit(事件名,参数) 第一个参数为 自定义的事件名称 第二个参数为需要传递的数据`

  组件触发的事件**没有冒泡机制**。你只能监听直接子组件触发的事件。

```vue
<template>
  <button @click="fn">粉嫩的按钮</button>
</template>
<script>
export default {
  // 组件的事件声明不是必须的
  emits: ["pinkClick"],
  // 对象写法可以配合Ts进行事件参数校验,函数内部也可以写逻辑进行校验,通过就返回true,不通过就返回false
  // emits: {
  //   click1: null,
  //   click2: () => {},
  // },
  props: {
    n: {},
  },
  data() {
    return {
      num: 0,
    }
  },
  mounted() {
    this.num = this.n
  },
  methods: {
    fn() {
      this.num++
      console.log("点击了粉嫩的按钮" + this.num)
      this.$emit("pinkClick", this.num)
    },
  },
}
</script>
<style scoped>
button {
  width: 100px;
  height: 40px;
  background-color: pink;
  border: 2px solid rgb(255, 137, 156);
  border-radius: 10px;
  color: #fff;
}
</style>
```

#### 父组件的操作

```vue
<template>
  <div>
    <!-- 使用子组件,并加上子组件的自定义组件pinkClick,事件触发函数fn -->
    <pinkButton
      :n="4"
      @pinkClick="fn"></pinkButton>
    <!-- 子组件传过来的参数会作为自定义事件的事件对象 -->
    <!-- <pinkButton :n="4" @pinkClick="fn($event)"></pinkButton> -->
  </div>
</template>
<script>
// 引入子组件
import pinkButton from "./components/pinkButton.vue"
export default {
  components: {
    // pinkButton: pinkButton,
    // 对象简写
    pinkButton,
  },
  methods: {
    //接收子组件传来的数据(时间对象)
    fn(event) {
      console.log("父组件接收到传的数据为:" + event)
    },
  },
}
</script>
<style></style>
```

#### 事件校验例子

```vue
<script>
export default {
  emits: {
    // 没有校验
    click: null,

    // 校验 submit 事件
    submit: ({ email, password }) => {
      if (email && password) {
        return true
      } else {
        console.warn("Invalid submit event payload!")
        return false
      }
    },
  },
  methods: {
    submitForm(email, password) {
      this.$emit("submit", { email, password })
    },
  },
}
</script>
```

### provide 和 inject:祖先组件向后代组件提供数据或方法。

#### 父组件提供数据

```vue
<template>
  <div>
    <pinkButton></pinkButton>
  </div>
</template>
<script>
import pinkButton from "./components/pinkButton.vue"
export default {
  components: {
    pinkButton,
  },
  /**
   * 使用provide 统一下发值
   * provide 是一个函数 函数必须有返回值
   * 返回的值可以在任意子级组件接收
   */
  provide() {
    return {
      str: "最上层组件传的值",
    }
  },
}
</script>
<style></style>
```

#### 子组件接收数据

```vue
<template>
  <button>{{ str }}</button>
</template>
<script>
export default {
  data() {
    return {
      num: 0,
    }
  },
  // 在后代组件中使用inject配置项,注入数据,inject的值是一个数组,数组中写要注入的数据的名字字符串
  inject: ["str"],
}
</script>
<style scoped>
button {
  width: 100px;
  height: 40px;
  background-color: pink;
  border: 2px solid rgb(255, 137, 156);
  border-radius: 10px;
  color: #fff;
}
</style>
```

注入 inject 进阶

```javascript
export default {
  // 当声明注入的默认值时
  // 必须使用对象形式
  inject: {
    // 注入的名字为 m
    message: {
      // 将 message 注入到当前组件中的m属性
      from: "message", // 当与原注入名同名时,这个属性是可选的
      // 设置默认值
      default: "default value",
    },
    user: {
      // 对于非基础类型数据,如果创建开销比较大,或是需要确保每个组件实例
      // 需要独立数据的,请使用工厂函数
      default: () => ({ name: "John" }),
    },
  },
}
```

### filters:用于定义过滤器,以在模板中格式化数据。

```vue

```

### directives:注册自定义指令。

#### 全局自定义指令

```javascript
// 创建vue实例,传入根组件
// 注意是根组件,根组件内的其他组件都可以用该指令
let app = createApp(LearnStyle)
// 定义一个指令 color,使用的时候 在标签中使用v-color属性
app.directive("color", {
  mounted(el, binding) {
    console.log(el, binding)
    el.style.color = binding.value
  },
})
// 把根组件挂载到 #app上
app.mount("#app")
```

#### 局部自定义指令

```vue
<template>
  <input
    type="text"
    v-focus />
</template>
<script>
export default {
  directives: {
    // 添加局部指令 v-focus
    focus: {
      // mounted: (el) => el.focus(),
      mounted(el) {
        el.focus()
      },
    },
  },
}
</script>
<style></style>
```

## 模板语法

### 文本插值

```vue
<template>
  <h1>{{ name }}</h1>
  <p>{{ "我爱" + name }}</p>
</template>
<script>
// 双大括号插值
export default {
  data() {
    return {
      name: "six-flower",
    }
  },
}
</script>
```

#### $nextTick

**data 内数据的修改是同步,dom 的更新不是同步的,是异步的**

data 是存放当前组件状态的地方,组件所有的状态数据都可以放到 data 里面存储,在 data 里定义的数据具备响应式,在组件中 data 只能是函数。这个函数 return 一个对象,对象里面对应状态或者数据

```vue
<template>
  <div id="a">{{ num }}</div>
  <button @click="fn">num++</button>
</template>
<script>
export default {
  data() {
    return {
      num: 0,
    }
  },
  methods: {
    async fn() {
      this.num++
      console.log(document.querySelector("#a").innerHTML)
      await this.$nextTick
      console.log("dom已更新")
      console.log(document.querySelector("#a").innerHTML)
    },
  },
}
</script>
```

## 指令

### v-bind:(\:) 属性绑定

#### 基本用法

```vue
<template>
  <!-- v-开头的东西在vue中被称为指令 -->
  <!-- 可以放变量或者js表达式 -->
  <h1 v-bind:class="color">{{ name + age + "岁了" }}</h1>
  <img
    v-bind:src="img"
    alt="" />
  <br />
  <!-- 简写 -->
  <h1 :class="color">{{ name + age + "岁了" }}</h1>
  <img
    :src="img"
    alt="" />
</template>
<script>
export default {
  data() {
    return {
      name: "six-flower",
      age: 21,
      color: "red",
      img: "./src/images/assets/5.jpg",
    }
  },
}
</script>
<style>
* {
  text-align: center;
}
.red {
  color: red;
}
img {
  display: block;
  width: 100px;
}
</style>
```

#### style 属性的绑定

```vue
<template>
  <!-- 放样式 -->
  <div :style="obj">six-flower</div>
  <!-- 放对象,对象内放样式 -->
  <div :style="{ color: c, backgroundColor: 'yellow' }">六花</div>
  <!-- 放数组,数组内放对象,对象内放样式 -->
  <div :style="[{ color: c, backgroundColor: 'yellow' }, obj2]">六花</div>
  <button
    @click="
      c = '#123456'
      obj.fontSize = '50px'
    ">
    变色
  </button>
</template>
<script>
export default {
  data() {
    return {
      obj: {
        color: "red",
        backgroundColor: "blue",
        fontSize: "30px",
      },
      obj2: {
        fontSize: "20px",
        border: "5px solid pink",
        // 后面的样式会覆盖掉前面的样式
        // 这里字体颜色pink覆盖掉前面的red
        color: "pink",
        fontWeight: "bold",
        textAlign: "center",
      },
      c: "red",
    }
  },
}
</script>
```

#### class 属性的绑定

```vue
<template>
  <h1 class="red font20">{{ name }}</h1>
  <br />
  <h1 :class="'red font20'">{{ name }}</h1>
  <br />
  <h1 :class="color">{{ name }}</h1>
  <br />
  <h1 :class="fontSize">{{ name }}</h1>
  <br />
  <h1 :class="{ red: flag, pink: !flag, font20: flag, font30: !flag }">{{ name }}</h1>
  <br />
  <h1 :class="['pink', 'font20', { backgroundB: true, red: false }, { borderO: true }]">{{ name }}</h1>
  <br />
  <button @click="color = color == 'red' ? 'pink' : 'red'">变色</button><br />
  <button @click="fontSize = fontSize == 'font20' ? 'font30' : 'font20'">变大小</button><br />
  <button @click="flag = !flag">切换</button>
</template>
<script>
export default {
  data() {
    return {
      name: "six-flower",
      color: "red",
      fontSize: "font20",
      flag: true,
    }
  },
}
</script>
<style>
* {
  text-align: center;
}
.red {
  color: red;
}
.pink {
  color: pink;
}
.font20 {
  font-size: 20px;
}
.font30 {
  font-size: 30px;
}
.backgroundB {
  background-color: blue;
}
.borderO {
  border: 5px solid orange;
}
</style>
```

#### 样式污染问题

Vue 最终编译打包后都在一个 html 页面中,如果在两个组件中取一样类名分别引用在自身,那么后者会覆盖前者。

解决方法:

- 手动起不同的类名(过于麻烦)
- CSS IN JS
  - CSS IN JS 是使用 JavaScript 编写 CSS 的统称,用来解决 CSS 样式冲突、覆盖等问题

##### Scoped CSS

1. 使用方法

在 style 标签上使用 scoped,当 `<style>` 标签有 scoped 属性时,它的 CSS 只作用于当前组件中的元素

```vue
<template>
  <div class="ex">hi</div>
</template>
<style scoped>
.ex {
  color: red;
}
</style>
```

2. 原理解析

- 每个 Vue 文件都将对应一个唯一的 id,该 id 根据文件路径名和内容 hash 生成,通过组合形成 scopeId
- 编译 template 标签时,会为每个标签添加了当前组件的 scopeId
  ```html
  <div class="demo">test</div>
  <!-- 会被编译成: -->
  <div
    class="demo"
    data-v-12e4e11e>
    test
  </div>
  ```
- 编译 style 标签时,会根据当前组件的 scopeId 通过属性选择器和组合选择器输出样式,如
  ```css
  .demo {
    color: red;
  }
  <!-- 会被编译成: -->
  .demo[data-v-12e4e11e] {
    color: red;
  }
  ```
- 这样就相当为我们配置的样式加上了一个唯一表示

3. 原理解析

vue-loader 通过生成哈希 ID,根据 type 的不同调用不同的 loader 将,哈希 ID 分别注入到 DOM 和属性选择器中。实现 CSS 局部作用域的效果。CSS Scoped 可以算作为 Vue 定制的一个处理原生 CSS 作用域的解决方案

###### 样式穿透

当一个组件的样式被应用到它的子组件时,子组件的样式也会被应用到父组件,这就是样式穿透。

```vue
<template>
  <div class="parent">
    <div class="child">子组件</div>
  </div>
</template>
<style scoped>
/* 父组件的样式使用 scoped,子组件的样式使用 deep 修饰符 */
:deep(.van-card__title) {
  color: red;
}
</style>
```

### v-on:(@) 事件绑定

曾遇错误:要牢记表单元素状态改变要用 change 事件,例如 checkbox,点击事件触发比元素状态改变要快,所以要用 change

```vue
<template>
  <h1>{{ name + age + "岁了" }}</h1>
  <br />
  <button v-on:click="age++">年龄加1</button>
  <br />
  <!-- 简写 -->
  <!-- 内部写js代码 -->
  <button @click="age--">年龄减1</button><br />
  <button @click="fn()">log</button>
  <!-- 绑定事件处理函数 -->
  <button @click="fn">log</button>
</template>
<script>
export default {
  data() {
    return {
      name: "six-flower",
      age: 21,
    }
  },
  // methods 内部用来存放函数
  methods: {
    fn() {
      console.log("this")
      // this 是一个vue实例
      // data 上定义的属性会自动成为组件实例上的属性
      // methods 上定义的函数会自动成为组件实例上的方法
      console.log(this)
      console.log("我爱" + this.name)
    },
  },
}
</script>
```

#### 事件修饰符

- .stop:用于调用 event.stopPropagation(),阻止事件冒泡。
- .prevent:用于调用 event.preventDefault(),阻止默认行为。
- .self:只有当事件目标是绑定的元素本身时,才会触发事件处理器。
- .once:仅在第一次触发时执行事件监听器。
- .capture:在事件捕获阶段调用事件监听器,而不是在冒泡阶段
- .passive:标记事件监听器为被动,表明不调用 preventDefault(),有助于提高性能

```vue
<template>
  <div @click="parentClick">
    <button @click.stop="buttonClick">点击我(阻止冒泡)</button>
    <!-- 修饰语可以使用链式书写 -->
    <a @click.stop.once="buttonClick">点我(链式书写))</a>
  </div>
  <a
    href="https://www.runoob.com/js/js-intro.html"
    @click.prevent="void 0"
    >a标签(阻止默认行为)</a
  >
  <!-- 也可以只有修饰符 -->
  <a
    href="https://www.runoob.com/js/js-intro.html"
    @click.prevent
    >a标签(阻止默认行为)</a
  >
  <button @click.once="console.log(1)">只能触发一次</button>
  <!-- 仅当 event.target 是元素本身时才会触发事件处理器 -->
  <!-- 例如:事件处理器不来自子元素 -->
  <div @click.self="doThat">...</div>
</template>
<script>
export default {
  methods: {
    parentClick() {
      console.log("父元素被点击")
    },
    buttonClick() {
      console.log("按钮被点击")
    },
  },
}
</script>
```

##### 按键事件修饰符

在监听键盘事件时,我们经常需要检查特定的按键。Vue 允许在 v-on 或 @ 监听按键事件时添加按键修饰符

1. 常用按键别名

- .enter
- .tab
- .delete (捕获“Delete”和“Backspace”两个按键)
- .esc
- .space
- .up
- .down
- .left
- .right

**系统按键修饰符**

- .ctrl
- .alt
- .shift
- .meta (徽标)

  也可以直接用触发按键事件时获取到的 key 值作为修饰符如.a、.b、./等

```vue
<template>
  <input @keydown="fn" />
</template>
<script>
export default {
  methods: {
    fn(e) {
      console.log(e)
      console.log(e.key)
    },
  },
}
</script>
```

**.exact 修饰符**

.exact 修饰符允许精确控制触发事件所需的系统修饰符的组合

```vue
<!-- 当按下 Ctrl 时,即使同时按下 Alt 或 Shift 也会触发 -->
<button @click.ctrl="onClick">A</button>

<!-- 仅当按下 Ctrl 且未按任何其他键时才会触发 -->
<button @click.ctrl.exact="onCtrlClick">A</button>

<!-- 仅当没有按下任何系统按键时触发 -->
<button @click.exact="onClick">A</button>
```

##### 鼠标事件修饰符

- .left
- .right
- .middle
  这些修饰符将处理程序限定为由特定鼠标按键触发的事件

#### this 指向

在 methods 中定义的方法,this 指向都被改变了,this 指向当前的 vue 实例

```vue
<template>
  <!-- this 指向 vue实例 可以拿到实例的数据和方法 -->
  <button @click="handle">点击</button>
</template>

<script>
export default {
  data() {
    return {
      data: "",
    }
  },
  methods: {
    handle() {
      console.log(this)
    },
  },
}
</script>
```

#### event 对象

```vue
<template>
  <!--
    如果事件什么都不传、并且不写()
    那么事件处理函数会默认接收到event对象
   -->
  <button @click="handle1">点击</button>
  <!--
    使用在template里面使用$event获取当前事件的event对象($event固定)
   -->
  <button @click="console.log($event)">点击</button>
  <!-- 将事件对象传入函数 -->
  <button @click="handle2('第一个参数', $event)">点击</button>
</template>

<script>
export default {
  methods: {
    handle1(event) {
      console.log(event)
    },
    // 接收事件对象时可以设置变量名
    handle2(msg, e) {
      console.log(e)
    },
  },
}
</script>
```

### v-if 、v-else-if 和 v-else 条件渲染指令

根据条件动态渲染与删除节点,在同一个标签上面,在 vue3 中 v-if 比 v-for 先执行,vue2 中相反

```vue
<template>
  <h1>{{ name + age + "岁了" }}</h1>
  <br />
  <span v-if="age >= 22">{{ name + "大学毕业了" }}</span> <span v-else-if="age >= 18">{{ name + "成年了" }}</span
  ><span v-else>{{ name + "未成年" }}</span
  ><br />
  <button @click="age++">+</button>
  <button @click="age--">-</button>
</template>

<script>
export default {
  data() {
    return {
      name: "six-flower",
      age: 21,
    }
  },
}
</script>
```

### v-show 条件显示隐藏指令

根据条件设置节点是否显示,适合频繁切换显隐的情景,在 vue 中 v-for 比 v-show 先执行

```vue
<template>
  <h1>{{ name + age + "岁了" }}</h1>
  <br />
  <span v-show="age >= 22">{{ name + "大学毕业了" }}</span> <br />
  <button @click="age++">+</button>
  <button @click="age--">-</button>
</template>

<script>
export default {
  data() {
    return {
      name: "six-flower",
      age: 21,
    }
  },
}
</script>
```

### v-for 列表渲染

原理

- vue 实现了一套虚拟 DOM,使我们可以不直接操作 DOM 元素只操作数据,就可以重新渲染页面,而隐藏在背后的原理是高效的 Diff 算法
- 当页面数据发生变化时,Diff 算法只会比较同一层级的节点；
- 如果节点类型不同,直接干掉前面的节点,再创建并插入新的节点,不会再比较这个节点后面的子节点；如果节点类型相同,则会重新设置该节点属性,从而实现节点更新
- 使用 key 给每个节点做一个唯一标识,Diff 算法就可以正确识别此节点,"就地更新"找到正确的位置插入新的节点

```vue
<template>
  <!-- key推荐使用唯一标识符,如商品id -->
  <!-- 通过唯一的key,再修改元素后,能找到对应的dom -->
  <!-- 如果设置唯一的key,则整个列表都会重新渲染 -->
  <div
    v-for="(item, index) in arr"
    :key="index">
    {{ name + "item:" + item + "index" + index }}
  </div>
</template>
<script>
export default {
  data() {
    return {
      name: "six-flower",
      arr: [1, 5, 6, 7, 9],
    }
  },
}
</script>
```

### v-model 表单输入双向绑定

```vue
<template>
  <div>{{ name }}</div>
  <input
    type="text"
    v-model="name" />
  <button @click="console.log(name)">log</button>
</template>
<script>
export default {
  data() {
    return {
      name: "six-flower",
    }
  },
}
</script>
```

`v-model` 可以用于各种不同类型的输入,textarea 和 select 元素。它会根据所使用的元素自动使用对应的 DOM 属性和事件组合:

- 文本类型的`input` 和 `textarea` 元素会绑定 `value` property 并侦听 `input` 事件；
- `<input type="checkbox">` 和 `<input type="radio">` 会绑定 `checked` property 并侦听 `change` 事件；
- `<select>` 会绑定 `value` property 并侦听 `change` 事件。

`v-model` 指令可以表单 及 元素上创建双向数据绑定。它会根据控件类型自动选取正确的方法来更新元素

#### 多个复选框

```vue
<template>
  <input
    type="checkbox"
    value="Jack"
    v-model="checkedNames" />
  <label for="jack">Jack</label>
  <input
    type="checkbox"
    value="john"
    v-model="checkedNames" />
  <label for="john">John</label>
  <input
    type="checkbox"
    value="Mike"
    v-model="checkedNames" />
  <label for="mike">Mike</label>
  <br />
  <!-- 被选中的复选框的value的值会被存入到数组中 -->
  <span>Checked names: {{ checkedNames }}</span>
</template>
<script>
export default {
  data() {
    return {
      checkedNames: [],
    }
  },
}
</script>
```

#### 单选框

```vue
<template>
  <input
    type="radio"
    value="男"
    v-model="picked" />
  <label for="one">男</label>
  <br />
  <input
    type="radio"
    value="女"
    v-model="picked" />
  <label for="two">女</label>
  <br />
  <!-- v-model绑定同一个变量说明这些单选框是一组的 -->
  <!-- 对于一组单选框来说,选中的单选按钮的值会被传入绑定的同一个变量 -->
  <span>性别: {{ gender }}</span>
</template>
<script>
export default {
  data() {
    return {
      gender: "",
    }
  },
}
</script>
```

#### 下拉框

```vue
<template>
  <!-- 单选,变量是一个字符串 -->
  <select v-model="selected">
    <option
      disabled
      value="">
      请选择
    </option>
    <option value="a">A</option>
    <option>B</option>
    <option>C</option>
  </select>
  <span>Selected: {{ selected }}</span>
  <!-- 下拉框选择选项时,如果option标签没有value值会把标签内的文本传入绑定的变量,如果有value值,会把value值传入到变量中 -->
  <!-- 多选,变量是一个数组,Ts要注意 -->
  <select
    v-model="selected_"
    multiple>
    <option
      disabled
      value="">
      请选择
    </option>
    <option value="a">A</option>
    <option>B</option>
    <option>C</option>
  </select>
  <span>Selected: {{ selected_ }}</span>
</template>
<script>
export default {
  data() {
    return {
      selected: "",
      selected_: "",
    }
  },
}
</script>
```

#### 动态绑定值

可以用 v-bind

```vue
<template>
  -----多选框为值动态绑定变量----- <br />{{ toggle1 }}/{{ toggle2 }} <br />
  <input
    type="checkbox"
    v-model="toggle1"
    true-value="yes"
    false-value="no" />
  <input
    type="checkbox"
    v-model="toggle2"
    :true-value="dynamicTrueValue"
    :false-value="dynamicFalseValue" /><br />
  -----单选按钮----- <br />{{ pick1 }}/{{ pick2 }} <br />
  <input
    type="radio"
    v-model="pick1"
    value="first" />
  <input
    type="radio"
    v-model="pick1"
    value="second" /><br />
  <input
    type="radio"
    v-model="pick2"
    :value="first" />
  <input
    type="radio"
    v-model="pick2"
    :value="second" /><br />
  -----下拉框----- <br />{{ selected }}
  <select v-model="selected">
    <!-- 内联对象字面量 -->
    <option :value="{ number: 123 }">123</option>
    <option value="{ number: 123 }">123</option>
    <option value="123">123</option>
  </select>
</template>
<script>
export default {
  data() {
    return {
      toggle1: false,
      toggle2: false,
      dynamicTrueValue: "dynamicTrueValue",
      dynamicFalseValue: "dynamicFalseValue",
      pick1: "",
      pick2: "",
      first: "first0",
      second: "second0",
      selected: "",
    }
  },
}
</script>
<style>
* {
  text-align: center;
}
</style>
```

#### 修饰符

- .lazy:在失去焦点时更新数据。(换成 change 事件触发再更新数据)
- .number:将输入转换为数字类型。
- .trim:自动去除输入值的首尾空格

```vue
<template>
  <div>
    <input
      type="text"
      v-model.lazy="message"
      placeholder="输入内容后失去焦点" />
    <p>你输入的内容是: {{ message }}</p>
    <input
      type="number"
      v-model.number="age"
      placeholder="输入你的年龄" />
    <p>你的年龄是: {{ age }}</p>
    <input
      type="text"
      v-model.trim="username"
      placeholder="输入用户名" />
    <p>你的用户名是: {{ username }}</p>
  </div>
</template>
<script>
export default {
  data() {
    return {
      message: "",
      age: 0,
      username: "",
    }
  },
}
</script>
```

#### 在组件上使用 v-model

```vue
<template>
  <CustomInput v-model="searchText" />
  <!-- v-model` 会被展开为如下的形式(**这个展开是固定的,vue做的-需要记住**): -->
  <!-- <CustomInput :modelvalue="searchText" @update:modelValue="searchText = $event" /> -->
  <CustomInput
    :modelValue="searchText"
    @update:modelValue="(newValue) => (searchText = newValue)" />
</template>

<!-- 因此vue组件中需要定义声明modelValue属性和update:modelValue事件 -->
<template>
  <input
    type="text"
    :value="modelValue"
    @input="$emit('update:modelValue', $event.target.value)" />
</template>
<script>
export default {
  emit: ["update:modelValue"],
  props: ["modelValue"],
}
</script>
<!-- v-model 指令在组件上使用时,默认属性是modelValue,事件是update:modelValue -->
<!-- 可以像下面这样修改参数 -->
<!-- 父组件 -->
<template>
  <CustomInput v-model:name="searchText" />
</template>
<!-- 子组件 -->
<template>
  <input
    type="text"
    :value="name"
    @input="$emit('update:name', $event.target.value)" />
</template>
<script>
export default {
  emit: ["update:name"],
  props: ["name"],
}
</script>
```

测试例子

```vue
<!-- 子组件 -->
<template>
  <input
    type="text"
    :value="name"
    @input="$emit('update:name', $event.target.value)" />
</template>
<script>
export default {
  emit: ["update:name"],
  props: ["name"],
}
</script>

<!-- 父组件 -->
<template>
  <MyInput
    v-model:name="searchText"
    @input="fn" />
  <button>按钮</button>
</template>
<script>
export default {
  data() {
    return {
      searchText: "",
    }
  },
  methods: {
    fn() {
      console.log(this.searchText)
    },
  },
}
</script>
```

### v-text 文本插值 与 v-html html 结构插值

类似于 innerText 和 innerHTML

v-text 作用和双大括号一样,v-html 可以往标签内插入 html 结构

```vue
<template>
  <h1>>----------双大括号插值----------<</h1>
  <p>文字插值:{{ str }}</p>
  <p>html结构插值:{{ html }}</p>
  <h1>>----------v-text指令插值(只能往空标签内插值)----------<</h1>
  <p v-text="str"></p>
  <p v-text="html"></p>
  <h1>>----------v-html指令插值----------<</h1>
  <p v-html="str"></p>
  <p v-html="html"></p>
</template>
<script>
export default {
  data() {
    return {
      str: "aaa",
      html: "<a href=''>a标签</a>",
    }
  },
}
</script>
<style>
* {
  text-align: center;
}
</style>
```

### v-once 一次性插值(取消动态绑定)

如果使用了 v-once 指令,执行一次性地插值,当数据改变时,插值处的内容不会更新。

```vue
<template>
  <div v-once>这个值将不会改变:{{ num }}</div>
  <button
    @click="
      num++
      console.log(num)
    ">
    +1
  </button>
</template>
<script>
export default {
  data() {
    return {
      num: 0,
    }
  },
}
</script>
<style>
* {
  text-align: center;
}
</style>
```

### 自定义指令

#### 语法

##### 局部指令

```javascript
export default {
  directives: {
    // 添加局部指令 v-focus
    focus: {
      mounted: (el) => el.focus(),
    },
  },
}
```

##### 全局指令

```javascript
// 创建vue实例,传入根组件
// 注意是根组件,根组件内的其他组件都可以用该指令
let app = createApp(LearnStyle)
// 定义一个指令 color,使用的时候 在标签中使用v-color属性
app.directive("color", {
  mounted(el, binding) {
    console.log(el, binding)
    el.style.color = binding.value
  },
})
// 把根组件挂载到 #app上
app.mount("#app")
```

#### 自定义指令钩子函数

```javascript
const myDirective = {
  // 在绑定元素的 attribute 前或事件监听器应用前调用
  created(el, binding, vnode, prevVnode) {},
  // 在元素被插入到 DOM 前调用
  beforeMount(el, binding, vnode, prevVnode) {},
  // 在绑定元素的父组件及他自己的所有子节点都挂载完成后调用 ()
  mounted(el, binding, vnode, prevVnode) {},
  // 绑定元素的父组件更新前调用
  beforeUpdate(el, binding, vnode, prevVnode) {},
  // 在绑定元素的父组件及他自己的所有子节点都更新后调用
  updated(el, binding, vnode, prevVnode) {},
  // 绑定元素的父组件卸载前调用
  beforeUnmount(el, binding, vnode, prevVnode) {},
  // 绑定元素的父组件卸载后调用
  unmounted(el, binding, vnode, prevVnode) {},
}
```

主要记忆 mounted

#### 钩子函数的参数

- `el`:指令绑定到的元素。这可以用于直接操作 DOM。
- `binding`:一个对象,包含以下属性。
  - `value`:传递给指令的值。例如在 `v-my-directive="1 + 1"` 中,值是 `2`。
  - `oldValue`:之前的值,仅在 `beforeUpdate` 和 `updated` 中可用。无论值是否更改,它都可用。
  - `arg`:传递给指令的参数 (如果有的话)。例如在 `v-my-directive:foo` 中,参数是 `"foo"`。
  - `modifiers`:一个包含修饰符的对象 (如果有的话)。例如在 `v-my-directive.foo.bar` 中,修饰符对象是 `{ foo: true, bar: true }`。
  - `instance`:使用该指令的组件实例。
  - `dir`:指令的定义对象。
- `vnode`虚拟 dom

## 实例属性 ($)

### $nextTick

- 类型: `function`
- 详细: 在下次 DOM 更新循环结束之后执行延迟回调。在修改数据之后立即使用它,然后等待 DOM 更新。

```javascript
// 在下次 DOM 更新循环结束之后执行延迟回调
// 可以使用async和await关键字
this.$nextTick(() => {
  // 访问 DOM
})
```

### $emit (vue 521)

- 类型: `function`
- 详细: 触发当前组件上的自定义事件。

```javascript
// 触发一个自定义事件,并向父组件传输数据data
this.$emit("my-event", data)
```

### $watch (vue 229)

- 类型: `function`
- 详细: 创建一个侦听器观察数据变化

```javascript
// 开启侦听器
let fn = this.$watch("num", (newVal, oldVal) => {
  console.log(newVal, oldVal)
})
// 停止侦听器
fn()
```

### $forceUpdate

- 类型: `function`
- 详细: 迫使 Vue 实例重新(rander)渲染虚拟 DOM
- 补充:
  - 调用$forceUpdate 后只会触发 beforeUpdate 和 updated 这两个钩子函数,不会触发其他的钩子函数。它仅仅影响实例本身和插入插槽内容的子组件,而不是所有子组件
  - 把当在 data 里没有显示的声明一个对象的属性,而是之后给该对象添加属性,这种情况 vue 是检测不到数据变化的,可以使用$forceUpdate()

```vue
<template>
  <!-- 既然渲染dom要用到name,那就显式声明一个,不要为了使用forceUpdate而像例子这样做 -->
  <div>{{ name }} <button @click="update">改变名字为李四</button></div>
</template>

<script>
export default {
  methods: {
    update() {
      this.name = "李四"
      // 如果不加forceUpdate 页面不会渲染
      // 迫使 Vue 实例重新(rander)渲染虚拟 DOM
      this.$forceUpdate()
    },
  },
}
</script>
```

### $refs

- 类型: `object`
- 详细: 一个对象,包含了所有注册过的子组件的实例。
- 补充:
  - 父组件可以通过 $refs 属性访问子组件实例,并调用实例方法。
  - 子组件可以通过 this.$parent.$refs 属性访问父组件实例,并调用实例方法。
  - 注意:$refs 只会在组件渲染完成后生效,在 mounted 钩子中访问 $refs 不会报错,但是在其他钩子中访问 $refs 可能会报错。
    在 dom 中使用 ref

```vue
<template>
  <div>
    <div ref="divRef">第一个一个普通的div</div>
    <div ref="divRef2">第二个个普通的div</div>
    <button @click="getRef">按钮</button>
  </div>
</template>
<script>
export default {
  methods: {
    getRef() {
      // 使用this.$refs, 拿到当前组件里所有的ref对象;
      console.log(this.$refs)
      console.log(this.$refs.divRef) //dom元素
    },
  },
}
</script>
```

在组件上使用 ref

```vue
<!-- 父组件 -->
<template>
  <div>
    <child-component ref="child" />
  </div>
</template>

<script>
export default {
  mounted() {
    // 拿到子组件实例
    console.log(this.$refs.child)
    // 调用子组件实例的方法
    this.$refs.child.doSomething()
  },
}
</script>

<!-- 子组件 -->
<template>
  <div>
    <button @click="doSomething">Do something</button>
  </div>
</template>

<script>
export default {
  methods: {
    doSomething() {
      console.log("do something")
    },
  },
}
</script>
```

## 插槽

### 默认插槽

```vue
<!-- 子组件设置插槽 -->
<template>
  <h1>插槽插入的内容: <slot></slot></h1>
</template>
<!-- 父组件使用插槽 -->
<template>
  <div>
    <son>你好</son>
  </div>
</template>
<!-- 父组件模板中的表达式只能访问父组件的作用域；子组件模板中的表达式只能访问子组件的作用域。 -->
<template>
  <div>
    <!--  只能访问到调用组件的那个vue实例的作用域 message是父组件的属性 -->
    <son>{{ message }} </son>
  </div>
</template>
```

### 具名插槽

具名插槽允许在子组件中定义多个命名插槽

-​ 在子组件的模板中,使用`<slot name="slotName"></slot>`标签定义具名插槽的位置,并为每个插槽指定一个唯一的名称。

- 父组件可以根据插槽的名称来插入内容。使用 `<template #slotName></template>`

```vue
<!-- 子组件中的操作 -->
<template>
  <!-- 默认插值中 -->
  <!-- slot的name属性为default,可以不写 -->
  <h1>name:<slot name="name"></slot></h1>
  <h1>age:<slot name="age"></slot></h1>
</template>
<script>
export default {
  data() {
    return {
      name: "six-flower",
      age: 21,
    }
  },
}
</script>
<!-- 父组件中的操作 -->
<template>
  <div>
    <son>
      <template #name> six-flower</template>
      <template #age> 21</template>
    </son>
  </div>
</template>
```

![](./images/assets/vue_插槽_具名插槽.png)

### 作用域插槽

```vue
<!-- 子组件操作 -->
<template>
  <button><slot :backTitle="{ name, age }"></slot></button>
</template>
<script>
export default {
  data() {
    return {
      name: "six-flower",
      age: 21,
    }
  },
}
</script>
<!-- 父组件操作 -->
<template>
  <div>
    <!-- 基础用法 -->
    <son>
      <template v-slot="slotProps"> {{ slotProps }}</template>
    </son>
    <br />
    <!-- 配合ES6解构命名语法 -->
    <son>
      <template v-slot="{ backTitle: Obj }"> {{ Obj }}</template>
    </son>
  </div>
</template>
```

![](./images/assets/vue_插槽_作用域插槽.png)

## 内置组件

### `<transition>`

`<Transition>` 是一个内置组件,这意味着它在任意别的组件中都可以被使用,无需注册。它可以将进入和离开动画应用到通过默认插槽传递给它的元素或组件上。进入或离开可以由以下的条件之一触发:

- 由 `v-if` 所触发的切换
- 由 `v-show` 所触发的切换
- 由特殊元素 `<component>` 切换的动态组件

以下是最基本用法的示例:

```vue
<!-- Transition 内部写想要发送动画的元素,按钮控制内容的显示和隐藏 -->
<template>
  <button @click="show = !show">切换</button>
  <Transition>
    <p v-if="show">hello</p>
  </Transition>
</template>
<script>
export default {
  data() {
    return {
      show: true,
    }
  },
}
</script>
<style lang="css">
p {
  background-color: red;
}
/* 进入动画的起始状态 */
.v-enter-from {
  opacity: 0;
}
/* 进入的这个过程的过渡效果- 进入动画的生效状态*/
.v-enter-active {
  transition: opacity 0.5s ease;
}
/* 进入动画的结束状态*/
.v-enter-to {
  opacity: 1;
}
/* 离开动画的起始状态 */
.v-leave-from {
  opacity: 1;
}
/* 离开动画的生效状态--整个离开动画过渡阶段 */
.v-leave-active {
  transition: opacity 1.5s ease;
}
/* 离开动画的结束状态 */
.v-leave-to {
  opacity: 0;
}
</style>
```

#### 过渡效果命名

```vue
<!-- 自定义过渡效果的命名 -->
<template>
  <button @click="show = !show">切换</button>
  <!-- 加个name属性 -->
  <Transition name="Tname">
  <Transition>
    <p v-if="show">hello</p>
  </Transition>
</template>
<script>
export default {
  data() {
    return {
      show: true,
    };
  },
};
</script>
<style lang="css">
/* 把v改为过渡效果的名称即可 */
.Tname-enter-from {
  opacity: 0;
}
.Tname-enter-active {
  transition: opacity 0.5s ease;
}
.Tname-enter-to {
  opacity: 1;
}
.Tname-leave-from {
  opacity: 1;
}
.Tname-leave-active {
  transition: opacity 1.5s ease;
}
.Tname-leave-to {
  opacity: 0;
}
</style>
```

#### animation 帧动画

```vue
<!-- 帧动画 -只关注动画执行过程- -->
<!-- 下面示例为name为bounce的Transition标签实现的帧动画 -->
<style lang="css">
/* .bounce-enter-active 进入动画的执行采用,这个关键帧bounce-in动画  :animation: bounce-in 0.5s */

/* .bounce-leave-active 立刻动画的执行采用,这个关键帧bounce-in动画的逆向过程:animation: bounce-in 0.5s reverse */
.bounce-enter-active {
  animation: bounce-in 0.5s;
}
.bounce-leave-active {
  animation: bounce-in 0.5s reverse;
}
@keyframes bounce-in {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.25);
  }
  100% {
    transform: scale(1);
  }
}
</style>
```

#### 出现时动画

如果你想在某个节点初次渲染时应用一个过渡效果,你可以添加 `appear`

```vue
<Transition appear>
  ...
</Transition>
```

#### transition-group 组件

会在一个 `v-for` 列表中的元素或组件被插入,移动,或移除时应用动画

```vue
<template>
  <div>
      <button @click="addItem">Add Item</button> <button @click="removeItem">Remove Item</button>​
    <!-- transition-group 组件,name属性为a,tag属性为ul渲染为ul标签 -->
    <transition-group
      name="a"
      tag="ul">
      <li
        v-for="item in items"
        :key="item.id">
        {{ item.text }}
      </li>
    </transition-group>
  </div>
</template>
<script>
export default {
  data() {
    return {
      items: [
        { id: 1, text: "Item 1" },
        { id: 2, text: "Item 2" },
        { id: 3, text: "Item 3" },
      ],
      nextId: 4,
    }
  },
  methods: {
    addItem() {
      this.items.push({ id: this.nextId, text: `Item ${this.nextId}` })
      this.nextId++
    },
    removeItem() {
      this.items.pop()
    },
  },
}
</script>
<style>
/* 激活时的过渡效果   */
/* a 为 name定义的值   */
/* -enter-active这部分写死     */
/* 过渡效果需要自己写 */
.a-enter-active,
.a-leave-active {
  transition: opacity 0.5s;
}
.a-enter-from,
.a-leave-to {
  opacity: 0;
}
li {
  background-color: red;
}
</style>
```

### `<Teleport>`

它可以将一个组件内部的一部分模板“传送”到该组件的 DOM 结构外层的位置去。这类场景最常见的例子就是全屏的模态框

```vue
<template>
  <div>
    <Teleport to="body"> <em>你好 世界！</em></Teleport>
  </div>
</template>
```

![](./images/assets/vue_内置组件_Teleport.png)

### `<component>`

component 是动态组件

有的时候,在不同组件之间进行动态切换是非常有用的,比如在一个多标签的界面里,像是选项卡一样,在实例、组件的模板中的某一个标签上,可以通过 is 属性来指定为另一个目标的组件,这个时候我们一般会使用 component 标签来占位、设置 is 属性来指定目标组件

```vue
<template>
  <div>
    <component :is="flag ? 'aa' : 'bb'"></component>
  </div>
  <button @click="flag = !flag">切换a和b</button>
</template>
<script>
import aa from "./components/a.vue"
import bb from "./components/b.vue"
export default {
  components: { aa, bb },
  data() {
    return {
      flag: true,
    }
  },
}
</script>
```

### `<keep-alive>`

keep-alive 组件包裹动态组件时,会缓存不活动的组件实例,而不是销毁它们,当组件重新激活时,会对其进行高效的更新。

```vue
<template>
  <div>
    <keep-alive>
      <!-- 将选项卡放在keep-alive组件中,可以缓存不活动的组件实例 -->
      <!-- 假设aa组件内有数据被改变了 -->
      <!-- 如果不使用keep-alive,每次切换都会重新渲染aa组件,这会导致它丢失其中所有已变化的状态 -->
      <!-- 使用keep-alive,组件不会重新渲染,以及变化的状态将会被保留 -->
      <component :is="flag ? 'aa' : 'bb'"></component>
    </keep-alive>
  </div>
  <button @click="flag = !flag">切换a和b</button>
</template>
<script>
import aa from "./components/a.vue"
import bb from "./components/b.vue"
export default {
  components: { aa, bb },
  data() {
    return {
      flag: true,
    }
  },
}
</script>
```

#### 包含和排除

``默认会缓存内部的所有组件实例,但我们可以通过`include`和`exclude` prop 来定制该行为。这两个 prop 的值都可以是一个以英文逗号分隔的字符串、一个正则表达式,或是包含这两种类型的一个数组。

include 用来设置是被缓存的组件

exclude 用来设置是不想被缓存的组件

```vue
<template>
  <!-- 以英文逗号分隔的字符串  想要缓存 a 和b组件 -->
  <KeepAlive include="a,b">
    <component :is="view" />
  </KeepAlive>

  <!-- 正则表达式 (需使用 `v-bind`)   想要缓存 a 和b组件  -->
  <KeepAlive :include="/a|b/">
    <component :is="view" />
  </KeepAlive>

  <!-- 数组 (需使用 `v-bind`)   想要缓存 a 和b组件  ,其他都不缓存-->
  <KeepAlive :include="['a', 'b']">
    <component :is="view" />
  </KeepAlive>

  <!-- 数组 (需使用 `v-bind`)   不想要缓存 a 和b组件 ,其他都缓存 -->
  <KeepAlive :exclude="['a', 'b']">
    <component :is="view" />
  </KeepAlive>
</template>
```

它会根据组件的 `name` 选项进行匹配,所以组件如果想要条件性地被 `KeepAlive` 缓存,就必须显式声明一个 `name` 选项。

```vue
<!-- 在组件内为组件设置name -->
<script>
// 组件a
export default {
  name: "a",
  // ...
};
// 组件b
export default {
  name: "b",
  // ...
};
</script>
```

#### 缓存组件的生命周期

当一个组件实例从 DOM 上移除但因为被 `<KeepAlive>` 缓存而仍作为组件树的一部分时,它将变为**不活跃**状态而不是被卸载。当一个组件实例作为缓存树的一部分插入到 DOM 中时,它将重新**被激活**。

一个持续存在的组件可以通过 `activated` 和 `deactivated` 选项来注册相应的两个状态的生命周期钩子

```vue
<template lang="">
  <div>新闻 组件A</div>
</template>
<script>
export default {
  created() {
    console.log("created A")
  },
  unmounted() {
    console.log("unmounted A ")
  },
  activated() {
    // 在首次挂载、以及每次从缓存中被重新插入的时候调用
    console.log("以及每次从缓存中被重新插入的时候调用")
  },
  deactivated() {
    // 在从 DOM 上移除、进入缓存、以及组件卸载时调用
    console.log("在从 DOM 上移除、进入缓存")
  },
}
</script>
```

### `<slot>`

插槽

### `<router-view>`

路由视图,用来显示匹配到的组件。

`<router-view></router-view>`

### `<router-link>`

切换路由,用来跳转路由,相当于 a 标签

`<router-link to="/about"></router-link>`

## 生命周期钩子函数(回调函数)

所有生命周期钩子函数的 this 上下文都会自动指向当前调用它的组件实例。注意:避免用箭头函数来定义生命周期钩子,因为如果这样的话你将无法在函数中通过 this 获取组件实例

生命周期图示
![生命周期图示](./images/assets/vue_生命周期钩子函数.png)

### 创建阶段

#### beforeCreate

在组件实例创建之前调用,此时组件的数据观测、事件监听和模板编译尚未开始,因此无法访问到组件的响应式数据、计算属性、方法等

#### created

在组件实例创建之后调用,此时组件的数据观测、事件监听和模板编译已经完成,可以访问到组件的响应式数据、计算属性、方法等,但是还没有挂载到 DOM 上,因此无法访问到组件的元素或子组件。常用于发送网络请求

### 挂载阶段

#### beforeMount

在组件挂载到 DOM 之前调用,此时组件的虚拟 DOM 已经创建,但是还没有插入到父容器中,可以对虚拟 DOM 进行一些操作或修改

#### mounted 在组件挂载到 DOM 之后调用,此时组件的虚拟 DOM 已经插入到父容器中,并且生成了真实的 DOM

节点,可以访问到组件的元素或子组件,并且可以执行一些需要 DOM 的操作或者 ajax 请求。

- 我们在此时可以去获取节点信息,做 Ajax 请求,对节点做一些操作

### 更新阶段

#### beforeUpdate

在组件更新之前调用,此时组件的数据已经发生变化,但是还没有更新到 DOM 上,可以在这个钩子中获取更新前的状态,并进行一些比较或逻辑处理。

- 我们可以在此时访问现有的 Dom,手动移除一些添加的监听事件

#### updated

在组件更新之后调用,此时组件的数据已经更新到 DOM 上,并且完成了重新渲染,可以在这个钩子中获取更新后的状态,并进行一些后续操作或效果处理。**不能在这个生命周期里做响应式操作,否则会死循环**

- 不建议在此时进行数据操作,避免进入死循环

### 卸载阶段

#### beforeUnmount

在组件卸载之前调用,此时组件还处于可用状态,可以在这个钩子中执行一些清理操作,如移除事件监听器、取消网络请求、停止定时器等。

- 在此时可以做一些操作,比如销毁定时器,解绑全局事件,销毁插件对象等

#### unmounted

在组件卸载之后调用,此时组件已经从 DOM 中移除,并且停止了所有的响应式效果和事件监听,无法再访问到组件的任何属性或方法。

## 路由

### 配置项

- mode:路由模式,默认值为 hash,可选值: hash, history, abstract
- base:应用的基路径,默认值: '/'
- linkActiveClass:激活的类名,默认值: 'router-link-active'
- linkExactActiveClass:精确激活的类名,默认值: 'router-link-exact-active'
- scrollBehavior:滚动行为,默认值: (to, from, savedPosition) => { return savedPosition || { x: 0, y: 0 } }
- routes:路由配置数组,数组的每一项都是一个路由对象,包含以下属性:
  - path:路由路径,必填
  - component:组件,必填
  - name:路由名称,可选
  - components:命名视图组件,可选
  - redirect:重定向,可选
  - beforeEnter:路由独享的守卫,可选
  - meta:元信息,可选
  - children:嵌套路由,可选
  - props:动态路径参数,可选

### 基本使用

main.js 文件

```javascript
import "./images/assets/main.css"

import { createApp } from "vue"
import App from "./App.vue"
// 导入路由
import router from "./router"

const app = createApp(App)
// 使用路由
app.use(router)

app.mount("#app")
```

路由 js 文件

```javascript
// 导入路由插件内的函数
import { createRouter, createWebHistory } from "vue-router"
// 导入组件
import HomeView from "../views/HomeView.vue"
//
const router = createRouter({
  // 路由模式
  history: createWebHistory(import.meta.env.BASE_URL),
  // 路由配置
  routes: [
    {
      // 路由路径
      path: "/",
      // 路由名称
      name: "home",
      // 路由组件
      component: HomeView,
    },
  ],
})
// 导出路由
export default router
// 这里最后导出了router对象,router对象其实是一个vue插件,我们想要在vue中使用这个路由插件,需要在我们项目的入口文件,main.js 中挂载这个router插件,才能在我们的vue项目中使用路由功能
```

#### 示例

```vue
<!-- App.vue -->
<template>
  <!-- 路由视图router-view,用来显示匹配到的组件 -->
  <div><router-view></router-view></div>
  <div>
    <!-- 路由链接router-link,用来跳转路由 -->
    <router-link to="/1">index1</router-link>
    <router-link to="/2">index2</router-link>
    <router-link to="/3">index2</router-link>
  </div>
</template>
<!-- index.js -->
<script>
import { createRouter, createWebHistory } from "vue-router"
import index1 from "@/views/index1.vue"

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: "/1",
      name: "index1",
      component: index1,
    },
    {
      path: "/2",
      name: "index2",
      component: () => import("@/views/index2.vue"),
    },
    {
      path: "/3",
      name: "index3",
      component: () => import("@/views/index3.vue"),
    },
  ],
})

export default router
</script>
```

### 路由模式

#### hash 模式

使用 URL 的 hash 来模拟一个完整的 URL,于是当 URL 改变时,页面不会重新加载,其显示的网路路径中会有 “#” 号,有一点点丑。比如http://example.com/#/about。这是最安全的模式,因为他兼容所有的浏览器和服务器。

底层原理--主要通过 window.onhashchange 监听 hash 值变化-- 改变页面组件

```javascript
import { createRouter, createWebHashHistory } from "vue-router"
const router = createRouter({
  // 使用方法
  history: createWebHashHistory(),
})
```

#### history 模式

底层原理 --依赖于 Html5 的 history,pushState API(改变地址栏的地址,但是不刷新页面)当你使用 history 模式时,URL 就像正常的 url,例如 `http://yoursite.com/user/id`,也好看！不过这种模式要玩好,还需要后台配置支持。因为我们的应用是个单页客户端应用,如果后台没有正确的配置,当用户在浏览器直接访问 `http://oursite.com/user/id` 就会返回 404,这就不好看了。所以呢,你要在服务端增加一个覆盖所有情况的候选资源:如果 URL 匹配不到任何静态资源,则应该返回同一个 `index.html` 页面,这个页面就是你 app 依赖的页面。

```javascript
import { createRouter, createWebHistory } from "vue-router"
const router = createRouter({
  // 使用方法
  history: createWebHistory(),
})
```

#### abstract 模式

abstract 是 vue 路由中的第三种模式,本身是用来在不支持浏览器 API 的环境中,充当 fallback,而不论是 hash 还是 history 模式都会对浏览器上的 url 产生作用

### 路由跳转

#### 声明式路由导航

```html
<!--使用 router-link 组件进行导航 -->
<!--通过传递 `to` 来指定链接 -->
<!--`<router-link>` 将呈现一个带有正确 `href` 属性的 `<a>` 标签-->
<router-link to="/home">首页</router-link>
<!-- <router-link to="/find">发现</router-link> -->
<!-- 添加一个replace属性,不会留下历史记录,不能后退 -->
<router-link
  to="/find"
  replace
  >发现</router-link
>
<!-- 路由出口 -->
<!-- 路由匹配到的组件将渲染在这里 -->
<router-view></router-view>
```

#### 编程式路由导航

除了使用 `<router-link>` 创建 a 标签来定义导航链接,我们还可以借助 router 的实例方法,通过编写代码来实现页面跳转,在 app 中挂载路由以后,可以任意组件中通过 this.$router 获取路由实例对象

##### $router.push

切换视图有历史记录(即可以后退)

```vue
<template>
  <div>
    <button @click="link">首页</button>
    <!-- <button @click="$router.push('/home')">首页</button> -->
  </div>
</template>

<script>
export default {
  methods: {
    link() {
      /**
       * 使用 this.$router对象里的push方法
       * 接收一个路由地址作为参数
       * 跳转到home页面
       */
      this.$router.push("/home")
      // 参数也可以放对象
      // this.$router.push({ path: '/home' })
      // this.$router.push({ name: 'home' })
      // 对象中添加一个replace属性,设置为true,则不会留下历史记录,不能后退
      // this.$router.push({ path: '/home', replace: true })
    },
  },
}
</script>
```

##### $router.replace

切换视图没有历史记录(即不能后退)

```vue
<template>
  <div>
    <button @click="link">首页</button>
    <!-- <button @click="$router.replace('/home')">首页</button> -->
  </div>
</template>

<script>
export default {
  methods: {
    link() {
      /**
       * 使用 this.$router对象里的push方法
       * 接收一个路由地址作为参数
       * 跳转到home页面
       */
      this.$router.replace("/home")
      // 参数也可以放对象
      // this.$router.replace({ path: '/home' })
      // this.$router.replace({ name: 'home' })

      // 相当于
      // this.$router.push({ path: '/home', replace: true })
    },
  },
}
</script>
```

##### $router.go

前进或后退到浏览器历史记录中的特定页面

```javascript
// 向前移动一条记录,与 router.forward() 相同
this.$router.go(1)
// 返回一条记录,与 router.back() 相同
this.$router.go(-1)
// 刷新当前页面与 router.reload() 相同
this.$router.go(0)
// 如果没有那么多记录,静默失败
this.$router.go(-100)
this.$router.go(100)
```

##### $router.back

等同于浏览器的后退按钮,返回上一页面

```javascript
this.$router.back()
```

##### $router.forward

等同于浏览器的前进按钮,前进到浏览器历史记录中的下一页

```javascript
this.$router.forward()
```

### 路由传参

#### parmas 动态路径传参

```vue
<!-- 路由配置 -->
<script>
{
  path: "/:name/:id",
  component: () => import("@/views/index1.vue"),
  name: "six"
  // 这里的name属性是给路由起别名,方便使用
  }
</script>
<!-- 切换页面 -->
<template>
  <div><router-view></router-view></div>
  <button @click="$router.push({ name: 'six', params: { name: 'six-flower', id: 666 } })">按钮</button>
</template>
<!-- 跳转页面获取参数 -->
<template>
  <!-- params 传参要用 $route.params 接收参数 -->
  <!-- $route.params 是一个对象,包含了当前路由的所有参数 -->
  <div>{{ $route.params.name }}</div>
  <div>{{ $route.params.id }}</div>
</template>
```

#### query 动态查询参数

参数使用?进行拼接,参数之间用&隔开,不需要在路由表中定义

```vue
<!-- 路由配置 -->
<script>
{
  path: "/she",
  component: () => import("@/views/index1.vue"),
  name: "six"
  }
</script>
<!-- 切换页面 -->
<template>
  <div><router-view></router-view></div>
  <!-- 使用 query,可以直接把参数拼接到url地址中 -->
  <!-- <button @click="$router.push('/she?name=six-flower&id=666')">按钮</button> -->
  <!-- 也可以使用对象形式传参 -->
  <button @click="$router.push({ name: 'six', query: { name: 'six-flower', id: 666 } })">按钮</button>
</template>
<!-- 跳转页面获取参数 -->
<template>
  <!-- query 传参要用 $route.query 接收参数 -->
  <!-- $route.query 是一个对象,包含了当前路由的所有查询参数 -->
  <div>{{ $route.query.name }}</div>
  <div>{{ $route.query.id }}</div>
</template>
```

#### router 和 route 的区别

在页面中跳转用$router.push(有 r), 在页面中获取路由跳转的参数用 $route.params (没有 r)

router 指的的 vue-router 这个路由插件,route 值的是当前页面的路由配置对象

#### watch $route

使用带有参数的路由时需要注意的是,当用户从 `/article/1` 导航到 `/article/3` 时,**相同的组件实例将被重复使用,vueRouter 默认会使用刚刚创建的组件**
因为这样比起销毁再创建,更加高效,这样就会出现一个问题,那就是我们在切换路由时,组件的生命周期不会被触发,比如 mouted - created 钩子函数,这就导致一些组件的状态不会被重置

解决方法:watch $route

要对同一个组件中参数的变化做出响应的话,你可以简单地 watch 监听 `$route` 对象上的任意属性,在这个场景中,就是 `$route.params` 或者 `$route.query` ,当路由参数发生变化时,组件就会重新渲染。

```vue
<!-- 路由配置 -->
<script>
{
  path: "/:name/:id",
  component: () => import("@/views/index1.vue"),
  name: "six"
  // 这里的name属性是给路由起别名,方便使用
  }
</script>
<!-- 切换页面 -->
<template>
  <div><router-view></router-view></div>
  <button @click="$router.push({ name: 'six', params: { name: 'six-flower', id: 6 } })">按钮6</button>
  <button @click="$router.push({ name: 'six', params: { name: 'six-flower', id: 66 } })">按钮66</button>
  <button @click="$router.push({ name: 'six', params: { name: 'six-flower', id: 666 } })">按钮666</button>
</template>

<!-- 跳转页面获取参数 -->
<template>
  <!-- params 传参要用 $route.params 接收参数 -->
  <!-- $route.params 是一个对象,包含了当前路由的所有参数 -->
  <div>{{ $route.params.name }}</div>
  <div>{{ $route.params.id }}</div>
</template>
<script>
export default {
  created() {
    console.log(this.$route.params)
    // 监听路由参数变化
    this.$watch(
      // 这里需要用函数返回参数对象,否则会报错
      // 正常写法要传入字符串(字符串是属性名),所以这里只能用函数返回对象
      () => this.$route.params,
      function () {
        console.log(this.$route.params)
      }
    )
  },
}
</script>
```

### 嵌套路由

点击顶栏导航按钮。页面跳转。导航栏保持不变。页面改变,在一级路由的配置中添加 children 属性,属性值数数组,数组中存放多一个子路由配置对象

```vue
<!-- 路由配置 -->
<script>
routes: [
// 一级路由
  {
    path: "/",
    component: () => import("@/App.vue"),
  },
  {
    path: "/home",
    component: () => import("@/views/index.vue"),
    name: "home",
    children: [
      // 二级路由
      {
        path: "/1",
        component: () => import("@/views/1.vue"),
      },
      {
        path: "/2",
        component: () => import("@/views/2.vue"),
      },
      {
        path: "/3",
        component: () => import("@/views/3.vue"),
      },
    ],
  },
],
</script>
<!-- 首页 -->
<template>
  <div><router-view></router-view></div>
  <button @click="$router.push('/home')">gohome</button>
</template>
<!-- home页面 -->
<template>
  <router-view></router-view>
  <router-link to="/1">1</router-link>
  <router-link to="/2">2</router-link>
  <router-link to="/3">3</router-link>
</template>
```

### 路由重定向 redirect

所谓路由重定向,就是用户在访问地址 A 的时候,我们让页面强制跳转到另一个地址。

```javascript
// 比如我们现在用户打开网页的时候默认是根路径 / ,我想要显示 /home 这个地址,这个时候也可以添加一条路由规则。路径是根,redirect 属性后面根重定向的地址。
route: [
  { path: "/", redirect: "/home" },
  { path: "/home", component: Home },
]
```

### props 解耦

应用场景:既可以当作一个路由页面,又可以当作一个普通组件

路由页面用路由参数传值,普通组件用 属性传值

```vue
<!--  路由配置 -->
<script>
{
      path: "/:name/:id",
      component: () => import("@/views/index.vue"),
      name: "six",
      // 设置 props 为 true,表示将路由参数传递给组件
      props: true,
    },
</script>
<!-- 传参数 -->
<template>
  <div><router-view></router-view></div>
  <button @click="$router.push({ name: 'six', params: { name: 'six-flower', id: 6 } })">按钮6</button>
  <button @click="$router.push({ name: 'six', params: { name: 'six-flower', id: 66 } })">按钮66</button>
  <button @click="$router.push({ name: 'six', params: { name: 'six-flower', id: 666 } })">按钮666</button>
</template>
<!-- 组件接收参数 -->
<template>
  <div>{{ name }}</div>
  <div>{{ id }}</div>
</template>
<script>
export default {
  props: ["name", "id"],
}
</script>
```

### 404 页面

vue-router 提供了一个全局的 afterEach 钩子函数,可以用来处理路由未匹配到页面的情况,比如 404 页面。

```vue
<!-- 配置404页面 -->
<script>
{
// 固定
  path: "/:pathMatch(.*)",
  // 准备好的404页面路径
  component: () => import("@/views/notFound.vue"),
  },
</script>
之后在任意页面访问不存在的页面时,就会跳转到 404 页面。
```

### 路由守卫

路由守卫就是在路由跳转前后进行一些操作,比如判断是否登录,是否有权限,是否是管理员等。

我们可以理解为一种回调函数-或者是事件,意思就是路由跳转过程中的一些钩子函数(跳转过程会触发的一些函数)！在函数中我们可以控制到底要不要跳转,也可以传递参数

#### 全局守卫

##### beforeEach 全局前置守卫

路由配置

```javascript
router.beforeEach((to, from, next) => {
  //to 将要访问的路径
  //from 代表从哪个路径跳转而来
  //next 是一个函数,表示放行 使用后beforeEach 必须得调用next
  // next() 放行
  //next('/login') 强制跳转
  //next(false) 不跳转
})
```

```javascript
import { createRouter, createWebHistory } from "vue-router"

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: "/",
      component: () => import("@/views/index.vue"),
    },
    {
      path: "/:name/:id",
      component: () => import("@/views/index.vue"),
      name: "six",
      props: true,
    },
    {
      path: "/:pathMatch(.*)",
      component: () => import("@/views/notFound.vue"),
    },
  ],
})

router.beforeEach((to, from) => {
  // to: Route: 即将要进入的目标 路由对象 {path:'',params:'',name}`
  console.log("to:", to)
  console.log("from:", from)
  // return false 取消路由跳转
  // return true 允许路由跳转(默认)
  if (to.href == "/six-flower/66") {
    // 当跳转到 /six-flower/66 时,重定向到 /
    return { path: "/" }
  }
})

export default router
```

![](/images/assets/vue_路由_路由守卫_全局守卫_beforeEach.png)

##### beforeResolve 全局解析守卫

这个钩子和 beforeEach 类似,也是路由跳转前触发,参数也是 to,from,next 三个,和 beforeEach 区别官方解释为:

区别是在导航被确认之前,同时在所有组件内守卫和异步路由组件被解析之后,解析守卫就被调用。

即在 beforeEach 和 组件内 beforeEnter 之后,afterEach 之前调用

```javascript
router.beforeResolve((to, from, next) => {
  //to 将要访问的路径
  //from 代表从哪个路径跳转而来
  //next 是一个函数,表示放行 使用后beforeEach 必须得调用next
  // next() 放行  next('/login') 强制跳转
  // next(false) 不跳转
})
```

##### afterEach 全局后置守卫

他是在路由跳转完成后触发,参数包括 to,from 没有了 next(参数会单独介绍),他发生在 beforeEach 和 beforeResolve 之后,beforeRouteEnter(组件内守卫)之前

```javascript
router.afterEach((to, from) => {
  //to 将要访问的路径
  //from 代表从哪个路径跳转而来
})
```

#### beforeEnter 组件内前置守卫

beforeEnter 在路由配置中定义的钩子函数,它会在路由被激活之前调用。它和全局前置守卫的参数一样,但是只对该路由生效。

```javascript
import { createRouter, createWebHashHistory } from "vue-router";

  // 使用 createRouter 创建路由实例
const router = createRouter({
  // 确定路由模式,当前使用hash模式
  history: createWebHashHistory(),
  //  定义路由表
  routes: [
    {
      // 路由地址
      path: "/a",
      // 对应路由显示组件
      component: () => import("./../a.vue"),
      beforeEnter: (to, from, next) => {
        next()
      },
});

// 导出
export default router;
```

`beforeEnter` 守卫 **只在进入路由时触发**,不会在 `params`、`query` 或 `hash` 改变时触发。例如,从 `/users/2` 进入到 `/users/3` 或者从 `/users/2#info` 进入到 `/users/2#projects`。它们只有在 **从一个不同的** 路由导航时,才会被触发。

#### 组件内守卫 (不常用)

##### beforeRouteEnter

路由进入之前调用,参数包括 to,from,next。该钩子在全局守卫 beforeEach 和独享守卫 beforeEnter 之后,全局 beforeResolve 和全局 afterEach 之前调用,该守卫内访问不到组件的实例,也就是 this 为 undefined。

```javascript
beforeRouteEnter (to, from, next) {
  // 这里还无法访问到组件实例,this === undefined
  next( vm => {
    // 通过 `vm` 访问组件实例
  })
}
```

##### beforeRouteUpdate

在当前路由改变时,并且该组件被复用时调用,可以通过 this 访问实例。参数包括 to,from,next。

对于一个带有动态参数的路径 /foo/:id,在 /foo/1 和 /foo/2 之间跳转的时候,组件实例会被复用,该守卫会被调用 当前路由 query 变更时,该守卫会被调用。

```javascript
beforeRouteUpdate (to, from) {

}
```

##### beforeRouteLeave

导航离开该组件的对应路由时调用,可以访问组件实例 this,参数包括 to,from,next。只有调用 next 才可以跳转

```javascript
beforeRouteLeave (to, from, next) {

}
```

beforeRouteLeave 这个离开守卫通常用来禁止用户在还未保存修改前突然离开。

未保存,可以弹出提示,该导航可以通过 `next(false)` 来取消。

### 路由元信息

#### 场景

如果项目中一些页面是需要登录的,一些是不需要登录的。这种需求,我们可以在路由对象中提前标记,并且在跳转之前做判断。这个需要用到路由元信息

路由元 meta 信息就是,在路由对象中增加一下属性,来记录一些额外的信息,常见的就是用来标记是一个路由是否登录。

#### 设置路由 meta 信息

定义路由的时候可以配置 `meta` 字段:

```javascript
const routes = [
  {
    path: '/posts',
    component: PostsLayout,
    children: [
      {
        path: 'new',
        component: PostsNew,
        // 只有经过身份验证的用户才能创建帖子
        meta: { requiresAuth: true }
      },
      {
        path: ':id',
        component: PostsDetail
        // 任何人都可以阅读文章
        meta: { requiresAuth: false }
      }
    ]
  }
]
```

#### 获取路由 meta 信息

下面例子展示在全局导航守卫中检查元字段:

```javascript
router.beforeEach((to, from) => {
  // 我们可以在登录的时候存入 1
  let isLogin = localStorage.getItem("isLogin")
  if (to.meta.requiresAuth && isLogin == "0") {
    // 此路由需要授权,请检查是否已登录
    // 如果没有,则重定向到登录页面
    return {
      path: "/login",
    }
  }
})
```

### 路由懒加载

路由懒加载就是在路由被访问时才加载对应组件,而不是一开始就加载所有路由组件。

```javascript
 {
      path: "/me",
      // component: Me,
      // 动态导入
      component: () => import("../views/Me.vue"),
    },
```

一般来说,对所有的路由**都使用动态导入**是比较好的方案

### 动态添加路由

对路由的添加通常是通过 routes 选项来完成的,但是在某些情况下,你可能想在应用程序已经运行的时候添加或删除路由。

动态路由主要通过两个函数实现。router.addRoute() 和 router.removeRoute()。

#### 添加路由

```javascript
/**
 * 接收一个参数是路由对象
 */
router.addRoute({ path: "/about", component: About, name: "about" })

/**
 * 添加嵌套路由
 * 第一个参数是父级路由的name
 * 第二个参数 是路由对象
 */
router.addRoute("父级路由Name", { path: "/about", component: About, name: "about" })
```

主要使用场景就是在登录以后,才会返回,需要显示的路由菜单

比如登录以后,返回了用户的具有操作权限的目录菜单,这个时候,我们就需要动态的添加进去路由中

#### 删除路由

```javascript
// 传入需要删除的路由的name
router.removeRoute("路由name")
```

# Vue3 组合式 API

## 模板语法

插值语法,v-if v-for v-model 等指令 跟 vue2 一样

## `<script setup> </script>` 语法糖

```vue
<script setup>
import { ref } from "vue"
// 定义响应式数据,类似选项式api中data选项中的数据
let count = ref(10)
function fn() {
  // setup中 使用的时候需要加.value (因为要追踪改变)
  count.value++
}
</script>
<template lang="">
  <!-- 模板中使用使用不需要加.value -->
  {{ count }}
</template>
```

1.解决了 vue2 的 data 和 methods 方法相距太远,无法组件之间复用

2.提供了 script 标签引入共同业务逻辑的代码块,顺序执行

3.script 变成 setup 函数,默认直接暴露给模版

4.组件直接挂载,无需注册

## 定义响应式数据

ref 和 reactive 这两个函数都是类似 vue2 中的 data 一样,用于生成响应式数据。

vue3 中实现响应式数据的方法是就是使用 ref 和 reactive,所谓响应式就是界面和数据同步,能实现实时更新

**推荐用 const 声明响应式数据,因为 const 声明的变量,只能被赋值一次,不能被重新赋值,这样可以避免一些错误**

### reactive

reactive():接受**对象类型数据的参数**传入并返回一个**响应式的对象**

```vue
<template>
  <button @click="fn">{{ obj.a }}</button>
</template>
<script setup lang="ts">
import { reactive } from "vue"
// reactive()接受对象类型数据的参数传入并返回一个响应式的对象,返回的对象具备参数对象一样的属性
const obj = reactive({ a: 1 })
function fn() {
  obj.a++
}
</script>
```

### ref

ref 函数是简单类型和对象类型都可以接受并返回一个响应式的对象

```vue
<template>
  <!-- 模板中使用的时候,不需要加.value,直接使用变量即可 -->
  <button @click="fn1">{{ obj.a }}</button>
  <button @click="fn2">{{ num }}</button>
  <button @click="fn3">{{ arr[0] }}</button>
</template>

<script setup lang="ts">
import { ref } from "vue"
const obj = ref({ a: 1 })
const num = ref(1)
function fn1() {
  //  ref创建的数据,js脚本中修改的时候需要加.value
  obj.value.a++
}
function fn2() {
  //  ref创建的数据,js脚本中修改的时候需要加.value
  num.value++
}
function fn3() {
  //  ref创建的数据,js脚本中修改的时候需要加.value
  arr.value[0]++
}
</script>
```

### 解构赋值

解构赋值要给被解构的变量赋值加.value,并且,解构出来的值没有响应式

```vue
<template>
  <h1>{{ obj }}</h1>
  <button @click="fn">{{ num }}</button>
</template>

<script setup>
import { ref, watch, watchEffect } from "vue"
const obj = ref({ num: 0 })
// 解构出来的值是静态数据，没有响应式
let { num } = obj.value
// 加一个数据深度监听来实时更新结构出来的值
// watch(
//   obj,
//   () => {
//     num = obj.value.num;
//   },
//   { immediate: true, deep: true }
// );
watchEffect(() => {
  num = obj.value.num
})
function fn() {
  obj.value.num++
}
</script>
```

## computed

computed 函数,参数是一个函数,参数返回一个对象,返回的对象的值,依赖于 return 的值,返回的值也是一个响应式对象

```vue
<template>
  <button @click="fn">变</button>
  <h1>{{ arr }}</h1>
  <h2>{{ num }}</h2>
</template>

<script setup lang="ts">
import { ref, computed } from "vue"
const arr = ref([1, 2, 3, 4, 5])
const num = computed(() => arr.value.reduce((num, item) => num + item, 0))
function fn() {
  // 修改了原始的数组,计算属性就会自动改变
  arr.value = arr.value.map((item) => Math.floor(Math.random() * 10))
}
</script>
```

## watch

watch 的作用与 vue2 中是一致的,侦听**一个或者多个数据**的变化,数据变化时执行回调函数。

额外的两个参数:

1. immediate(立即执行)
2. deep(深度侦听)

**监听的回调函数中的两个参数在使用的时候不需要加.value**

**如果在一个对象内只需要监听几个属性,第一个参数可以用箭头函数返回需要监听的对象的那个属性,而不需要监听整个对象(监听整个对象太消耗性能)**

```vue
<template>
  <h1>arr:{{ arr }}</h1>
  <button @click="fn1">num:{{ num }}</button>
  <button @click="fn2">obj:{{ obj }}</button>
</template>

<script setup>
import { ref, watch, computed } from "vue"
const num = ref(1)
const obj = ref({ num: 1 })
const arr = computed(() => [num.value, obj.value])
function fn1() {
  num.value++
}
function fn2() {
  obj.value.num++
}
watch(num, (newnum, oldnum) => {
  console.log("--------------分割线1--------------")
  console.log(`我是监听的num;旧的:${oldnum}新的:${newnum}`)
})
watch(
  obj,
  (newobj, oldobj) => {
    // 注意,对象类型存的是地址,获取到的oldobj和newobj是一样的
    // 注意初始时旧的是undefind,所以使用的immediate时,如果取旧值会报错
    console.log("--------------分割线1--------------")
    console.log(`我是监听的obj;旧的:${oldobj.num}新的:${newobj.num}`)
  },
  {
    // immediate: true,
    deep: true,
  }
)
watch(
  arr,
  // 注意immediate属性,如果使用该属性解构时,旧属性会报错
  ([a1, a2], [a3, a4]) => {
    console.log("--------------分割线1--------------")
    console.log(`我是监听的arr;num旧的:${a1}新的:${a3}`)
    console.log(`我是监听的arr;objnum旧的:${a2.num}新的:${a4.num}`)
  },
  {
    // immediate: true,
    deep: true,
  }
)
</script>
```

## watchEffect

watchEffect` 用于立即执行一个函数,并响应该函数内部所依赖的所有响应式引用或计算属性。

- **自动侦测依赖 vs 显式声明**: `watchEffect` 自动侦测函数内所用到的所有响应式引用,而 `watch` 需要你明确指定要观察的引用。
- **立即执行**: `watchEffect` 创建时会立即执行一次,而 `watch` 默认不会,除非设置了 `immediate` 选项。
- **旧值与新值**: `watch` 回调提供新值和旧值,而 `watchEffect` 不提供。
- **多源观察**: `watch` 可以观察多个源,但 `watchEffect` 观察函数内的所有响应式引用。

**使用场景**:

- **使用 watch**:
  - 当你需要访问旧值和新值。
  - 当你需要基于条件观察某个值。
  - 当你需要更细粒度的控制(如 `deep`, `flush` 等选项)。
- **使用 watchEffect**:
  - 当你需要依赖多个响应式引用,并希望所有这些改变都触发同一函数。
  - 当你不需要旧值,只关心新值

```vue
<template>
  <h1>{{ arr }}</h1>
  <button @click="num++">{{ num }}</button>
  <button @click="obj.a++">{{ obj.a }}</button>
</template>

<script setup>
import { ref, computed, watchEffect } from "vue"
const num = ref(1)
const obj = ref({ a: 1 })
const arr = computed(() => [num.value, obj.value])
watchEffect(() => {
  console.log(num.value)
  console.log(obj.value)
  console.log(arr.value)
})
</script>
```

## 生命周期

![](/images/assets/vue_生命周期钩子函数.png)

组合式的 setup 脚本等同于 原来的 beforeCreate/created

| 选项式 API           | 组合式 API      |
| -------------------- | --------------- |
| beforeCreate/created | setup           |
| beforeMount          | onBeforeMount   |
| mounted              | onMounted       |
| beforeUpdate         | onBeforeUpdate  |
| updated              | onUpdated       |
| beforeUnmount        | onBeforeUnmount |
| unmounted            | onUnmounted     |

以 onMounted 函数为例

使用 onMounted 是一个函数,用来监听挂载完成这个生命周期,onMounted 的参数是一个函数,组件规则完毕以后,这个监听函数会执行,多次监听时传入的回调会在时机成熟时**依次执行**

```vue
<template>
  <div>onMounted</div>
</template>

<script setup>
//1.导入
import { onMounted } from "vue"
//2.执行函数
onMounted(() => {
  console.log("onMounted1")
})
onMounted(() => {
  console.log("onMounted2")
})
</script>
```

## ref 模版引用

```vue
<template>
  <!-- 用ref标记div标签 -->
  <div ref="domdiv"></div>
  <!-- 使用自定义组件并用ref标记 -->
  <my ref="mycom">我的组件</my>
</template>

<script setup>
import { ref, onMounted } from "vue"
// 导入自定义组件
// setup中使用另一个组件的时候,只需要导入,不需要注册组件,会自动注册对应组件
import my from "./components/my.vue"
const domdiv = ref(null)
const mycom = ref(null)
onMounted(() => {
  console.log(domdiv.value)
  console.log(mycom.value)
})
</script>
```

如果想看到自定义组件的状态或方法需要在子组件上主动暴露出来

```vue
<template>
  <h1><slot></slot></h1>
</template>

<script setup>
import { ref } from "vue"
const a = ref(1)
function b() {
  console.log(a)
}
// 主动暴露a和b
defineExpose({ a, b })
</script>
```

## 组件

setup 中使用另一个组件的时候,只需要导入,不需要注册组件,会自动注册对应组件

### 父传子通信

1. 子组件内部通过 defineProps 定义需要接收的参数
2. 父组件给子组件绑定属性

```vue
<!-- 父组件中与vue2一样,用属性传值 -->
<template>
  <!-- 模板里使用不需要用props -->
  <!-- 也不需要声明变量存储props -->
  <h1>{{ title }}</h1>
  <h1><slot></slot></h1>
</template>

<script setup>
import { defineProps } from "vue"
// props里面获取父组件传来的属性,并自带响应式
const props = defineProps({
  title: {
    type: String,
    default: "你干嘛",
  },
})
// 代码中使用要用props
console.log(props.title)
</script>
```

### 子传父通信

1. 子组件内部,通过 defineEmits 定义事件 ,返回一个 emit 方法用于在适当的时候触发事件
2. 父组件中给子组件标签通过@绑定事件

```vue
<template>
  <!-- 触发自定义事件并传值 -->
  <!-- 父组件使用方法和vue2一致,事件对象($event)就是传过去的数据 -->
  <button @click="emit('click', ++num)"><slot></slot></button>
</template>

<script setup>
import { defineEmits, ref } from "vue"
const num = ref(1)
// 自定义事件,声明emit以后可以用emit在合适的时机触发定义的事件
const emit = defineEmits(["click"])
</script>
```

### 祖孙传值

父组件提供(provide)数据(生产数据),任意底层组件注入(inject)数据(消费数据)

```vue
<!-- 父 -->
<template>
  <my></my>
  <button @click="fn">父组件传值改变</button>
</template>

<script setup>
import { ref, provide } from "vue"
import my from "./components/my.vue"
const num = ref(1)
// 父组件为子孙组件传了一个响应式数据
// 传递响应式数据，父子孙组件间都可以修改数据
provide("num1", num)
// 父组件为子孙组件传了一个静态数据
provide("num2", num.value)
function fn() {
  num.value++
}
// 父组件为子孙组件传了一个函数
provide("fn", fn)
</script>

<!-- 子孙 -->
<template>
  <h1>{{ num1 }}</h1>
  <h2>{{ num2 }}</h2>
  <button @click="fn">子组件按钮</button>
</template>

<script setup>
import { inject } from "vue"
// 子孙组件接收父组件传过来的数据和函数
const num1 = inject("num1")
const num2 = inject("num2")
const fn = inject("fn")
</script>
```

### v-model

vue3.3 新增的 defineModel 宏,简化了组件的 v-model 使用方法。

从 Vue 3.4 开始,推荐的实现方式是使用 `defineModel()` 宏

`defineModel` 是一个便利宏。编译器将其展开为以下内容:

- 一个名为 `modelValue` 的 prop,本地 ref 的值与其同步；
- 一个名为 `update:modelValue` 的事件,当本地 ref 的值发生变更时触发。

```vue
<!-- 父组件 -->
<template>
  <!-- v-model命名 -->
  <my
    v-model:username="username"
    v-model:password="password"
    @input="fn"></my>
</template>

<script setup>
import { ref, provide } from "vue"
import my from "./components/my.vue"
const username = ref("")
const password = ref("")
function fn() {
  console.log("username:" + username.value)
  console.log("password:" + password.value)
}
</script>
<!-- 子组件 -->
<template>
  username:<input
    type="text"
    v-model="username"
    @input="emit('input')" />
  <br />
  password:<input
    type="text"
    v-model="password"
    @input="emit('input')" />
</template>

<script setup lang="ts">
import { defineEmits } from "vue"
// defineModel 宏,简化了组件的 v-model 使用方法
// 获取父组件传来的数据,并自带响应式
const username = defineModel("username")
const password = defineModel("password")
const emit = defineEmits(["input"])
</script>
```

### 用响应式 API 做简单状态管理

如果你有一部分状态需要在多个组件实例间共享,你可以使用 ref 或 reactive 来创建一个响应式对象,并将它导入到多个组件中:

```js
// store.js
import { reactive } from "vue"

export const store = reactive({
  count: 0,
})
```

```vue
<!-- ComponentA.vue -->
<script setup>
import { store } from "./store.js"
</script>

<template>From A: {{ store.count }}</template>
```

```vue
<!-- ComponentB.vue -->
<script setup>
import { store } from "./store.js"
</script>

<template>From B: {{ store.count }}</template>
```

现在每当 `store` 对象被更改时,`<ComponentA>` 与 `<ComponentB>` 都会自动更新它们的视图。现在我们有了单一的数据源。

然而,这也意味着任意一个导入了 `store` 的组件都可以随意修改它的状态:

虽然这在简单的情况下是可行的,但从长远来看,可以被任何组件任意改变的全局状态是不太容易维护的。为了确保改变状态的逻辑像状态本身一样集中,建议在 store 上定义方法,方法的名称应该要能表达出行动的意图:

```js
// store.js
import { reactive } from "vue"

export const store = reactive({
  count: 0,
  increment() {
    this.count++
  },
})
```

```vue
<template>
  <button @click="store.increment()">From B: {{ store.count }}</button>
</template>
```

除了我们这里用到的单个响应式对象作为一个 store 之外,你还可以使用其他响应式 API 例如 `ref()` 或是 `computed()`,或是甚至通过一个组合式函数来返回一个全局状态:

```js
import { ref } from "vue"

// 全局状态,创建在模块作用域下
const globalCount = ref(1)

export function useCount() {
  // 局部状态,每个组件内导入use函数，然后用函数返回状态，函数内声明响应式变量的是局部状态(闭包)
  const localCount = ref(1)

  return {
    globalCount,
    localCount,
  }
}
```

## 路由

### useRouter

前面路由配置等步骤不变

```vue
<template>
  <router-view></router-view>
  <router-link to="/">home</router-link>
  <router-link to="/about">about</router-link>
  <button @click="fn">home</button>
  <button @click="$router.push('/about')">about</button>
</template>

<script setup>
// 从vue-router引入 useRouter这个钩子
import { useRouter, useRoute } from "vue-router"
// 初始化这个钩子并赋值给router
const router = useRouter()
const route = useRoute()
function fn() {
  router.push("/")
}
</script>
```

这里这个 router 的使用方式和之前一样

### useRoute

```javascript
// 获取当前组件中 route 配置
import { useRoute } from "vue-router"
// 创建
const route = useRoute()
// 获取跳转的参数( this.$route)
// route.query 获取传递的搜索参数
// route.params 获取传递的动态id (需要在路由配置中配置/detail/:id)
```

## toValue()

toValue 是一个在 3.3 版本中新增的 API。它的设计目的是将 ref 或 getter 规范化为值。如果参数是 ref,它会返回 ref 的值；如果参数是函数,它会调用函数并返回其返回值。否则,它会原样返回参数

```javascript
import { ref, toValue } from "vue"

const count = ref(0)
const double = toValue(count * 2)
console.log(double) // 0

const obj = {
  a: 1,
  b: 2,
}
const objValue = toValue(obj)
console.log(objValue) // { a: 1, b: 2 }

const getter = () => 1
const getterValue = toValue(getter)
console.log(getterValue) // 1
```

## 组合式函数约定用驼峰命名法命名,并以“use”作为开头

# pinia

pinia 是 Vue 3.0 版本中新增的状态管理库,它提供了一种更加灵活的方式来管理状态,它提供了一种更加声明式的方式来管理状态,它提供了一种更加可测试的方式来管理状态。

## 安装

```bash
pnpm i pinia
# 本地存储插件
pnpm i pinia-plugin-persistedstate
```

```javascript
// main.ts
import { createApp } from "vue"
import { createPinia } from "pinia"
import piniaPluginPersistedstate from "pinia-plugin-persistedstate"

import App from "./App.vue"

const app = createApp(App)

app.use(createPinia().use(piniaPluginPersistedstate))

app.mount("#app")
```

```ts
// store.ts
// 代码示例
import { reactive } from "vue"
import { defineStore } from "pinia"

export const usemenuSelect = defineStore(
  "menu",
  () => {
    const menu = reactive({
      menuSelect: ["home"],
    })

    function set(menuSelect: string) {
      menu.menuSelect = [menuSelect]
    }
    function get() {
      return menu.menuSelect
    }
    return { menu, setmenu, getmenu } as {
      get: () => string[]
      set: (menuSelect: string) => void
    }
  },
  { persist: true }
)
```

### OpenSSL 安装与使用完整教程

---

#### **一、安装 OpenSSL**

##### Windows 系统

1. **下载安装包**
   访问 [OpenSSL 官方下载页](https://slproweb.com/products/Win32OpenSSL.html)

   - 推荐下载最新 `Win64 OpenSSL vX.X.X`（如 Light 版足够使用）

2. **安装步骤**

   - 运行安装程序 → 选择安装路径（默认 `C:\OpenSSL-Win64`）
   - 勾选 `Copy OpenSSL DLLs to The OpenSSL binaries directory`
   - 完成安装

3. **配置环境变量**
   - 右键【此电脑】→ 属性 → 高级系统设置 → 环境变量
   - 在 `Path` 中添加 OpenSSL 的 bin 目录（如 `C:\OpenSSL-Win64\bin`）
   - 验证安装：命令提示符运行
     ```bash
     openssl version
     ```

##### macOS 系统

```bash
# 通过 Homebrew 安装（推荐）
brew update
brew install openssl

# 配置环境变量（zsh 用户）
echo 'export PATH="/usr/local/opt/openssl@3/bin:$PATH"' >> ~/.zshrc
source ~/.zshrc

# 验证安装
openssl version
```

##### Linux 系统 (Ubuntu/Debian)

```bash
# 更新并安装
sudo apt update
sudo apt install openssl

# 验证安装
openssl version
```

---

#### **二、OpenSSL 基础使用**

##### 1. 生成私钥（RSA 2048 位）

```bash
openssl genrsa -out private.key 2048
```

- `private.key`：生成的私钥文件名

##### 2. 创建证书签名请求（CSR）

```bash
openssl req -new -key private.key -out request.csr
```

按提示输入信息：

```
Country Name (2 letter code) [XX]:CN
State or Province Name (full name) []:Beijing
Locality Name (eg, city) [Default City]:Beijing
Organization Name (eg, company) [Default Company Ltd]:YourCompany
Organizational Unit Name (eg, section) []:Dev
Common Name (eg, your name or server hostname) []:localhost  # ← 重要！填写域名或IP
Email Address []:<EMAIL>
```

##### 3. 生成自签名证书（有效期 365 天）

```bash
openssl x509 -req -days 365 -in request.csr -signkey private.key -out certificate.crt
```

- `certificate.crt`：最终生成的证书文件

##### 快速生成证书（一步到位）

```bash
openssl req -x509 -newkey rsa:2048 -nodes -keyout server.key -out server.crt -days 365
```

按提示填写信息后，同时生成：

- `server.key`（私钥）
- `server.crt`（证书）

```bash
# web 服务器证书
openssl req -x509 -newkey rsa:4096 -nodes -keyout web.sixflower.love.key -out web.sixflower.love.crt -days 3650 -subj "/C=CN/T=Beijing/L=Beijing/O=MyOrg/OU=Dev/CN=web.sixflower.love" -addext "subjectAltName=DNS:web.sixflower.love,DNS:web.sixflower.love,DNS:localhost,IP:127.0.0.1"
# api 服务器证书
openssl req -x509 -newkey rsa:4096 -nodes -keyout api.sixflower.love.key -out api.sixflower.love.crt -days 3650 -subj "/C=CN/T=Beijing/L=Beijing/O=MyOrg/OU=Dev/CN=api.sixflower.love" -addext "subjectAltName=DNS:api.sixflower.love,DNS:api.sixflower.love,DNS:localhost,IP:127.0.0.1"

```

---

#### **三、证书格式转换**

##### 1. PEM 转 PFX（用于 IIS 等场景）

```bash
openssl pkcs12 -export -out cert.pfx -inkey server.key -in server.crt
```

- 按提示设置导出密码

##### 2. 查看证书信息

```bash
openssl x509 -in server.crt -text -noout
```

---

#### **四、在开发服务器中使用证书**

以 Webpack DevServer 为例：

```javascript
const fs = require('fs')
const path = require('path')

module.exports = {
  devServer: {
    https: {
      key: fs.readFileSync(path.resolve(__dirname, 'ssl/server.key')),
      cert: fs.readFileSync(path.resolve(__dirname, 'ssl/server.crt')),
    },
    host: '0.0.0.0',
    port: 1314,
  },
}
```

---

#### **五、常见问题解决**

1. **浏览器提示 "不安全"**

   - 原因：自签名证书未被信任
   - 解决方案：
     - Chrome：页面输入 `thisisunsafe`（直接输入，无确认框）
     - 或手动导入证书到系统信任库（[教程](https://support.securly.com/hc/en-us/articles/360008547993-How-to-Trust-Self-Signed-Certificates)）

2. **ERR_SSL_VERSION_OR_CIPHER_MISMATCH**

   - 升级 OpenSSL 到最新版本
   - 重新生成证书：`openssl req -x509 -newkey rsa:4096 ...`

3. **文件路径错误**

   ```bash
   Error: ENOENT: no such file or directory
   ```

   - 使用绝对路径：`path.resolve(__dirname, 'ssl/server.crt')`

4. **证书过期**

   - 重新生成证书并增加有效期：`-days 730`

5. **使用自定义域名开发**

   - host 文件修改：地址：C:\Windows\System32\drivers\etc
   - 添加自定义域名 `127.0.0.1 mydomain.com`
   - 双击 .crt 文件安装, 选择安装到系统信任库

---

#### **六、高级应用**

##### 1. 为多域名生成证书（SAN 扩展）

```bash
openssl req -x509 -newkey rsa:2048 -nodes -keyout server.key -out server.crt -days 365 \
  -subj "/CN=MyProject" \
  -addext "subjectAltName=DNS:localhost,IP:127.0.0.1,DNS:mydomain.com"
```

##### 2. 移除私钥密码（自动化部署时）

```bash
openssl rsa -in encrypted.key -out decrypted.key
```

##### 3. 检查证书有效期

```bash
openssl x509 -enddate -noout -in server.crt
# 输出：notAfter=May 28 12:00:00 2026 GMT
```

---

> **最佳实践提示**
>
> - 开发环境使用自签名证书
> - 生产环境使用 [Let's Encrypt](https://letsencrypt.org/) 免费证书
> - 敏感文件（如 `.key`）加入 `.gitignore`

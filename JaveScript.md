[TOC]

# JavaScript 知识点

## 基础知识点

1.  概念

    JavaScript 是一门实现人机交互和前后端交互,运行在浏览器端的脚本语言。

    JS 的三部分组成:ES,BOM,DOM

    网页脚本语言的规范（ECMAScript）由 ECMA（欧洲计算机制造商协会）下辖的 ECMA-262 制定,JavaScript 是 ES 标准的具体实现,从 2015 年的 ES6（也称 ES2015）以后每年推出的新版本以年份命名。

2.  js 的书写位置

- 行内:

```javascript
// 在标签的事件内书写
<input
  type="button"
  value="点我"
  onclick="alert('hello world')"
/>
```

- 内部:

```javascript
// 在<script></script>内部书写
<script>alert('hello world');</script>
```

-外部:

```javascript
// 外部引入
<script src="路径"></script>
```

3.  注释

```javascript
// 单行注释  ctrl+/
/* 多行注释  ctrl+shift+/ */
```

## 数据类型

### 基础数据类型

#### Number 数值类型

包含一切数字和数值相关的内容;

- 整数:123、-456;
- 小数:3.14、-2.5;
- 科学计数法:`10e(+)5`10 乘 10 的五次方、`10e-5`10 乘 10 的负五次方;
- 二进制:0b01;
- 八进制:0o07;
- 十六进制:0x0f;
- 无穷:Infinity、-Infinity;
- 特殊数值类型 NaN（not a number）;

#### String 字符串类型

- 所有被引号（"",'',``）包裹起来的内容都是字符串类型;
- 模板字符串:\`${变量},字符串\`;
- 字符串的拼接:`字符串1 + 字符串2`;

##### 字符串的创建

```javascript
// 字面量创建字符串
// 单引号
let str1 = "hello world"
// 双引号
let str2 = "hello world"
// 反引号
let str3 = `hello ${name}`
// 构建函数创建字符串
let str4 = new String("hello world")
// 如果字符串中包含单引号,双引号,反引号,需要用转义符\转义
let str5 = 'I\'m a "teacher".'
```

##### 字符串的方法

**string.length:返回字符串的长度**

**所有的字符串方法都不会改变原字符串,而是返回一个新字符串**

1. includes():判断字符串是否包含指定内容,返回布尔值

```javascript
// 字符串使用此方法可以判断字符串内是否含有一段指定的字符串（区分大小写）
// 可以指定搜索的起始位置n
let str = "hello world"
console.log(str.includes("world")) // true
console.log(str.includes("world", 6)) // false
```

2. search():在字符串中查找指定内容,返回匹配项的索引,没有找到返回-1

```javascript
// 字符串使用此方法可以查找指定内容,返回匹配项的索引,没有找到返回-1
let str = "hello world"
console.log(str.search("world")) // 6
```

3. match():根据字符串或正则查找匹配项,用正则全局匹配可以返回一个数组否则返回伪数组

```javascript
// 字符串使用此方法可以查找正则表达式的匹配项,返回一个数组
// 如果没有找到匹配项,则返回null
let str = "hello world hello"
// 需要用全局匹配
console.log(str.match("hello")) // ['hello']
console.log(str.match(/hello/)) // ['hello']
// 上面两个返回的是一个伪数组(类似于对象),下面返回的是一个数组
console.log(str.match(/hello/g)) // ['hello', 'hello']
```

4. replace():在字符串中查找指定内容,并替换成另一段内容,返回替换后的字符串

```javascript
// 字符串使用此方法可以查找指定内容,并替换成另一段内容,返回替换后的字符串
// 用正则表达式的全局匹配（g）可以替换所有符合条件的字符串,不用正则表达式的全局匹配只能替换一次
let str = "hello world world"
console.log(str.replace("world", "universe")) // "hello universe world"
console.log(str.replace(/world/g, "universe")) // "hello universe universe"
```

5. charCodeAt():返回字符串中指定位置的字符的 Unicode 编码

```javascript
// 字符串使用此方法可以返回字符串中指定位置的字符的 Unicode 编码
// 空字符串编码返回NaN
let str = "hello world"
console.log(str.charCodeAt(0)) // 104
let str = ""
console.log(str.charCodeAt(0)) // NaN
```

6. indexOf():返回字符串中字符或字符串的第一个出现的索引,没有找到返回-1

```javascript
// 字符串使用此方法可以返回字符串中指定内容的第一个出现的索引,没有找到返回-1
// 从前往后找
// 可以指定搜索的起始索引n
let str = "hello world"
console.log(str.indexOf("l")) // 2
console.log(str.indexOf("ll")) // 2
console.log(str.indexOf("l", 3)) // 3
console.log(str.indexOf("l", 4)) // 9
console.log(str.indexOf("l", 10)) // -1
```

7. lastIndexOf():返回字符串中字符或字符串的最后一个出现的索引,从后往前找,没有找到返回-1

```javascript
// 字符串使用此方法可以返回字符串中指定内容的最后一个出现的索引,没有找到返回-1
// 从后往前找
// 可以指定搜索的起始索引n
let str = "hello world"
console.log(str.lastIndexOf("l")) // 9
console.log(str.lastIndexOf("ll")) // 2
console.log(str.lastIndexOf("l", 2)) // 2
console.log(str.lastIndexOf("l", 3)) // 3
console.log(str.lastIndexOf("l", 0)) // -1
```

8. split():将字符串按照指定分隔符分割成数组,返回分割后的数组

```javascript
// 字符串使用此方法可以将字符串按照指定分隔符分割成数组,返回分割后的数组
// 如果没有指定分隔符,则默认按空格分割
let str = "hello world"
console.log(str.split(" ")) // ["hello", "world"]
console.log(str.split()) // ["hello", "world"]
console.log(str.split("")) // ["h", "e", "l", "l", "o", " ", "w", "o", "r", "l", "d"]
```

9. slice():截取字符串,返回一个新数组,包含源数组中指定位置的元素

```javascript
// 字符串使用此方法可以截取字符串,返回一个新数组,包含源数组中指定位置的元素
// 传入的参数可以为负值,表示倒数第几个位置
let str = "hello world"
// 一个参数表示截取从这个参数到结尾所有的字符
console.log(str.slice(6)) // "world"
// 两个参数表示截取从开始位置到结束位置的字符,包左不包右
console.log(str.slice(0, 5)) // "hello"
// 负数表示倒数第几个位置
console.log(str.slice(-1)) // "d"
console.log(str.slice(-6, -2)) // "orl"
```

10. toLowerCase():将字符串全部转换为小写,返回新的字符串

```javascript
// 字符串使用此方法可以将字符串全部转换为小写,返回新的字符串
let str = "HELLO WORLD"
console.log(str.toLowerCase()) // "hello world"
```

11. toUpperCase():将字符串全部转换为大写,返回新的字符串

```javascript
// 字符串使用此方法可以将字符串全部转换为大写,返回新的字符串
let str = "hello world"
console.log(str.toUpperCase()) // "HELLO WORLD"
```

12. trim():去除字符串两端的空格,返回新的字符串

```javascript
// 字符串使用此方法可以去除字符串两端的空格,返回新的字符串
let str = "  hello world  "
console.log(str.trim()) // "hello world"
```

**下面的是不推荐用的方法,只做了解即可**

1. concat():字符串拼接,返回新的字符串(不好用,不如直接用+拼接)

```javascript
// 字符串使用此方法可以拼接字符串,返回新的字符串
let str1 = "hello"
let str2 = "world"
console.log(str1.concat(" ", str2)) // "hello world"
```

2.charAt():返回字符串中指定位置的字符(不好用,不如用\[索引\]取值)

```javascript
// 字符串使用此方法可以返回字符串中指定位置的字符
let str = "hello world"
console.log(str.charAt(0)) // "h"
```

3. substr():从索引开始截取 n 个字符(不好用,不如用 slice()截取)

```javascript
// 字符串使用此方法可以从索引开始截取 n 个字符
let str = "hello world"
console.log(str.substr(0, 5)) // "hello"
```

4. substring():截取开始索引到结束索引位置的字符串,包左不包右(不好用,不如用 slice()截取)

```javascript
// 字符串使用此方法可以截取字符串,返回一个新字符串,参数不能传负值
let str = "hello world"
console.log(str.substring(0, 5)) // "hello"
```

##### ASCII 编码

1. 字符编码:ASCII 编码是每个字符对应一个唯一的二进制编码,每个字符占用 7 位二进制,共 128 个字符编码,其中 32-126 是可显示字符,127 是退格键（Backspace）;
   <img src="./images/assets/ASCII控制字符.png">
   <img src="./images/assets/ASCII可显示字符.png">
2. 相关方法
   - String.fromCharCode():将 Unicode 编码转换为字符
   - String.charCodeAt():将字符转换为 Unicode 编码

##### JSON

JSON（JavaScript Object Notation）是一种轻量级的数据交换格式,它基于 ECMAScript 4.0 标准。它主要用于在网络上传输数据,特别适合于数据交换。

JSON 格式是纯文本,易于人阅读和编写,同时也易于机器解析和生成,并有效地表示各种数据结构。

1. 创建 JSON 对象

```javascript
// 字面量创建 JSON 对象
// json 数据 的 属性名 只能加双引号
// 如果属性值是字符串类型,只能用 双引号
// 数字-字符串-布尔值,null 可以转为 json 数据, 函数,undefined 是不能转为 json 数据,
// JSON数据中的对象与普通对象不同的是键名必须要用引号包起来,键值只能是字符串或者数字
// 普通对象用于js程序内部表示和操作数据,而json对象主要用于数据交换
let jsonObj = '{"name":"名字","age":12}'
let jsonArray = '[{"name":"名字","age":12},{"name":"名字","age":12}]'
```

2. 相关方法

- JSON.parse():将 JSON 字符串转换为对象
- JSON.stringify():将数组或对象转换为 JSON 字符串

```javascript
// JSON.parse() 将 JSON 字符串转换为对象
let jsonObj = '{"name":"名字","age":12}'
let obj = JSON.parse(jsonObj)
console.log(obj) //{ name: '名字', age: 12; }
console.log(typeof obj) // object

// JSON.stringify() 将数组或对象转换为 JSON 字符串
let obj = {
  name: "名字",
  age: 12,
}
let arr = [
  { name: "名字", age: 12 },
  { name: "名字", age: 12 },
]
let jsonObj = JSON.stringify(obj)
let jsonArr = JSON.stringify(arr)
console.log(jsonObj) // {"name":"名字","age":12}
console.log(typeof jsonObj) // string
console.log(jsonArr) // [{"name":"名字","age":12},{"name":"名字","age":12}]
console.log(typeof jsonArr) // string
```

#### Boolean 布尔类型

两个取值,真或者是假,分别是 true 和 false;

#### Null 空类型

null 空类型, 代表变量有值, 值为空 , 只有在变量赋值的时候才能得到 null;

#### Undefined 未定义类型

- undefined 类型, 代表的是未定义, 代表的是没有值;
- 在声明变量, 没有赋值的时候该变量的值为 undefined;
- 调用函数时,如果实参比形参多,没有被赋值的形参是 undefined;
- 如果一个函数没有设置返回值,则会默认返回 undefined;

### 复杂数据类型(引用数据类型)

#### Object 对象类型

对象类型,是一种复合数据类型,包含多个键值对,每个键值对都是一个属性,每个属性都有一个值;

##### 创建对象

```javascript
// 字面量创建对象
let obj1 = {
  name: "张三",
  age: 20,
  sayHello: function () {
    console.log("hello world")
    // 对象中的方法如果需要调用该对象中的键值,可以通过this.键名 调用
  },
}
// 构造函数创建对象（只能创建空对象）
let obj2 = new Object()
```

##### 对象的增删改查

```javascript
let obj = {
  name: "张三",
  age: 20,
  sayHello: function () {
    console.log("hello world")
  },
}
// 增
obj.sex = "男"
obj["city"] = "北京"
// 删
delete obj.age
delete obj["city"]
// 改
obj.age = 25
obj["name"] = "李四"
// 查
console.log(obj.name) // 李四
console.log(obj["age"]) // 25
// 访问对象中不存在的键名会返回undefined
```

##### 点语法和数组关联语法

- 当键名为纯数字或者键名中包含有特殊符号如（#-）时只能用数组关联语法
- 数组关联语法可以往方括号内传变量,可以动态获取属性值

```javascript
let obj = {
  name: "张三",
  age: 20,
}
let a = "name"
console.log(obj[a]) // 张三
```

##### 判断属性是否存在

- in:`key in obj`
- `obj.hasOwnProperty(key)`
- 存在返回 true,不存在返回 false

##### 对象的遍历

for...in 循环:遍历对象所有可枚举的（enumerable）属性,包括原型链上的属性

```javascript
let obj = {
  name: "张三",
  age: 20,
  sayHello: function () {
    console.log("hello world")
  },
}
for (let key in obj) {
  console.log(key, obj[key])
}
```

##### 自定义属性

1. 属性特征配置

```javascript
let obj = {}
// 自定义属性
// 注意下面这种方式创建对象,如果将三个配置属性都设置为true,还不如用字面量创建对象,下面这种方式是在有配置属性需要改为false时,才会用这种方式创建对象
Object.defineProperty(obj, "name", {
  // 是否可以被删除
  configurable: true,
  // 是否可以枚举,即被for...in循环遍历
  // 注意控制台查看属性时颜色变浅,表示不可枚举
  // 可以为对象中的方法设置不可枚举
  enumerable: true,
  // 是否可以修改值
  writable: true,
  // 上面三个默认都是false
  // 初始值
  value: "张三",
})
```

**简单封装可以方便使用**

```javascript
function defineProperty(obj, key, value){
  Object.defineProperty(obj, key, {
    value: value,
    writable: false,
    enumerable: false,
    configurable: false,
    // 三条默认值可以不写,但是写了可以方便随时修改
}
```

2. 属性存取

```javascript
let obj = {}
Object.defineProperty(obj, "name", {
  // 属性特征配置与属性存取配置不能同时存在
  // 取值
  get: function () {
    console.log("get name")
    // this指向当前对象
    return this._name
  },
  // 赋值
  set: function (value) {
    console.log("set name")
    // this指向当前对象
    this._name = value
  },
})
obj.name // get name
obj.name = "张三" // set name
console.log(obj) // {name: "张三"}
```

```javascript
let module = {}
function getStorage() {
  let list = localStorage.getItem("list")
  if (list) {
    list = JSON.parse(list)
  } else {
    list = []
  }
  return list
}
function setStorage(value) {
  localStorage.setItem("list", JSON.stringify(value))
  // set时设置数据,使数据发生改变,我们可以在这里数据改变时进行页面渲染
  // 这样的话只要有赋值操作就可以重新渲染页面
}
Object.defineProperty(module, "list", {
  get: getStorage,
  set: setStorage,
})
let list = module.list
// let {list} = module;
list.push("hello")
module.list = list
```

##### 静态方法

1. Object.assign(): 用于将一个或多个源对象的可枚举属性复制到目标对象。返回目标对象

```javascript
const obj1 = { a: 1, b: 2 }
const obj2 = { b: 4, c: 5 }
const mergedObj = Object.assign({ e: 6 }, obj1, obj2)
console.log(mergedObj) // { e: 6, a: 1, b: 4, c: 5 }
```

2. Object.create(): 用于创建一个新对象,指定其原型对象

```javascript
const proto = {
  greet: function () {
    console.log("Hello!")
  },
}
const obj = Object.create(proto)
obj.greet() // 'Hello!'
```

3. Object.keys(): 返回一个由给定对象自身可枚举属性的键名组成的数组

```javascript
const obj = { a: 1, b: 2, c: 3 }
const keys = Object.keys(obj) // ['a', 'b', 'c']
```

4. Object.values(): 返回一个由给定对象自身可枚举属性的值组成的数组

```javascript
const values = Object.values(obj) // [1, 2, 3]
```

5. Object.entries(): 返回一个给定对象自身可枚举属性的键值对数组

```javascript
const entries = Object.entries(obj) // [['a', 1], ['b', 2], ['c', 3]]
```

6. Object.defineProperty();

```javascript
let obj = { name: "wu", age: 21 }
// 第一个参数是要监听的对象
// 第二个参数是要监听对象的属性名
// 第三个参数是属性描述符--可以写getter和setter两个监听函数(传入两个回调函数)
// getter--get(),监听的回调函数,当获取监听对象的值的时候自动执行的代码
// setter--set(),监听的回调函数,当设置监听对象的值的时候自动执行的代码
Object.defineProperty(obj, "name", {
  get: function () {
    // get基本上没有什么用
    console.log("获取了")
    // 如果return obj.name 就会死循环
    return name
  },
  set: function (v) {
    // 形参接收修改的值
    console.log("修改了")
    console.log(v)
  },
})
obj.name = "six-flower"
console.log(obj.name)
```

**缺点:无法监听调用数据的方法**

```javascript
// (vue2)vue2用的defineProperty方法只能监听已经有的属性,并且不能监听数组的方法,不能监听通过数组下标修改元素(通过其他方式实现监听:如重写方法)
// (vue3)使用的proxy没有这些缺点
let push = Array.prototype.push
Array.prototype.push = function (...args) {
  // 改变this指向,指向调用push的数组
  push.call(this, ...args)
  console.log("调用了push")
}
```

#### Array 数组类型

数组类型,是一种复合数据类型,包含多个元素,每个元素都有一个索引,可以动态添加和删除元素,通常会往一个数组内装入相同类型的元素;

##### 创建数组

```javascript
// 字面量创建数组
let arr1 = [1, 2, 3, 4, 5]
// 构造函数创建数组
// 传入一个参数
let arr2 = new Array(5) // [,,,,]
// 传入多个参数
let arr2 = new Array(1, 2, 3, 4, 5) // [1, 2, 3, 4, 5]
```

##### 数组的增删改查

```javascript
let arr = [1, 2, 3, 4, 5]
// 增
arr[5] = 6 //为没有值的位置添加一个值
// 删
delete arr[0] //可以删掉对应位置里面的值,但是数组长度不会改变,不好用,不建议用
// 改
arr[0] = 10 //相当于赋值
// 查
console.log(arr[0]) // 10
// 访问数组中不存在的索引会返回undefined
```

##### 数组的方法

**array.length:返回数组的长度**

1. push():在数组末尾添加一个或多个元素,返回数组的新长度

```javascript
// arr.push(元素1, 元素2……);
let arr = [1, 2, 3, 4, 5]
console.log(arr.push(6, 7, 8)) // 8
console.log(arr) // [1, 2, 3, 4, 5, 6, 7, 8]
```

2. pop():删除数组的最后一个元素,返回删除的元素

```javascript
// arr.pop();
let arr = [1, 2, 3, 4, 5]
log(arr.pop()) // 5
console.log(arr) // [1, 2, 3, 4]
```

3. unshift():在数组开头添加一个或多个元素,返回数组的新长度

```javascript
// arr.unshift(元素1, 元素2……);
let arr = [1, 2, 3, 4, 5]
console.log(arr.unshift(0, -1)) // 7
console.log(arr) // [0, -1, 1, 2, 3, 4, 5]
```

4. shift():删除数组的第一个元素,返回删除的元素

```javascript
// arr.shift();
let arr = [1, 2, 3, 4, 5]
console.log(arr.shift()) // 1
console.log(arr) // [2, 3, 4, 5]
```

5. splice():在数组的指定位置添加或删除元素,返回被删除的元素组成的新数组

```javascript
// arr.splice(起始位置, 删除的元素个数, 要添加的元素1, 要添加的元素2……);
// 第一个参数是起始位置,第二个参数是删除的元素个数,第三个以及之后的参数是要添加的元素
let arr = [1, 2, 3, 4, 5]
// 一个参数, 删除数组中从指定位置开始到结尾的所有元素
console.log(arr.splice(2)) // [3, 4, 5]
console.log(arr) // [1, 2]
// 两个参数, 删除数组中从指定位置开始的指定个数的元素
arr = [1, 2, 3, 4, 5]
console.log(arr.splice(1, 2)) // [2, 3]
console.log(arr) // [1, 4, 5]
// 三个以及更多参数, 从指定位置开始,删除指定个数的元素,并用参数中指定的值替换掉,返回被删除的元素组成的新数组
arr = [1, 2, 3, 4, 5]
console.log(arr.splice(1, 2, "a", "b")) // [2, 3]
console.log(arr) // [1, "a", "b", 4, 5]
```

6. reverse():颠倒数组中元素的顺序,返回颠倒后的数组

```javascript
let arr = [1, 2, 3, 4, 5]
// arr.reverse();
console.log(arr.reverse()) // [5, 4, 3, 2, 1]
console.log(arr) // [5, 4, 3, 2, 1]
```

7. sort():对数组元素进行排序,默认是按照字母顺序排序,返回排序后的数组

```javascript
// arr.sort(比较函数);
// 默认排序,按照字母顺序排序
let arr = ["apple", "banana", "orange", "pear"]
console.log(arr.sort()) // ["apple", "banana", "orange", "pear"]
arr = [5, 2, 3, 1, 4]
console.log(arr.sort()) // [1, 2, 3, 4, 5]
console.log(arr) // [1, 2, 3, 4, 5]
// 自定义排序,按照数字大小排序
arr = [5, 2, 3, 1, 4]
console.log(
  arr.sort(function (a, b) {
    return a - b // 正数表示升序,负数表示降序,即b-a从大到小排序
  })
) // [1, 2, 3, 4, 5]
console.log(arr) // [1, 2, 3, 4, 5]
// 自定义排序,按照字符串长度排序
arr = ["apple", "banana", "orange", "pear"]
console.log(
  arr.sort(function (a, b) {
    return a.length - b.length
  })
) // ["pear", "apple", "banana", "orange"]
console.log(arr) // ["pear", "apple", "banana", "orange"]

// 随机排序
arr = [5, 2, 3, 1, 4]
console.log(
  arr.sort(function () {
    return Math.random() - 0.5
  })
) // [2, 4, 1, 3, 5]
```

8. concat():用于合并两个或多个数组,返回合并后的数组

```javascript
// arr.concat(数组1, 数组2……);
let arr1 = [1, 2, 3]
let arr2 = [4, 5, 6]
let arr3 = [7, 8, 9]
let arr4 = arr1.concat(arr2, arr3)
console.log(arr4) // [1, 2, 3, 4, 5, 6, 7, 8, 9]
```

9. join():用于将数组中的元素转换为字符串,并用指定分隔符连接,返回连接后的字符串

```javascript
// arr.join(连接符);
let arr = [1, 2, 3, 4, 5]
console.log(arr.join("-")) // "1-2-3-4-5"
```

10. slice():截取数组,用于从数组中返回一个新的数组,包含从指定位置开始到指定位置结束的元素,不改变原数组

```javascript
// arr.slice(起始位置, 结束位置);（包左不包右）
// 不写结束位置默认截取到最后
// 可以用负数
let arr = [1, 2, 3, 4, 5]
console.log(arr.slice(1, 3)) // [2, 3]
console.log(arr) // [1, 2, 3, 4, 5]
```

11. indexOf():查找数组中指定元素的第一个索引,如果不存在则返回 -1

```javascript
// arr.indexOf(元素, 指定起始位置);
// 从前向后查找
let arr = [1, 2, 3, 4, 5]
console.log(arr.indexOf(3)) // 2
console.log(arr.indexOf(6)) // -1
console.log(arr.indexOf(3, 4)) // -1
```

12. lastIndexOf():查找数组中指定元素的最后一个索引,如果不存在则返回 -1

```javascript
// arr.lastIndexOf(元素, 指定起始位置);
// 从后向前查找
let arr = [1, 2, 3, 4, 5, 3]
console.log(arr.lastIndexOf(3)) // 5
console.log(arr.lastIndexOf(6)) // -1
console.log(arr.lastIndexOf(3, 4)) // 2
```

13. includes():用来判断数组是否包含某个元素,返回布尔值

```javascript
// arr.includes(元素, 指定起始位置);
let arr = [1, 2, 3, 4, 5]
console.log(arr.includes(3)) // true
console.log(arr.includes(6)) // false
console.log(arr.includes(3, 4)) // false
```

14. forEach():用于遍历数组的每个元素,并对每个元素进行操作,没有返回值

```javascript
// arr.forEach(回调函数);
let arr = [1, 2, 3, 4, 5]
arr.forEach(function (value, index, arr) {
  console.log(value, index)
})
// 或者箭头函数
arr.forEach((value, index, arr) => console.log(value, index))
// 输出结果:
// 1 0
// 2 1
// 3 2
// 4 3
// 5 4
```

15. find():用于查找数组中满足条件的第一个元素,返回查找到的元素,如果没有查找到则返回 undefined

```javascript
// arr.find(回调函数);
let arr = [1, 2, 3, 4, 5]
let result = arr.find(function (value, index, arr) {
  return value % 2 === 0
})
// 或者箭头函数
let result = arr.find((value, index, arr) => value % 2 === 0)
console.log(result) // 2
```

16. findIndex():用于查找数组中满足条件的第一个元素的索引,返回查找到的索引,如果没有查找到则返回 -1

```javascript
// arr.findIndex(回调函数);
let arr = [1, 2, 3, 4, 5]
let result = arr.findIndex(function (value, index, arr) {
  return value % 2 === 0
})
// 或者箭头函数
let result = arr.findIndex((value, index, arr) => value % 2 === 0)
console.log(result) // 1
```

17. map():用于遍历数组的每个元素,并对每个元素进行操作,返回一个新的数组

```javascript
// arr.map(回调函数);
let arr = [1, 2, 3, 4, 5]
let newArr = arr.map(function (value, index, arr) {
  return value * 2
})
// 或者箭头函数
let newArr = arr.map((value, index, arr) => value * 2)
console.log(newArr) // [2, 4, 6, 8, 10]
```

18. filter():用于遍历数组的每个元素,并对每个元素进行操作,返回一个新的数组,该数组中的元素都是符合条件的元素

```javascript
// arr.filter(回调函数);
let arr = [1, 2, 3, 4, 5]
let newArr = arr.filter(function (value, index, arr) {
  return value % 2 === 0
})
// 或者箭头函数
let newArr = arr.filter((value, index, arr) => value % 2 === 0)
console.log(newArr) // [2, 4]
```

19. some():用于遍历数组的每个元素,并对每个元素进行操作,返回一个布尔值,表示数组中是否有元素符合条件

```javascript
// arr.some(回调函数);
let arr = [1, 2, 3, 4, 5]
let result = arr.some(function (value, index, arr) {
  return value % 2 === 0
})
// 或者箭头函数
let result = arr.some((value, index, arr) => value % 2 === 0)
console.log(result) // true
```

20. every():用于遍历数组的每个元素,并对每个元素进行操作,返回一个布尔值,表示数组中是否所有元素都符合条件

```javascript
// arr.every(回调函数);
let arr = [1, 2, 3, 4, 5]
let result = arr.every(function (value, index, arr) {
  return value % 2 === 0
})
// 或者箭头函数
let result = arr.every((value, index, arr) => value % 2 === 0)
console.log(result) // false
```

21. reduce():用于遍历数组的每个元素,并对每个元素进行操作,返回一个值,该值是通过回调函数计算出来的

```javascript
// arr.reduce(回调函数, 初始值);
// 遍历数组进行累积运算,第一次运算,prev为0,curr为arr[0],return返回结果赋值给第二次运算的prev,第二次运算,prev为第一次运算结果,curr为arr[1],以此类推,最后返回最后一次运算的结果
let arr = [1, 2, 3, 4, 5]
let result = arr.reduce(function (prev, curr, index, arr) {
  return prev + curr
}, 0)
// 或者箭头函数
let result = arr.reduce((prev, curr, index, arr) => prev + curr, 0)
console.log(result) // 15

// 二维数组扁平化
let arr = [
  [1, 2],
  [3, 4],
  [5, 6],
]
let result = arr.reduce((a, b) => a.concat(b))
console.log(result) // [1, 2, 3, 4, 5, 6]
```

##### 静态方法

1. Array.from():用于将类似数组的对象（包括类数组对象）转换为真正的数组,返回转换后的数组

```javascript
// Array.from(类数组对象);
let arrLike = {
  0: "a",
  1: "b",
  2: "c",
  length: 3,
}
let arr = Array.from(arrLike)
console.log(arr) // ["a", "b", "c"]
```

2. Array.isArray(),用于确认一个值是否是数组

```javascript
console.log(Array.isArray([])) // true
console.log(Array.isArray({})) // false
```

##### 数组使用容易遇到的问题

1. 数组塌陷
   数组塌陷是在遍历数组时,改变数组的长度引起的（例如删掉当前遍历到的元素,下个元素会继承当前元素的索引,从而跳过下一个元素的现象）

解决办法:

- 倒序遍历,即从后面开始遍历,可以解决该问题
- 在修改数组长度后,将索引变量减一个步长（例如 i--）
- 将修改索引变量的代码写在循环体内部（例如,没有执行修改数组长度的代码时 i++,执行时不改变索引变量）
- 用 filter 方法,直接筛选出一个新的数组,不改变原数组

##### 数组应用场景

1. 数组去重
   数组去重是指将数组中重复的元素去掉,返回一个没有重复元素的新数组

- 先将数组元素进行排序,这样一样的数据会挨在一起,接下来判断相邻的数据是否相等,相等就删除一个删除了之后 i--
- 声明一个新的数组,遍历旧数组,如果遍历到的元素在新数组中没有相等的值就添加到新数组,相反则不添加
- 双指针（嵌套循环）(也可以先排序),慢指针在前,快指针向后遍历,如果快指针遍历到的值与慢指针所指的值相同就删除快指针指着的值,然后索引遍历减 1
- 直接遍历整个数组,用 indexOf 方法,找到相等的元素的索引,删除,然后 i--,这样就会在删除一个重复元素后再次找重复元素,直到没有重复的元素才会执行到下一个元素
- 利用对象的键名是不能重复的特点,将数组的元素全部都作为键名放入对象中,然后声明一个新数组,遍历对象,将键名拿出来放入新数组,新数组内没有重复的值
- 利用 Set()数据结构
  Set 数据结构是一个类似于数组的数据结构,可以接受一个数组作为参数,Set 数据结构的特点是,所有元素都是唯一的,没有重复

```javascript
// Set方法去重
let arr = [1, 2, 3, 2, 4, 5, 3, 6]
// ...运算符可以将数组转换为可迭代对象（解构赋值）
let newArr = [...new Set(arr)]
console.log(newArr) // [1, 2, 3, 4, 5, 6]
```

#### Function 函数

函数是 JavaScript 中最重要的概念之一,函数可以将一段代码封装成一个整体,可以重复使用,可以提高代码的复用性,提高代码的可读性。

##### 函数定义

```javascript
// 声明式函数定义
function 函数名(参数1, 参数2, ……) {
  // 函数体
  return 返回值;
}
// 赋值式函数定义:
let 变量名 = function 函数名(参数1, 参数2, ……) {
  // 函数体
  return 返回值;
}
// 构造函数声明
let 函数名 = new Function(参数1, 参数2, ……, 函数体);
```

##### 实参与形参

形参:在创建函数时写在小括号里的变量,这些变量用于替换函数内部哪些可变的值

实参:在调用函数时写在小括号里的值,一旦函数调用,实参就会传给形参

- 形参的个数应当和实参的个数一致 一一对应
- 实参比形参多,多余的实参无效
- 形参比实参多,多余的形参默认取值 undefined

##### 返回值 return

return 可以将函数内部的值给返回,同时会终止函数的运行

一个函数内部有没有 return,其实都有返回值,如果不加 return 或者 return 后面没有值,默认返回值 undefined

在函数内部使用 return 和 return false 都能终止函数的执行,但是 return false 比 return 多一个优点

**return false 的作用**

- 能在终止函数时返回一个 false
- 在事件处理函数中返回 false 可以阻止事件默认行为,在 HTML 和 DOM 事件处理中特别有用,例如在表单提交时如果事件处理函数返回 false,则表单不会提交

##### 类数组对象 arguments

arguments 对象是一个类数组对象,它包含了函数调用时的所有参数,可以通过索引来访问,也可以通过 length 属性来获取参数的个数,不能用 forEach()等遍历,只能用索引来访问。

```javascript
function test(a, b) {
  console.log(arguments[0]) // 1
  console.log(arguments[1]) // 2
  console.log(arguments.length) // 2
}
test(1, 2)
```

arguments.callee
可以用来代表当前函数本身,一般应用在匿名函数中
开启严格模式会被禁用该功能（严格模式:"use strict"）

##### 函数的调用

1. 直接调用

```javascript
function test() {
  console.log("hello world")
}
test()
```

2. 事件驱动

```javascript
btn.onclick = function () {
  console.log("hello world")
}
```

##### 回调函数

```javascript
// 下面的这种模式就是回调函数
function A(fn) {
  console.log(1)
  fn()
}
A(function () {
  console.log(2)
})
// 打印
// 1
// 2
```

##### 递归函数

递归函数是指函数自己调用自己,函数的调用次数不受限制,直到满足特定条件退出递归,一定要写终止条件,不然就是一个死递归。

```javascript
// 阶乘递归函数
function factorial(n) {
  if (n === 1) {
    return 1
  } else {
    return n * factorial(n - 1)
  }
}
console.log(factorial(5)) // 120
```

##### this 关键字

this 关键字在函数内部指向调用它的对象,在函数调用前,this 关键字的值是 undefined,在函数调用后,this 关键字的值会根据调用方式的不同而不同。

###### this 指向

1. 全局调用

```javascript
// 全局调用就是前面没有任何对象调用,this 指向全局对象 window
// 如果没有开启严格模式,this指向全局对象window,如果开启了严格模式,this就是undefined
function test() {
  console.log(this)
}
test() // window
```

2. 方法调用

```javascript
// 作为对象的方法被调用,this 指向调用它的对象
let obj = {
  name: "张三",
  sayName: function () {
    console.log(this.name) //this指向obj对象
  },
}
obj.sayName() // 张三
```

3. 事件处理函数调用

```javascript
btn.onclick = function () {
  console.log(this) // this是前面的dom对象btn
}
btn.addEventListener("click", function () {
  console.log(this) // this是前面的dom对象btn
})
```

4. 构造函数调用

```javascript
function Person(name, age) {
  this.name = name
  this.age = age
}
let p1 = new Person("张三", 20)
console.log(p1.name) // 张三
console.log(p1.age) // 20
```

5. 箭头函数调用

```javascript
// 箭头函数没有自己的 this 绑定,它的 this 绑定是根据外层作用域来确定,如果外层作用域是普通函数,则 this 绑定的是 window,如果外层作用域是对象的方法,则 this 绑定的是该对象。
let obj = {
  name: "张三",
  // 因为箭头函数没有自己的 this 绑定,此时的外层作用域是window对象,所以 this 绑定的是 window 对象
  sayName: () => {
    console.log(this.name) // 输出 为空字符串,因为 this 指向 window 对象
    console.log(this) //this指向window对象
  },
}
obj.sayName()
let obj = {
  name: "张三",
  sayName: function () {
    let a = () => {
      console.log(this.name) // 输出 为张三
      console.log(this) //this指向obj对象
    }
    a()
  },
}
obj.sayName()
```

###### 改变 this 指向

**改变 this 指向后返回的是一个新的函数,可以将其存到一个新的函数名内,这样不仅能固定 this 指向,还能在事件监听中添加并*移除*该函数**

1. call() 方法

```javascript
// call() 方法可以改变函数的 this 指向,第一个参数是 this 要指向的对象,第二个以及后面的参数是可选参数,将后面的参数按顺序传给形参
let obj = {
  name: "张三",
}
let sayName = function (x, y, z) {
  console.log(this.name) // this指向window对象,this.name为空字符串
  console.log(this) // this指向window对象
  console.log(x, y, z) //输出x,y,z的值
}
sayName.call(obj, 1, 2, 3) //改变this指向,此时函数内部的this指向obj对象
// 输出结果:
// 张三
// {name: "张三"}
// 1 2 3
```

2. apply() 方法

```javascript
// apply() 方法可以改变函数的 this 指向,第一个参数是 this 要指向的对象,第二个参数是数组形式的参数,可以将数组形式的参数转为普通形式的参数传给函数
let obj = {
  name: "张三",
}
let sayName = function (x, y, z) {
  console.log(this.name) // this指向window对象,this.name为空字符串
  console.log(this) // this指向window对象
  console.log(x, y, z) //输出x,y,z的值
}
sayName.apply(obj, [1, 2, 3]) //改变this指向,此时函数内部的this指向obj对象
// 输出结果:
// 张三
// {name: "张三"}
// 1 2 3

// 高级用法,Math.min 和 Math.max 两个方法只能找出传入参数的最大值和最小值,但是利用 apply 方法我们可以传入一个数组来找,
// 如:Math.min.apply（false,arr）
// （不需要更改 this 指向时可以用一个 false 占位）
```

3. bind() 方法

```javascript
// bind() 方法可以创建一个新的函数,新函数的 this 指向 bind() 方法的第一个参数,其余参数将作为新函数的参数,返回值是一个函数
// 第一个参数是 this 要指向的对象,其余参数将作为新函数的固定参数,固定参数在调用时不可改变,如果固定参数比原函数的形参少,此时新函数在调用时传入其他参数,会排在固定参数后面
let obj = {
  name: "张三",
}
let sayName = function (x, y, z) {
  console.log(this.name) // this指向window对象,this.name为空字符串
  console.log(this) // this指向window对象
  console.log(x, y, z) //输出x,y,z的值
}
let newSayName = sayName.bind(obj, 1, 2) // 创建一个新的函数,this指向obj对象,固定参数为1,2,1,2分别为x,y的固定值,不可被修改
newSayName(3) // 调用新函数并传入新的参数3,因为x和y的值已经固定为1,2,所以此时调用时传入的参数3为z的值
// 输出结果:
// 张三
// {name: "张三"}
// 1 2 3
```

##### 箭头函数

箭头函数是一种新的函数定义方式,它使用 => 而不是 function 关键字,并且没有自己的 this、arguments、super、new.target 绑定。

1. 基本语法

```javascript
// 箭头函数的基本语法
let 函数名 = (参数1, 参数2, ……) => {
  // 函数体
  return 返回值;
};
// 如果函数体只有一个表达式,可以省略大括号和return关键字,函数返回值为箭头函数的结果
let 函数名 = (参数1, 参数2, ……) => 返回值;
// 如果只有一个参数可以省略小括号,但是如果没有参数或者有多个参数,小括号不能省略
let 函数名 = 参数1 => {
  // 函数体
  return 返回值;
};
// 如果返回的结果是一个对象,则需要将对象用小括号包起来
let 函数名 = () => ({ name: "张三", age: 20 });
```

2. 箭头函数的 this

   箭头函数没有自己的 this 绑定,它的 this 绑定是根据外层作用域来确定,如果外层作用域是普通函数,则 this 绑定的是 window,如果外层作用域是对象的方法,则 this 绑定的是该对象。

#### 正则表达式

正则表达式是一种用来匹配字符串的模式,它是由一系列字符组成的规则,用来描述字符串的结构、特征和行为。

##### 正则表达式的创建

```javascript
// 字面量创建
let reg = /hello world/
// 构造函数创建,第二个参数用来传入修饰符
// 可以拼接字符串
let reg = new RegExp("hello" + " " + "world", "i")
```

##### 修饰符

修饰符是用来修改正则表达式的行为的符号,包括 i、g、m 等。

- i:忽略大小写,匹配时不区分大小写
- g:全局匹配,匹配所有符合条件的字符串,而不是只匹配第一个
- m:多行匹配,^ 和 $ 匹配每一行的开始和结束
- s: 匹配所有字符,包括换行符

```javascript
// 忽略大小写
let reg = /hello world/i
let reg = new RegExp("hello world", "i")
// 全局匹配
let reg = /hello world/g
let reg = new RegExp("hello world", "g")
// 多行匹配
let reg = /hello\nworld/m
let reg = new RegExp("hello\\nworld", "m")
```

##### 元字符

元字符是用来匹配字符串的特殊字符

- .:匹配任意字符,除了换行符
- \:转义字符,用来匹配元字符本身
- ^: 放在[]里面开头,匹配[]中字符以外的字符
- \d:匹配数字,等价于 [0-9]
- \D:匹配非数字,等价于 [^0-9]
- \w:匹配单词字符,等价于 [a-zA-Z0-9_]
- \W:匹配非单词字符,等价于 [^a-zA-Z0-9_]
- \s:匹配空白字符,等价于 [\f\n\r\t\v]
- \S:匹配非空白字符,等价于[^\f\n\r\t\v]
- \b:匹配单词边界,等价于 \w\W
- \B:匹配非单词边界,等价于 \W\w
- \0:匹配 null 字符
- \n:匹配换行符
- \f:匹配换页符
- \r:匹配回车符
- \t:匹配制表符
- \v:匹配垂直制表符
- \uxxxx:匹配 16 进制 Unicode 字符
  - 中文汉字范围:\u4e00-\u9fa5
  - 日文假名范围:\u3040-\u309f
  - 韩文假名范围:\uac00-\ud7a3
  - 数字范围:\u0030-\u0039
  - 小写英文字母范围:\u0061-\u007a
  - 大写英文字母范围:\u0041-\u005a
- \Uxxxxxxxx:匹配 32 进制 Unicode 字符

  **注意:构造函数创建的正则表达式,要用两个\\来表示一个\\**

##### 限定符

限定符是用来限定正则表达式匹配的字符个数的符号,包括 +、\*、?、{n}、{n,m}、{n,} 等。

- +:匹配前面的字符至少出现一次,相当于{1,}
- \*:匹配前面的字符可以出现任意次,相当于{0,}
- ?:匹配前面的字符出现一次或不出现,相当于{0,1}

  ** \*和+会尽可能长的匹配符合条件的规则,\*?可以实现尽可能短的匹配符合条件的规则 **

##### |,(),[],{}

- |:或运算符,匹配多个表达式,匹配满足其一的表达式

```javascript
let reg = /hello|world/
let reg = new RegExp("hello|world")
let str1 = "hello"
console.log(reg.test(str1)) //true
let str2 = "world"
console.log(reg.test(str2)) //true
let str3 = "hi"
console.log(reg.test(str3)) //false
```

- ():限定一组元素,表示括号内的元素是一个整体

```javascript
let reg = /(abc)\d+/
let reg = new RegExp("(abc)\\d+")
let str1 = "abc123"
console.log(reg.test(str1)) //true
let str2 = "ab123"
console.log(reg.test(str2)) //false
```

- []:匹配字符集,表示括号内的字符可以匹配任意一个字符

```javascript
let reg = new RegExp("[abc]\\d+")
let str1 = "a1"
console.log(reg.test(str1)) //true
let str2 = "d1"
console.log(reg.test(str2)) //false
// []里面第一个字符加^表示不匹配括号内的字符
let reg = new RegExp("[^abc]\\d+")
let str1 = "a1"
console.log(reg.test(str1)) //false
let str2 = "d1"
console.log(reg.test(str2)) //true
// 使用-可以进行范围运算
// [0-9]表示匹配数字
// [a-z]表示匹配小写字母
// [A-Z]表示匹配大写字母
// [a-zA-Z]表示匹配字母
// [a-zA-Z0-9_]表示匹配字母、数字、下划线
```

- {}:匹配重复次数,表示括号内的字符可以重复出现多次

```javascript
// {n}:表示前面的字符出现 n 次
let reg = new RegExp("a{3}")
let str1 = "aaa"
console.log(reg.test(str1)) //true
let str2 = "aa"
console.log(reg.test(str2)) //false
// {n,m}:表示前面的字符出现 n-m 次
let reg = new RegExp("a{2,3}")
let str1 = "aaa"
console.log(reg.test(str1)) //true
let str3 = "aaaa"
console.log(reg.test(str3)) //false
// {n,}:表示前面的字符至少出现 n 次
let reg = new RegExp("a{2,}")
let str1 = "aaa"
console.log(reg.test(str1)) //true
let str2 = "a"
console.log(reg.test(str2)) //false
```

##### 边界符

边界符是用来匹配字符串的边界的符号,包括 ^、$、\b、\B 等。

- ^:匹配字符串的开始位置
- $:匹配字符串的结束位置

##### 预查规则

预查规则是用来匹配字符串的前面或后面部分的符号,包括 ?=、?<=、?<!、?! 等。

1. 正向预查

- 正向肯定预查:(?=),匹配后面符合条件的字符

```javascript
// 需要找到 windows, 并且是后面紧跟的是数字的 widnows
let reg = /windows(?=\d+)/g
```

- 正向否定预查:(?!),匹配后面不符合条件的字符

```javascript
// 需要找到 windows, 并且是后面紧跟的不是数字的 widnows
let reg = /windows(?!\d+)/g
```

2. 反向预查

- 反向肯定预查:(?<=),匹配前面符合条件的字符

```javascript
// 需要找到 windows, 并且是前面紧跟的是数字的 widnows
let reg = /(?<=\d+)windows/g
```

- 反向否定预查:(?<!),匹配前面不符合条件的字符

```javascript
// 需要找到 windows, 并且是前面紧跟的不是数字的 widnows
let reg = /(?<!\d+)windows/g
```

##### 方法

- test():用来测试字符串是否匹配正则表达式,返回布尔值

```javascript
// 有符合要求的片段返回 true
// 没有符合要求的片段返回 false
let reg = /hello world/
let str1 = "hello world"
console.log(reg.test(str1)) //true
let str2 = "hi world"
console.log(reg.test(str2)) //false
```

2. exec():用来在字符串中搜索符合正则表达式的模式,返回匹配结果数组,如果没有匹配到则返回 null

```javascript
// 没有匹配到返回 null
// 有符合要求的片段,没有（）没有修饰符g,返回数组只有一个元素是匹配到的字符串
// 有符合要求的片段,有（）没有修饰符g,返回数组第一个元素是匹配到的字符串,第二个元素是匹配到的第一个子组（括号内匹配的元素为子组）
// 有符合要求的片段,没有（）有修饰符g,第一次返回为第一个匹配到的字符串,第二次返回为第二个匹配到的字符串,以此类推,直到返回 null然后重新开始匹配
// 前两种情况的结合
let reg = /hello (world)/
let str1 = "hello world"
console.log(reg.exec(str1)) //["hello world", "world"]
let str2 = "hi world"
console.log(reg.exec(str2)) //null
```

3. match():用来在字符串中搜索符合正则表达式的模式,返回匹配结果数组,如果没有匹配到则返回 null

```javascript
// 该方法在正则没有加g修饰符时和exec方法一样,有g标识符时,返回数组,数组内装有所有匹配到的内容
let reg = /hello (world)/
let str1 = "hello world"
console.log(str1.match(reg)) //["hello world", "world"]
let str2 = "hi world"
console.log(str2.match(reg)) //null
```

4. replace():用来替换字符串中匹配正则表达式的模式,返回替换后的字符串

```javascript
// 第一个参数传的是字符串或者传的是正则但是没有全局修饰符g时只能替换一个,但是当正则加了全局修饰符g时可以替换全部匹配的片段
let reg = /hello world/
let str1 = "hello world"
console.log(str1.replace(reg, "hi")) //"hi"
let str2 = "hi world"
console.log(str2.replace(reg, "hi")) //"hi world"
```

5. search():用来在字符串中搜索符合正则表达式的模式,返回匹配到的第一个位置,如果没有匹配到则返回 -1

6. split():用来将字符串按照正则表达式匹配的结果进行分割,返回分割后的数组

### js 中检查数据类型的方式

1. typeof(变量名)或 typeof 变量名,能检查基本数据类型,结果必然是一个字符串类型

```javascript
// 检测数值会得到 number
console.log(typeof 123) // "number"
// 检测字符串会得到 string
console.log(typeof "hello world") // "string"
// 检测布尔值会得到 boolean
console.log(typeof true) // "boolean"
// 检测 undefined 会得到 undefined
console.log(typeof undefined) // "undefined"
// 检测 null 会得到 object
console.log(typeof null) // "object"
// 检测数组会得到 object
console.log(typeof [1, 2, 3]) // "object"
// 检测对象会得到 object
console.log(typeof { name: "张三", age: 20 }) // "object"
// 检测正则表达式会得到 object
console.log(typeof /hello/g) // "object"
// 检测函数会得到 function
console.log(typeof function () {}) // "function"
```

2. instanceof 运算符 ,检测对象是否是某个类的实例,结果是一个布尔值

```javascript
// 检测数组是否是 Array 类的实例
console.log([] instanceof Array) // true
// 检测对象是否是 Object 类的实例
console.log({} instanceof Object) // true
// 检测函数是否是 Function 类的实例
console.log(function () {} instanceof Function) // true
// 检测正则表达式是否是 RegExp 类的实例
console.log(/hello/g instanceof RegExp) // true
```

3. isNaN（值）,用来判断一个值在转换为数值时是否会转换为 NaN,如果会转换为 NaN 则返回布尔值 true,如果不会转换为 NaN 则返回布尔值 false

```javascript
console.log(isNaN(NaN)) // true
console.log(isNaN(123)) // false
console.log(isNaN("123")) // false
console.log(isNaN("hello")) // true
console.log(isNaN(undefined)) // true
console.log(isNaN(null)) // false
```

4. Number.isNaN(值),用来判断一个值是否是 NaN,如果是 NaN 则返回 true,否则返回 false

```javascript
console.log(Number.isNaN(NaN)) // true
console.log(Number.isNaN(123)) // false
console.log(Number.isNaN("123")) // false
console.log(Number.isNaN("hello")) // true
console.log(Number.isNaN(undefined)) // false
console.log(Number.isNaN(null)) // false
```

### 数据类型转换

#### 转数值

1. Number(值),可以被整体转为数值的字符串会被转为对应数值,否则转换为 NaN

```javascript
console.log(Number("123")) // 123
console.log(Number("hello")) // NaN
console.log(Number(true)) // 1
console.log(Number(false)) // 0
console.log(Number(undefined)) // NaN
console.log(Number(null)) // 0
```

2. parseInt(值,进制),可以将字符串转为整数,只会取字符串中数字开头的部分,字符串进制默认为 10,第二个参数可以指定进制（指定字符串的进制,把字符串当作几进制数转十进制）,如果不是以数字开头的字符串,则会返回一个 NaN

```javascript
console.log(parseInt("123")) // 123
console.log(parseInt("123.123")) // 123
console.log(parseInt("123", 10)) // 123
console.log(parseInt("10", 2)) // 2
console.log(parseInt("10", 8)) // 8
console.log(parseInt("10", 16)) // 16
console.log(parseInt("hello")) // NaN
console.log(parseInt(true)) // NaN
console.log(parseInt(false)) // NaN
console.log(parseInt(undefined)) // NaN
console.log(parseInt(null)) // NaN
```

3. parseFloat(值),可以将字符串转为浮点数,只会取字符串中数字开头的部分,字符串中包含小数点,则会返回一个浮点数,否则返回 NaN

```javascript
console.log(parseFloat("123")) // 123
console.log(parseFloat("123.456")) // 123.456
console.log(parseFloat("hello")) // NaN
console.log(parseFloat(true)) // NaN
console.log(parseFloat(false)) // NaN
console.log(parseFloat(undefined)) // NaN
console.log(parseFloat(null)) // NaN
```

4. 隐式转换

- 一元运算符+号和-号,可以将其他类型的值转换为数值类型,-号会将数值转为负值
- -,*,/,%,\*\*通过计算转数值,如值-0,值*1,值/1,值\*\*1
- 自增自减符号++,--,通过计算转数值,然后再进行自增自减

#### 转字符串

1. String(值),可以将任意类型的值转换为字符串类型
2. 值.toString()方法,可以将任意类型的值转换为字符串类型
3. 用加号连接一个空字符串

#### 转布尔值

1. Boolean(值),可以将任意类型的值转换为布尔类型,规则如下:

- 数值类型:非 0 就是 true,0 就是 false
- 字符串类型:非空字符串就是 true,空字符串就是 false
- 空数组、空对象、null、undefined、NaN 都是 false
- 其他类型都为 true

## 运算符

运算符是用来执行特定操作的符号,包括算术运算符、赋值运算符、比较运算符、逻辑运算符、位运算符、条件运算符、逗号运算符等。

### 算术运算符

- +:加法运算符,用来连接两个值,如果其中一个值是字符串,则会将另一个值转换为字符串连接
- -:减法运算符,用来计算两个数值的差值
- \*:乘法运算符,用来计算两个数值的乘积
- /:除法运算符,用来计算两个数值的商
- %:取模运算符,用来计算两个数值的余数
- \*\*:指数运算符,用来计算一个数值的幂

```javascript
// 两边都是数字
console.log(1 + 2) // 3
// 有字符串参与运算
console.log(1 + "hello" + " world") // "1hello world"
console.log(10 - 5) // 5
console.log(2 * 3) // 6
console.log(10 / 2) // 5
console.log(10 % 3) // 1
console.log(2 ** 3) // 8
```

### 赋值运算符

- =:赋值运算符,用来给变量赋值,可以用来给变量重新赋值,也可以用来给对象属性赋值
- +=:加等于运算符,`a+=b` 相当于 `a = a + b`
- -=:减等于运算符,`a-=b` 相当于 `a = a - b`
- \*=:乘等于运算符,`a*=b` 相当于 `a = a * b`
- /=:除等于运算符,`a/=b` 相当于 `a = a / b`
- %=:取余等于运算符,`a%=b` 相当于 `a = a % b`
- \*\*=:指数等于运算符,`a\*\*=b` 相当于 `a = a ** b`

```javascript
let a = 1
a = 2
console.log(a) // 2
a += 3
console.log(a) // 5
a -= 2
console.log(a) // 3
a *= 4
console.log(a) // 12
a /= 2
console.log(a) // 6
a %= 4
console.log(a) // 2
a **= 2
console.log(a) // 4
```

### 比较运算符

- ==:等于运算符,符号两边的值相等返回 true 否则返回 false,不判断数据类型
- ===:全等运算符,符号两边的值和数据类型都相等返回 true 否则返回 false
- !=:不等于运算符,符号两边的值不相等返回 true,否则返回 false,不判断数据类型
- !==:不全等运算符,符号两边的值和数据类型有一个不相等就返回 true 否则返回 false
- > :大于运算符,左边大于右边返回 true,否则返回 false
- <:小于运算符,左边小于右边返回 true,否则返回 false
- > =:大于等于运算符,左边大于或者等于右边返回 true,否则返回 false
- <=:小于等于运算符,左边小于或者等于右边返回 true,否则返回 false

  **两个字符串间比较,会按位比较,如果第一位相等就比较第二位,以此类推**

  **如果不都是字符串（一方为数值）会转数值进行比较**

```javascript
console.log(1 == 1) // true
console.log(1 == "1") // true
console.log(1 === 1) // true
console.log(1 === "1") // false
console.log(1 != 1) // false
console.log(1 != "1") // false
console.log(1 !== 1) // false
console.log(1 !== "1") // true
console.log(1 > 2) // false
console.log(1 < 2) // true
console.log(1 >= 1) // true
console.log(1 >= 2) // false
console.log(1 <= 1) // true
```

### 逻辑运算符

**当逻辑或与逻辑与在同时出现时,逻辑与的优先级要高于逻辑或**

#### 基本逻辑

- &&:逻辑与运算符,只有两个值都为 true 才返回 true,否则返回 false

  **如果进行运算的两个值不是布尔值,不会转布尔值再运算,而是第一个值为真返回第二个值,第一个值为假返回第一个值**

- ||:逻辑或运算符,只要其中一个值为 true 就返回 true,否则返回 false

  **如果进行运算的两个值不是布尔值,不会转布尔值再运算,而是第一个值为真返回第一个值（如果第二个值有数学运算,则不会再执行计算）,第一个值为假返回第二个值**

- !:逻辑非运算符,用来将 true 变为 false,false 变为 true

  **自动转布尔值然后进行取反,返回值返回 true 或 false**

```javascript
console.log(true && true) // true
console.log(true && false) // false
console, log(true || false) // true
console.log(false || false) // false
console.log(!true) // false
console.log(!false) // true
console.log(!1) // false
console.log(!0) // true
console.log(!"") // true
console.log(1 && 2) // 2
console.log(0 && 1) // 0
console.log(1 || 2) // 1
console.log(0 || 1) // 1
```

#### 短路逻辑

- 值 && 代码:当运算符左边的值为 true 时,右边代码执行,否则不执行

- 值 || 代码:当运算符左边的值为 false 时,右边代码执行,否则不执行

```javascript
false && console.log("hello") // 不执行
true && console.log("hello") // 执行
false || console.log("hello") // 执行
true || console.log("hello") // 不执行
```

### 一元运算符

- +,-,正负是一元运算符,加减是二元运算符
- 逻辑运算符！也是一元运算符
- 自增运算符（含隐式转换）
  1. ++x:先将 x 加 1,然后返回 x 的值
  2. x++:先返回 x 的值,然后将 x 加 1
- 自减运算符（含隐式转换）
  1. --x:先将 x 减 1,然后返回 x 的值
  2. x--:先返回 x 的值,然后将 x 减 1

```javascript
let a = 1
console.log(++a) // 2
console.log(a) // 2
console.log(a--) // 2
console.log(a) // 1
console.log(--a) // 0
console.log(a) // 0
```

## 内置对象

### Math 对象

#### 取整方法

- parseInt(string, radix):将字符串转换为整数,radix 为进制,默认为 10,如果不是以数字开头的字符串,则会返回一个 NaN
- parseFloat(string):将字符串转换为浮点数,如果字符串中包含小数点,则会返回一个浮点数,如果不是以数字开头的字符串,则会返回一个 NaN
- Math.floor(number):向下取整,返回一个小于或等于 number 的最大整数
- Math.ceil(number):向上取整,返回一个大于或等于 number 的最小整数
- Math.round(number):四舍五入,返回一个四舍五入后的整数

#### 随机数方法

Math.random():返回一个 0 到 1 之间的随机数（不包含 1）,
Math.floor(Math.random()\*(n+1)): 返回一个 0 到 n 的随机数

#### 取绝对值方法

Math.abs(number):返回一个数的绝对值

#### 最大最小值方法

Math.max(number1, number2,...):返回多个数中的最大值
Math.min(number1, number2,...):返回多个数中的最小值

```javascript
// 这两个方法不能直接从数组中找最大值和最小值
// 但是可以利用一个apply（改变this指向的一个工具,该工具可用传数组的方式传n个实参）工具可以实现
console.log(Math.max.apply(null, [1, 2, 3])) // 3
console.log(Math.min.apply(null, [1, 2, 3])) // 1
```

#### 保留小数

- Math.round(number \* 100) / 100:保留两位小数
- toFixed(number):保留指定位数的小数,将数字转换为指定小数位数的字符串（四舍五入）

#### 三角函数相关方法

##### 三角函数

**传参为弧度,返回值也是弧度**

- Math.sin(radians):返回一个角度的正弦值
- Math.cos(radians):返回一个角度的余弦值
- Math.tan(radians):返回一个角度的正切值

##### 反三角函数

**传参为弧度,返回值也是弧度**

- Math.asin(radians):返回一个正弦值的反正弦值
- Math.acos(radians):返回一个余弦值的反余弦值
- Math.atan(radians):返回一个正切值的反正切值
- Math.atan2(y, x):回的是点 (x, y) 与 x 轴之间的角度（弧度）,这个函数在计算向量方向时非常有用。

##### 双曲函数

- Math.sinh(radians):返回一个双曲正弦值
- Math.cosh(radians):返回一个双曲余弦值
- Math.tanh(radians):返回一个双曲正切值

##### 反双曲函数

- Math.asinh(radians):返回一个双曲正弦值的反双曲正弦值
- Math.acosh(radians):返回一个双曲余弦值的反双曲余弦值
- Math.atanh(radians):返回一个双曲正切值的反双曲正切值

##### 弧度与角度转换

**可以将计算封装成函数,方便使用**

- (degrees)度转弧度(radians):Math.PI / 180 \* degrees
- (radians)弧度转度(degrees):180 / Math.PI \* radians

#### 对数相关方法

- Math.log(number):返回一个数的自然对数
- Math.log2(number):返回一个数以 2 为底的对数
- Math.log10(number):返回一个数以 10 为底的对数

#### 计算相关

- Math.pow(x, y):返回 x 的 y 次方
- Math.sqrt(number):返回一个数的平方根
- Math.cbrt(number):返回一个数的立方根
- Math.exp(number):返回 e 的 number 次方
- Math.hypot(x, y, z...):返回传入参数的平方和的平方根

#### 数学常量

- Math.E:自然常数 e
- Math.PI:圆周率
- Math.SQRT2:平方根 2

#### 其他方法

- Math.sign(number):返回一个数的符号,1 表示正数,-1 表示负数,0 表示 0

#### 进制转换

- number.toString(radix):将数字转换为字符串,radix 为进制,默认为 10
- parseInt(string, radix):将字符串(可以放其他进制数,将其转为十进制)转换为整数,radix 为进制,默认为 10

### Date 对象

#### 创建日期对象

- new Date():创建当前日期和时间的日期对象
- new Date(year, month, day, hours, minutes, seconds, milliseconds):创建指定日期和时间的日期对象
- new Date(milliseconds):创建指定时间戳的日期对象,单位为毫秒,时间戳:从 1970 年 1 月 1 日 00:00:00 UTC 时间（世界标准时间）开始所经过的毫秒数

```javascript
let date = new Date(); // 创建当前日期和时间的日期对象
let date2 = new Date(2021, 1, 1); // 创建 2021 年 2 月 1 日 00:00:00 的日期对象
let date3 = new Date(2021-1-1 00:00:00); // 创建 2021 年 2 月 1 日 00:00:00 的日期对象
let date4 = new Date(1612137600000); // 创建 2021 年 2 月 1 日 00:00:00 的日期对象
```

#### 静态方法

Date.now()返回当前时间的时间戳(number)

#### 相关方法

##### 获取日期信息

- getFullYear():返回时间对象内的年份的信息
- getMonth():返回时间对象内的月份（0-11）
- getDate():返回时间对象内的日期（1-31）
- getDay():返回时间对象内的星期（0-6）
- getHours():返回时间对象内的小时（0-23）
- getMinutes():返回时间对象内的分钟（0-59）
- getSeconds():返回时间对象内的秒（0-59）
- getMilliseconds():返回时间对象内的毫秒（0-999）
- getTime()返回当前时间的时间戳

```javascript
let date = new Date()
console.log(date.getFullYear()) // 年份
console.log(date.getMonth()) // 月份（0-11）
console.log(date.getDate()) // 日期（1-31）
console.log(date.getDay()) // 星期（0-6）
console.log(date.getHours()) // 小时（0-23）
console.log(date.getMinutes()) // 分钟（0-59）
console.log(date.getSeconds()) // 秒（0-59）
console.log(date.getMilliseconds()) // 毫秒（0-999）
```

##### 设置日期信息

- setFullYear(year):设置时间对象内的年份
- setMonth(month):设置时间对象内的月份（0-11）
- setDate(date):设置时间对象内的日期（1-31）
- setHours(hours):设置时间对象内的小时（0-23）
- setMinutes(minutes):设置时间对象内的分钟（0-59）
- setSeconds(seconds):设置时间对象内的秒（0-59）
- setMilliseconds(milliseconds):设置时间对象内的毫秒（0-999）

```javascript
let date = new Date()
date.setFullYear(2021)
date.setMonth(1)
date.setDate(1)
date.setHours(0)
date.setMinutes(0)
date.setSeconds(0)
date.setMilliseconds(0)
console.log(date) // 2021-02-01T00:00:00.000Z
```

##### 日期格式化

- toString():返回日期对象对应的字符串形式
- toLocaleString():返回日期对象对应的本地化字符串形式
- toDateString():返回日期对象内的日期部分的字符串形式
- toLocaleDateString():返回日期对象内的日期部分的本地化字符串形式
- toTimeString():返回日期对象内的时间部分的字符串形式
- toLocaleTimeString():返回日期对象内的时间部分的本地化字符串形式

```javascript
let date = new Date()
console.log(date.toDateString()) // Fri Feb 01 2021
console.log(date.toLocaleDateString()) // 2021/02/01
console.log(date.toLocaleTimeString()) // 00:00:00
```

## 三大基本结构

### 顺序结构

顺序结构是指按照顺序执行代码,从上到下,从左到右。

### 选择（分支）结构

#### if 语句

if 语句是条件语句,它根据条件来执行代码,如果条件为 true,则执行 if 语句后面的代码,否则不执行。

```javascript
// 单分支语句
if (true) {
  console.log("hello")
}

// 双分支语句
if (true) {
  console.log("hello")
} else {
  console.log("world")
}

// 多分支语句
if (true) {
  console.log("hello")
} else if (false) {
  console.log("world")
} else {
  console.log("!")
}
```

#### switch 语句

switch 语句是多分支语句,它根据表达式的值来执行代码,如果表达式的值与 case 语句的值相等,则执行该 case 语句后面的代码,否则执行下一个 case 语句。

- 查找符合的条件时是从上往下逐个查找的,每个情况的顺序是由自己决定的
- 需要进行判断的值必须要和 case 后面的值全等才算满足条件,即值和数据类型都要相等
- break 通常情况下都要书写,如果不书写 break,会发生穿透现象,不管下一个 case 是否满足都会执行代码,直到遇见 break 为止,合理利用穿透可以实现神奇的效果

```javascript
let num = 1
switch (num) {
  case 1:
    console.log("hello")
    break
  case 2:
    console.log("world")
    break
  default:
    console.log("!")
}
```

#### 三元运算符

三元运算符是条件运算符,它根据条件来决定表达式的值,如果条件为 true,则返回第一个表达式的值,否则返回第二个表达式的值。

```javascript
let a = 1
let b = a === 1 ? "hello" : "world"
console.log(b) // hello
```

### 循环结构

#### for 循环

for 循环是一种最基本的循环结构,它可以指定一个初始值,一个结束值,一个递增值,然后在每次循环中执行代码。

```javascript
for (let i = 0; i < 5; i++) {
  console.log(i)
}
```

#### while 循环

while 循环是一种条件循环,它会在条件为 true 时执行代码,否则不执行。

```javascript
let i = 0
while (i < 5) {
  console.log(i)
  i++
}
```

#### do-while 循环

do-while 循环是一种先执行代码后判断条件的循环,它会先执行代码,然后判断条件,如果条件为 true,则继续执行代码,否则不执行。

```javascript
let i = 0
do {
  console.log(i)
  i++
} while (i < 5)
```

### 循环终止与跳过

#### break 语句

break 语句是跳出循环的语句,它会立即结束当前循环,并开始执行循环后的语句。

```javascript
for (let i = 0; i < 5; i++) {
  if (i === 3) {
    break
  }
  console.log(i)
}
```

#### continue 语句

continue 语句是跳过当前循环的剩余语句,它会立即结束当前循环,并开始下一次循环。

```javascript
for (let i = 0; i < 5; i++) {
  if (i === 3) {
    continue
  }
  console.log(i)
}
```

#### 标签

标签（label）是一个标识符,可以与 break 或 continue 语句一起使用,以便在嵌套循环或代码块中进行控制转移。label 通常用于跳出多层循环或跳过特定的循环迭代。

```javascript
// 配合 break 使用
label1: for (let i = 0; i < 5; i++) {
  for (let j = 0; j < 5; j++) {
    if (j === 3) {
      break label1 // 跳出 label1 标识的循环,即结束最外层循环。
    }
    console.log(i + " " + j)
  }
}
// 配合 continue 使用
label2: for (let i = 0; i < 5; i++) {
  for (let j = 0; j < 5; j++) {
    if (j === 3) {
      continue label2 // 跳过 label2 标识的循环,即结束当前循环,开始下一次循环。
    }
    console.log(i + " " + j)
  }
}
```

## BOM 对象

BOM（Browser Object Model）对象模型,它提供了访问浏览器窗口的功能,包括窗口大小、位置、对话框、cookie、历史记录、窗口间的通信等。

### navigator 对象

navigator 对象提供了一些关于浏览器的信息,包括浏览器的名称、版本、操作系统、CPU 信息等。

```javascript
// 1. appName:浏览器名称
console.log(navigator.appName) // 输出: Netscape
// 2. appVersion:浏览器版本
console.log(navigator.appVersion) // 5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
// 3. platform:操作系统平台
console.log(navigator.platform) // 输出: Win32
// 4. userAgent:浏览器的 user agent 字符串,包含了浏览器的名称、版本、操作系统平台、CPU 信息等。
console.log(navigator.userAgent) // 输出: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36
```

### location 对象

location 对象提供了当前页面的 URL、协议、主机名、端口号、路径名、参数、锚点等信息。

```javascript
// 1. href:返回当前页面的完整 URL。
console.log(location.href) // 输出: http://www.example.com/path/page.html?query=string#hash
// 可以赋值,会跳转页面到后面你给的那个地址
window.location.href = "http://yzx.qfedu.com/courseDetails?id=185&title=%E9%B8%BF%E8%92%99%E7%94%9F%E6%80%81%E5%BC%80%E5%8F%91%E8%AF%BE%E7%A8%8B%E4%BB%8B%E7%BB%8D&courseType=0"
// 2. protocol:返回当前页面的协议。
console.log(location.protocol) // 输出: http:
// 3. host:返回当前页面的主机名。
console.log(location.host) // 输出: www.example.com
// 4. hostname:返回当前页面的域名。
console.log(location.hostname) // 输出: www.example.com
// 5. port:返回当前页面的端口号。
console.log(location.port) // 输出: 80
// 6. pathname:返回当前页面的路径。
console.log(location.pathname) // 输出: /path/page.html
// 7. search:返回当前页面的查询字符串,获取网址中所携带的参数.
console.log(location.search) // 输出: ?query=string
// 8. hash:返回当前页面的锚点。
console.log(location.hash) // 输出: #hash
// 9.reload():重新加载当前页面,注意不要写在全局,不然页面一直在刷新
location.reload()
// 10.assign():加载新的 URL 并跳转到该页面,与 location.href 不同的是,不会重新加载页面,而是直接跳转到新的页面（可以后退）
location.assign("http://www.example.com")
// 11.replace():加载新的 URL 并替换当前页面,与 location.assign 不同的是,会重新加载页面（无法后退）
```

### history 对象

history 对象提供了浏览器的历史记录,可以用来操作浏览器的前进、后退、刷新等操作。

```javascript
// 1. length:返回浏览器的历史记录的数量。
console.log(history.length) // 输出: 2
// 2. back():浏览器后退。
history.back()
// 3. forward():浏览器前进。
history.forward()
// 4. go(n):前进或后退n页。
history.go(-1) // 后退
history.go(1) // 前进
```

### screen 对象

screen 对象提供了屏幕相关的信息,包括屏幕的宽度、高度、像素密度、颜色深度等。

```javascript
// 1. width:屏幕的宽度。
console.log(screen.width) // 输出: 1920
// 2. height:屏幕的高度。
console.log(screen.height) // 输出: 1080
// 3. colorDepth:颜色深度。
console.log(screen.colorDepth) // 输出: 24
```

### window 对象

window 对象提供了一些与窗口及文档对象相关的功能,包括窗口的大小、位置、框架、窗口间的通信等。

**前面的 window.可以省略**

#### 弹窗

```javascript
// alert():弹出警告框。
alert("Hello, world!")

// confirm():弹出确认框,并返回用户选择的结果。
let result = confirm("Are you sure?")
// 当你点击确定的时候,就会得到 true
// 当你点击取消的时候,就会得到 false
if (result) {
  console.log("OK")
} else {
  console.log("Cancel")
}

// prompt():弹出输入框,并返回用户输入的结果。
let name = prompt("What is your name?")
console.log(name)
// 当你点击取消的时候,得到的是 null
// 当你点击确定的时候得到的就是你输入的内容
```

#### 窗口操作

```javascript
// open():打开一个新的窗口或tab页。
let newWindow = window.open("http://www.example.com")

// close():关闭当前窗口或tab页。
window.close()

// resizeTo():调整当前窗口的大小。
window.resizeTo(500, 500)

// resizeBy():调整当前窗口的大小。
window.resizeBy(200, 200)

// moveTo():移动当前窗口的位置。
window.moveTo(100, 100)

// moveBy():移动当前窗口的位置。
window.moveBy(200, 200)
```

#### 窗口尺寸获取

```javascript
// 窗口尺寸改变事件
window.onresize = function () {
  console.log("window resized")
}
// 获取当前窗口的宽度和高度
console.log(window.innerWidth) // 输出: 1920
console.log(window.innerHeight) // 输出: 1080
```

#### 滚动条事件

```javascript
// 滚动条事件
window.onscroll = function () {
  console.log("scrolled")
  // 获取当前页面的滚动条位置
  console.log(window.scrollX) // 输出: 0
  console.log(window.scrollY) // 输出: 0
  // 为滚动条位置赋值
  window.scrollTo(0, 0) // 滚动到顶部,左部
}
// 设置页面滚动到顶部可以添加动画效果
window.scrollTo({
  top: 0,
  left: 0,
  behavior: "smooth", // 平滑滚动
})
```

#### 定时器

**设置定时器和延时器是有返回值的,这个定时器是页面中的第几个定时器,返回值就是几,定时器和延时器共用一套编号,通常将编号放入一个变量内用于清除定时器**

```javascript
// setTimeout():设置一个延时器,在指定的时间后执行函数。
setTimeout(function () {
  console.log("Hello, world!")
}, 1000)

// clearTimeout():取消一个延时器。
let timerId = setTimeout(function () {
  console.log("Hello, world!")
}, 1000)
clearTimeout(timerId)

// setInterval():设置一个定时器,每隔指定的时间就执行函数。
let timerId = setInterval(function () {
  console.log("Hello, world!")
}, 1000)

// clearInterval():取消一个定时器。
let timerId = setInterval(function () {
  console.log("Hello, world!")
}, 1000)
clearInterval(timerId)
```

- 原则上是
  - `clearTimeout` 关闭 `setTimeout`
  - `clearInterval` 关闭 `setInterval`
- 但是其实是可以通用的,他们可以混着使用

#### 本地存储

在一个页面中用本地存储的数据,可以被同级文件夹下的其他页面访问

##### localStorage

localStorage 是一个本地存储对象,可以用来存储少量的数据,数据会永久保存,除非手动删除。

- 存储数据:localStorage.setItem(key, value)
- 获取数据:localStorage.getItem(key)
- 删除数据:localStorage.removeItem(key)
- 清空数据:localStorage.clear()
- 获取数据长度:localStorage.length

##### sessionStorage

sessionStorage 与 localStorage 类似,也是用来存储少量的数据,不同的是,数据在浏览器会话结束后就会清除。（关闭页面数据就会消失）

- 存储数据:sessionStorage.setItem(key, value)
- 获取数据:sessionStorage.getItem(key)
- 删除数据:sessionStorage.removeItem(key)
- 清空数据:sessionStorage.clear()
- 获取数据长度:sessionStorage.length

##### 注意事项

- localStorage 和 sessionStorage 的区别主要在于生命周期不同,localStorage 除非手动删除,否则数据会永久保存,sessionStorage 数据在浏览器会话结束后就会清除。
- localStorage 和 sessionStorage 适用于存储少量数据,比如用户的偏好设置、浏览记录、游戏分数等。
- localStorage 和 sessionStorage 都只能存储字符串,如果存储其他类型的数据,需要先进行转换。

## DOM 对象

DOM（Document Object Model）对象模型,它提供了操作 HTML 文档的功能,包括创建、修改、删除元素、事件处理、样式、动画、遍历等。

### 节点类型

- 元素节点（Element Node）:标签元素,如 `<div>`、`<p>`、`<a>` 等。
- 文本节点（Text Node）:标签元素中的文本内容,如 "Hello, world!"。
- 属性节点（Attribute Node）:标签元素的属性,如 `id`、`class`、`href` 等。
- 注释节点（Comment Node）:注释内容,如 `<!-- This is a comment -->`。

### 节点操作

#### 获取元素节点

##### 方式一:通过标签名、属性或选择器获取元素节点

```javascript
// 通过元素的 id 属性获取元素节点。
let element = document.getElementById("myElement")
// 通过元素的 class 属性获取元素节点的集合(伪数组,不能用forEach)。
let elements = document.getElementsByClassName("myClass")
// 通过标签名获取元素节点的集合(伪数组,不能用forEach)。
let elements = document.getElementsByTagName("div")
// 通过元素的 name 属性获取元素节点的集合(伪数组,不能用forEach)。
let elements = document.getElementsByName("myName")
// 通过 CSS 选择器获取元素节点。
let elements = document.querySelector(".myClass")
// 通过 CSS 选择器获取元素节点的集合(伪数组,能用forEach)。
let elements = document.querySelectorAll(".myClass")
// 前三种方式,get获取到数据的可以动态更新,不需要重新获取
// 而后两种获取到的值是固定的,如果DOM对象有更新,则需要重新获取
```

##### 方式二:通过父节点和子节点获取元素节点

- dom.childNodes:获取某一个节点下的所有的子一级节点(包含文本节点)

  ```html
  <div>
    <p>hello</p>
  </div>
  <script>
    // 获取某一个节点下的所有的子一级节点(包含文本节点)
    // let childNodes = 父节点.childNodes;
    let oDiv = document.querySelector("div")
    console.log(oDiv.childNodes)
    /*
      	NodeList(3) [text, p, text]
        0: text
        1: p
        2: text
        length: 3
        __proto__: NodeList
      */
    //  一个 `text`:从 `<div> 一直到 <p>` 中间有一个换行和一堆空格,这个是第一个节点,是一个文本节点
    // 一个 `p`:这个 `p` 标签就是第二个节点,这个是一个元素节点
    // 一个 `text`:从 `</p> 一直到 </div>` 中间有一个换行和一堆空格,这个是第三个节点,是一个文本节点
  </script>
  ```

- dom.children:获取某个节点下的所有子一级元素节点(不包含文本节点)

  ```html
  <div>
    <p>hello</p>
  </div>
  <script>
    // 获取某个节点下的所有子一级元素节点
    // let childNodes = 父节点.children;
    let oDiv = document.querySelector("div")
    console.log(oDiv.children)
    /*
    	HTMLCollection [p]
      0: p
      length: 1
      __proto__: HTMLCollection
    */
    //  div 下面又只有一个元素节点,就是 `p `标签
  </script>
  ```

- dom.firstChild:获取某个节点下的第一个子节点(包含文本节点)

  ```html
  <div>
    <p>hello</p>
  </div>
  <script>
    // 获取某个节点下的第一个子节点
    // let firstChild = 父节点.firstChild;
    let oDiv = document.querySelector("div")
    console.log(oDiv.firstChild) // text
    // 这个是只获取一个节点,不再是伪数组了
    // 第一个就是 `<div> 一直到 <p>` 的那个换行和空格,是个文本节点
  </script>
  ```

- dom.lastChild:获取某个节点下的最后一个子节点(包含文本节点)

  ```html
  <div>
    <p>hello</p>
  </div>
  <script>
    // 获取某个节点下的最后一个子节点
    // let lastChild = 父节点.lastChild;
    let oDiv = document.querySelector("div")
    console.log(oDiv.lastChild) // text
    // 这个是只获取一个节点,不再是伪数组了
    // 最后一个就是 `</p> 一直到 </div>` 的那个换行和空格,是个文本节点
  </script>
  ```

- dom.firstElementChild:获取某一节点下子一级的第一个元素节点(不包含文本节点)

  ```html
  <div>
    <p>hello</p>
  </div>
  <script>
    // 获取某一节点下子一级的第一个元素节点
    // let firstElementChild = 父节点.firstElementChild;
    let oDiv = document.querySelector("div")
    console.log(oDiv.firstElementChild) // p
    // 这个是只获取一个节点,不再是伪数组了
    // 第一个就是 `<p>` 标签
  </script>
  ```

- dom.lastElementChild:获取某一节点下子一级的最后一个元素节点(不包含文本节点)

  ```html
  <div>
    <p>hello</p>
  </div>
  <script>
    // 获取某一节点下子一级的最后一个元素节点
    // let lastElementChild = 父节点.lastElementChild;
    let oDiv = document.querySelector("div")
    console.log(oDiv.lastElementChild) // p
    // 这个是只获取一个节点,不再是伪数组了
    // 最后一个就是 `</p>` 标签
  </script>
  ```

- dom.nextSibling:获取某个节点的下一个兄弟节点(包含文本节点)

```html
<ul>
  <li id="a">hello</li>
  <li id="b">world</li>
  <li id="c">!!!</li>
</ul>
<script>
  // 获取某一个节点的下一个兄弟节点
  // let nextSibling = 节点.nextSibling;
  let oLi = document.querySelector("#b")
  console.log(oLi.nextSibling) // text
  // 这个是只获取一个节点,不再是伪数组了
  // 因为 `id="b"` 的下一个节点,是两个 `li` 标签之间的换行和空格,所以是一个文本节点
</script>
```

- dom.previousSibling:获取某一个节点的上一个兄弟节点(包含文本节点)

```html
<ul>
  <li id="a">hello</li>
  <li id="b">world</li>
  <li id="c">!!!</li>
</ul>
<script>
  // 获取某一个节点的上一个兄弟节点
  // let previousSibling = 节点.previousSibling;
  let oLi = document.querySelector("#b")
  console.log(oLi.previousSibling) // text
  // 这个是只获取一个节点,不再是伪数组了
  // 因为 `id="b"` 的上一个节点,是 `id="a"` 标签之间的换行和空格,所以是一个文本节点
</script>
```

- dom.nextElementSibling:获取某一个节点的下一个元素节点(不包含文本节点)

```html
<ul>
  <li id="a">hello</li>
  <li id="b">world</li>
  <li id="c">!!!</li>
</ul>
<script>
  // 获取某一个节点的下一个元素节点
  // let nextElementSibling = 节点.nextElementSibling;
  let oLi = document.querySelector("#b")
  console.log(oLi.nextElementSibling) // li#c
  // 这个是只获取一个节点,不再是伪数组了
  // 因为 `id="b"` 的下一个节点,是 `id="c"` 标签,所以是一个元素节点
</script>
```

- dom.previousElementSibling:获取某一个节点的上一个元素节点(不包含文本节点)

```html
<ul>
  <li id="a">hello</li>
  <li id="b">world</li>
  <li id="c">!!!</li>
</ul>
<script>
  // 获取某一个节点的上一个元素节点
  // let previousElementSibling = 节点.previousElementSibling;
  let oLi = document.querySelector("#c")
  console.log(oLi.previousElementSibling) // li#b
  // 这个是只获取一个节点,不再是伪数组了
  // 因为 `id="c"` 的上一个节点,是 `id="b"` 标签,所以是一个元素节点
</script>
```

- dom.parentNode:获取某个节点的父节点

```html
<ul>
  <li id="a">hello</li>
  <li id="b">world</li>
  <li id="c">!!!</li>
</ul>
<script>
  // 获取某一个节点的父节点
  // let parentNode = 节点.parentNode;
  let oLi = document.querySelector("#b")
  console.log(oLi.parentNode) // <ul>...</ul>
  // 这个是只获取一个节点,不再是伪数组了
  // 因为 `id="b"` 标签的父节点是 `ul` 标签,所以是一个元素节点
</script>
```

#### 元素节点操作

```javascript
// 创建一个元素节点。
let element = document.createElement("div");
// 创建一个文本节点,（存到一个变量内）,不会解析HTML标签(没人用的玩意)
let text = document.createTextNode("Hello, world!");
// 插入节点,将子节点插入到父节点的尾部
父节点.appendChild(要插入的节点);
// 插入节点,将子节点插入到父节点内某个节点的前面
父节点.insertBefore(要插入的节点, 哪个节点前面);
// 删除节点,删除自己
节点.remove();
// 删除节点,删除指定父节点的子节点
父节点.removeChild(要删除的节点);
// 将一个父节点下的一个旧节点替换为新节点
父节点.replaceChild(新节点,旧节点)
// 替换节点,将旧节点替换为一个或多个新节点
旧节点.replaceWith(新节点1, 新节点2,....);
// 克隆节点,复制一个节点
// true表示深度克隆,false表示浅度克隆
// 深度克隆会复制所有子节点,浅度克隆只复制当前节点
let cloneNode = 节点.cloneNode(true/false);
```

#### 获取与设置节点的文本

```javascript
// 主要针对的是表单元素（input的文本类型）
元素节点.value = "Hello, world!"
let value = 元素节点.value
// 获取元素节点的文本内容(不会获取HTML结构)。
let text = 元素节点.innerText
// 设置元素节点的文本内容(不会解析HTML标签)。
元素节点.innerText = "Hello, world!"
// 获取元素节点的 HTML 内容(会获取HTML结构)。
let html = 元素节点.innerHTML
// 设置元素节点的 HTML 内容(会解析HTML标签)。
元素节点.innerHTML = "<b>Hello, world!</b>"
```

#### 属性节点操作

1. 基础操作

```javascript
// 原生属性操作,可获取和设置元素的属性值
元素.属性名 = 值
元素.src = "图片路径"
元素.href = "链接路径"
元素.style.backgroundColor = "red"
// 获取元素的样式通过这种方式只能获得行内样式
略
// 自定义属性操作,可获取和设置元素的自定义属性值
// 获取一个属性值
let value = 元素.getAttribute("属性名")
// 获取全部属性值,获取到的是一个伪数组
let attributes = 元素.attributes
// 设置属性值
元素.setAttribute("属性名", 值)
// 删除属性值
元素.removeAttribute("属性名")
// 自定义属性操作不仅能获取和设置自定义属性,还可以对原生属性进行操作
```

```html
<!-- html5中我们可以使用data-前缀设置我们需要的自定义属性,来进行一些数据的存放,例如我们要在一个文字按钮上存放相应的id: -->
<!-- 这里的data-前缀就被称为data属性,其可以通过脚本进行定义,也可以应用css属性选择器进行样式设置.数量不受限制,在控制和渲染数据的时候提供了非常强大的控制. -->
<!-- 下面是元素应用data属性的一个例子: -->
<div
  id="day-meal-expense"
  data-drink="tea"
  data-food="noodle"
  data-meal="lunch">
  $18.3
</div>
<!-- 要想获取某个属性的值,可以像下面这样使用dataset对象: -->
<script>
  var expenseday = document.getElementById("day-meal-expense")
  var typeOfDrink = expenseday.dataset.drink
  console.log(typeOfDrink) //tea
  console.log(expenseday.dataset.food) //noodle
  console.log(expenseday.dataset.meal) //lunch
</script>
```

2. 元素样式操作

```javascript
// 注意下面方法获取样式是带单位的,如:10px,10em等
// 获取元素的行内样式
let color = 元素.style.color
// 设置元素的行内样式
元素.style.color = "red"
// 获取一个元素
let element = document.getElementById("myElement")

// 获取该元素的计算样式(window可省略)
let computedStyle = window.getComputedStyle(element, null)
// 或者获取某个样式
let width = window.getComputedStyle(element).width

// 访问某个具体的样式属性
let width = computedStyle.width
let color = computedStyle.color

console.log("Width:", width)
console.log("Color:", color)
```

```javascript
// 获取元素的尺寸,获取元素尺寸（只能读取不能赋值）,此方法获取的不带单位是纯数值
// 获取元素内容+padding+border区域的尺寸
let width = 元素.offsetWidth
let height = 元素.offsetHeight
// 获取元素内容+padding区域的尺寸
let width = 元素.clientWidth
let height = 元素.clientHeight
```

```javascript
// 获取位置
// 获取元素相对于文档的位置
元素.getBoundingClientRect()
// 获取元素相对于视口的位置
元素.getClientRects()

// 获取相对于祖先级元素中已定位元素或者body的偏移量
// 获取元素相对于其定位父级元素的垂直偏移量
元素.offsetTop
// 获取元素相对于其定位父级元素的水平偏移量
元素.offsetLeft
// 获取元素的定位父级元素
元素.offsetParent
```

3. 元素类名操作

```javascript
// 获取元素的类名
let className = 元素.className
// 设置元素的类名
元素.className = "myClass"
// 添加类名
元素.classList.add("myClass")
// 移除类名
元素.classList.remove("myClass")
// 判断是否有类名
元素.classList.contains("myClass")
// 切换类名,方法可以切换元素的类名。如果类名存在,则移除它;如果类名不存在,则添加它
元素.classList.toggle("myClass")
// 清除所有类名
元素.className = ""
// 获取全部类名
let classList = 元素.className
```

## 事件

### 事件类型

#### 鼠标事件

- click:鼠标点击事件
- dblclick:鼠标双击事件
- mousedown:鼠标按下事件
- mouseup:鼠标松开事件
- mouseover:鼠标移入事件
- mouseout:鼠标移出事件
- mousemove:鼠标移动事件
- mouseenter:鼠标进入事件
- mouseleave:鼠标离开事件

#### 键盘事件

- keydown:键盘按下事件,如果按住不放,会重复触发
- keyup:键盘松开事件
- keypress:键盘按下事件,如果按住不放,会重复触发

#### 表单事件

- submit:当用户点击提交按钮在<form>元素上触发
- focus:页面或元素获得焦点事件
- blur:页面或元素失去焦点事件
- select:下拉框选择事件
- reset:当用户点击重置按钮在<form>元素上触发
- focus:元素获得焦点事件
- blur:元素失去焦点事件
- change:表单元素内容改变时触发（只有在元素失去焦点后,内容发生更改才触发）

  适用标签:input 标签,select 下拉框标签,textarea 多行文本输入框标签

- input:用户输入时触发（每次输入都触发,不需要等焦点消失）

  适用标签:input 标签,textarea 多行文本输入框或者带有 contenteditable 属性的元素

#### 页面事件

- load:一张页面或一幅图像完成加载后触发
- unload:页面卸载事件,在用户关闭窗口、导航到另一个页面或刷新页面时触发。
- error:加载页面或者图片失败触发
- resize:窗口大小改变事件时触发
- scroll:页面滚动事件,页面滚动时触发
- contextmenu:右键菜单事件

### 事件处理

#### 事件流机制

- 事件流是指从页面中接收事件的顺序。
- 事件流包括三个阶段:事件捕获阶段、事件目标阶段、事件冒泡阶段。
- 事件捕获阶段:从 window 节点开始,逐级向下传播到目标节点,直到目标节点。
- 事件目标阶段:在目标节点上触发事件。
- 事件冒泡阶段:从目标节点开始,逐级向上传播到 window 节点。

  事件流的顺序是先捕获,再目标,最后冒泡。

#### 事件处理函数

- 事件处理函数是指在事件发生时,执行的函数。
- 事件处理函数可以是 JavaScript 函数,也可以是 HTML 标签的属性值。

```javascript
on事件名 = function () {
  // 事件处理代码
}
```

#### 事件监听器

- dom.addEventListener(事件名,回调函数):为元素添加事件监听器
- dom.removeEventListener(事件名,回调函数):移除元素的事件监听器
- dom.dispatchEvent(new Event(事件名)):执行该 dom 对象,对应事件绑定的函数(相当于模拟触发了该事件)

```javascript
// addEventListener(事件名, 事件处理函数, 是否捕获阶段);
// 第一个参数是事件名,第二个参数是事件处理函数,第三个参数是布尔值,表示是否捕获阶段,默认是 false,不是捕获阶段就是冒泡阶段。
// 单个元素添加事件监听器
let btn = document.getElementById("myBtn")
btn.addEventListener("click", function () {
  console.log("clicked")
})

// 移除事件监听器
btn.removeEventListener("click", function () {
  console.log("clicked")
})
```

#### 事件对象

##### 事件对象的获取

1. 事件触发时,在事件处理函数内部,可以用 event 关键词来获取事件触发获取到的信息（以对象的形式获取）
2. 以传参的形式来获取时间对象,执行事件处理函数时,浏览器调用该函数会传入一个事件对象,只需要用一个变量就可以获取到这个事件对象了,function nam(参数){console.log(参数);}
3. 兼容语法,兼容语法即上面两种语法都用,因为个别浏览器会只支持一种语法,所以我们可以用||短路逻辑来获取事件对象

```javascript
// 事件对象获取
let btn = document.getElementById("myBtn")
btn.addEventListener("click", function (event) {
  console.log(event)
})
//
let btn = document.getElementById("myBtn")
btn.addEventListener("click", function (e) {
  console.log(e)
})

// 兼容语法
let btn = document.getElementById("myBtn")
btn.addEventListener("click", function (e) {
  console.log(e || event)
})
```

##### 事件对象的属性

1. `event.target/srcElement` 触发事件的元素（事件源）

```javascript
document.querySelector("button").addEventListener("click", function (event) {
  console.log(event.target); // 输出: <button>元素
  console.log(event.srcElement); // 输出: <button>元素
});
  // 兼容写法:event.target || event.srcElement
  console.log(event.target.nodeName); // 输出: "BUTTON"
  console.log(event.srcElement.nodeName); // 输出: "BUTTON"
});
event.target; // 输出: <button>元素
event.target.nodeName; // 输出: "BUTTON"
```

2.  `event.type` 事件类型

```javascript
// 返回事件类型
document.querySelector("button").addEventListener("click", function (event) {
  console.log(event.type) // 输出: "click"
})
```

3.  `event.clientX/Y event.offsetX/Y event.pageX/Y` 鼠标位置信息

```javascript
// 以距离最近的元素为参照物,获取鼠标位置信息
console.log(event.offsetX)
console.log(event.offsetY)
// 以页面可视区域为参照物（不管有没有滚动条,屏幕上同一个点的坐标不会变）,获取鼠标位置信息
console.log(event.clientX)
console.log(event.clientY)
// 以文档为参照物（如果有滚动条,点的坐标会计算上被盖住的部分）
console.log(event.pageX)
console.log(event.pageY)
```

4.  `event.keyCode/charCode event.key` 键盘按键信息

```javascript
// 获取按下的键的 ASCII 码
console.log(event.keyCode)
// 获取按下的键的字符
console.log(event.key)
```

5.  `event.preventDefault()` 阻止默认行为

- 该方法的作用是阻止浏览器执行与事件关联的默认行为。
- 例如,当用户点击一个链接时,浏览器的默认行为是导航到链接指向的 URL。如果在一个点击事件的处理函数中调用了 event.preventDefault(),浏览器将不会导航到该 URL。
- 同样,对于表单提交事件,默认行为是将表单数据发送到服务器。如果调用了 event.preventDefault(),表单将不会被提交。

```javascript
document.querySelector("a").addEventListener("click", function (event) {
  event.preventDefault() // 阻止链接的默认跳转行为
  console.log("链接被点击,但不会跳转")
})
```

```javascript
// 行内阻止页面的跳转,这种方法比较简单,但是局限性比较大
// a标签的href属性设置为javascript:void(0)可以阻止页面的跳转,在没有写跳转链接时,可以用这个方法阻止页面刷新
// 同理,表单的action属性设置为javascript:void(0)可以阻止表单的提交
// 意思是执行里面的代码,void(0)代码意思是什么都不做
<a href="javascript:void(0);">百度一下</a>
```

6.  `event.stopPropagation()` 阻止事件冒泡

- stopPropagation() 是 event 对象的一个方法,用于阻止事件在 DOM 树中进一步传播。
- 当一个事件在 DOM 元素上触发时,它会从目标元素开始,沿着 DOM 树向上冒泡（bubble up）,直到到达根元素（通常是 document 或 window）。
- stopPropagation() 方法的作用就是在这个事件冒泡的过程中,阻止它继续向上传播。

```html
<div id="outer">
  <div id="inner">Click me</div>
</div>

<script>
  document.getElementById("outer").addEventListener("click", function (event) {
    console.log("Outer div clicked")
  })

  document.getElementById("inner").addEventListener("click", function (event) {
    console.log("Inner div clicked")
    event.stopPropagation() // 阻止事件冒泡
  })
  // 首先触发 #inner 元素上的点击事件,输出 "Inner div clicked"。
  // 由于在 #inner 的事件处理函数中调用了 event.stopPropagation(),事件不会继续向上冒泡到 #outer 元素。
  // 因此,#outer 元素上的点击事件不会被触发,不会输出 "Outer div clicked"。
</script>
```

7. `event.currentTarget` 返回事件监听器所附加的元素。

```javascript
document.querySelector("button").addEventListener("click", function (event) {
  console.log(event.currentTarget) // 输出: <button>元素
})
```

8. `event.shiftKey`, `event.ctrlKey`, `event.altKey`, `event.metaKey` 判断是否按下了 shift 、ctrl 、alt 、meta 键

```javascript
document.addEventListener("keydown", function (event) {
  // 判断是否按下了 shift键 按下了则返回 true,否则返回 false
  console.log(event.shiftKey)
  // 判断是否按下了 ctrl键 按下了则返回 true,否则返回 false
  console.log(event.ctrlKey)
  // 判断是否按下了 alt键 按下了则返回 true,否则返回 false
  console.log(event.altKey)
  // 判断是否按下了 meta键 按下了则返回 true,否则返回 false
  // meta 键通常是 windows 键盘上的那个windows 图标,在 mac 电脑上通常是 command 键
  console.log(event.metaKey)
  // 用来设计键盘快捷键时,可以判断是否是组合键
})
```

9. `event.timeStamp` 返回事件发生时的时间戳

```javascript
document.querySelector("button").addEventListener("click", function (event) {
  console.log(event.timeStamp)
})
// 当用户点击按钮时,控制台会输出点击事件的时间戳,例如 1633078923456。
```

#### 事件委托

事件委托是利用事件冒泡的机制,将事件监听器添加到父元素上,当子元素触发事件时,会向上传递到父元素,父元素再向上传递到祖先元素,这样就可以将事件监听器应用到多个子元素上。

## 其他知识点

### 严格模式

- 严格模式是一种特殊的运行模式,它使得 JavaScript 变得更加严格,更加符合语言的精神。
- 开启方式:在脚本的第一行加上 `"use strict";`
- 可以让书写的代码更加规范,在局部作用域和全局作用域只会对自己的作用域开启严格模式
- 变量必须声明后再使用
- 函数的参数不能有同名属性,否则会报错
- 严格模式禁用了 arguments.callee（可以代表当前函数,一般会应用在匿名函数之中）
- this 指向的严格,普通函数调用时,this 将不再指向 window,而是变成 undefined

### 作用域

- 作用域是指变量和函数的可访问范围,作用域决定了变量和函数的可见性和生命周期。
- 全局作用域:全局作用域是指在函数外部定义的变量和函数,它可以被所有函数访问。
- 局部作用域:局部作用域是指在函数内部定义的变量和函数,它只能被该函数内部的语句访问。
- 作用域链:当函数调用另一个函数时,会创建一个新的作用域,这个作用域的链式结构会把当前作用域和调用函数的作用域串起来,这样就可以访问到外部作用域的变量和函数。

#### var

var（函数作用域）:只有在函数内部用 var 声明的变量才会作为局部变量,只能在函数内访问,在函数外部声明的变量统统都是全局变量

- 函数内声明的变量会被提升到函数的最前面,在函数执行之前被创建
- var 关键字可以先使用该变量,再声明
- var 关键字可以在同一作用域多次声明同一个变量
- var 关键字声明的变量可以作为 window 对象内的键名,使用 window.a 可以访问到该变量

#### let 和 const

let 和 const（块级作用域）:在函数内部,if 分支和循环中（大括号内）用 let 声明的变量都是局部变量,只在大括号内可用（即使 let 在最外层作用域声明的变量依然是局部作用域,只不过范围比较大而已）

- let 关键字声明的变量只能在声明它所在的块级作用域内访问,不能在声明它之前访问
- let 关键字必须先声明变量再使用,不能重复声明同一个变量
- let 关键字声明的变量不能作为 window 对象内的键名,使用 window.a 不能访问到该变量
- const 关键字声明的变量的值不能被修改,只能初始化

#### 变量访问

- 获取一个变量的值（访问变量）:先在自己作用域查找,如果没有就向上一级作用域查找,以此类推,如果到全局作用域也没有找到就报错
- 为一个变量赋值:先在自己的作用域查找,如果没有就向上一级作用域查找,如果到全局作用域都没有找到就会定义该变量为全局变量并为其赋值（但是该全局变量是一个伪全局变量,即只能在下方的代码中使用,上方的代码无法访问）

### 预解析,变量提升

- 变量提升:JavaScript 引擎在对 JavaScript 代码进行解释执行之前,会对 JavaScript 代码进行预解析,在预解析阶段,会将以关键字 var 和 function 开头的语句块提前进行处理,提升到当前作用域的顶部
- 处理过程:处理过程:当变量和函数的声明处在作用域比较靠后的位置的时候,变量和函数的声明会被提升到作用域的开头。
  - 变量的声明提升只有变量名的声明提升了,赋值没有提升
  - 赋值型的函数声明,只有声明变量名被提升了,为函数赋值没有提升
  - 声明式的函数声明是整体提升,所以这种声明方式,可以先调用再声明
- 预解析的作用:预解析的作用是将变量和函数的声明提升到作用域的顶部,这样就可以在后续的代码中直接使用这些变量和函数,而不需要再次声明。
- 在代码中, 不管 if 条件是否为 true, if 语句代码里面的内容依旧会进行预解析,函数体内, return 后面的代码虽然不执行, 但是会进行预解析

### 数据储存

在 js 中,基本数据类型会储存在栈中（变量名和该变量的值都放在栈中）,复杂数据类型会储存在堆中（先在堆开辟一个储存空间,将数据放到储存空间中,然后将储存空间的地址赋值给栈里面的变量）

- 由此可得,在变量赋值时
  let a=1;
  let b=a;
  因为栈中储存的是值的缘故,将 a 的值赋给 b 后,a 和 b 的值是互不影响的,即改变 a 或 b 的值都不会影响另一个
- 但是复杂数据类型是会有影响的
  因为栈中是储存地址的缘故
  let a=[];
  let b=a;
  此操作实际上是把 a 中存的地址赋值给了 b,此时 a 和 b 操作的是同一个数组,即改变数组 a,数组 b 会跟着改变,相反同理,其他复杂数据类型同理
  函数传入实参,实参如果是一个复杂数据类型,仍然选入的是储存地址
- 基本数据类型之间的比较是数值之间的比较
  而复杂数据类型之间的比较是地址的比较,除非两个复杂数据类型共用的一个储存地址,否则不会相等,即
  - let a=[1,2];
    let b=[1.2];
    上面的 ab 因为比较的是地址的缘故,故 a 不等于 b
  - let a=[1,2]
    let b=a;
    上面的 ab 因为使用的同一个地址,故 a 等于 b

### 异步代码

事件,定时器延时器,网络请求,这三种被称为回调函数,回调函数是指先执行全部同步代码然后再回头执行的异步代码（不被回调函数包裹的程序都是同步程序）

### 拦截器

#### 拦截对象

```javascript
let obj = { a: 1, b: 2, c: 3, _c: 3 }
// 通过new proxy(代理) 创建一个中间层对象,对这个对象的访问可以访问到源对象,但是中间为我们提供了一层可被我们编程的函数层,让我们在获取值前将数据进行预处理(统一便捷)
let obj_proxy = new Proxy(obj, {
  // get函数是在取值时进行拦截的函数,这里我们可以对访问的属性值进行修改,并返回被修改后的值
  get(obj, key, proxy) {
    // get函数中存在三个参数,这三个参数是我们返回数据,拦截数据的重要依据
    // obj:原对象
    // key:要访问的属性名
    // proxy:中间层对象(通常不使用)
    // 这里的 get 函数,我们可以对访问的属性进行修改,并返回被修改后的参数
    // 下面是一个简单的示例:将对象内的数值类型数据转换为字符串类型返回
    if (typeof obj[key] === "number") {
      return obj[key] + ""
    }
    return obj[key]
  },
  // set函数是在设置值时进行拦截的函数,这里我们可以对设置的值进行修改,并将修改后的值存入源对象
  set(obj, key, value, proxy) {
    // set函数中存在四个参数,这四个参数是我们设置数据,拦截数据的重要依据
    // obj:原对象
    // key:要设置的属性名
    // value:要设置的属性值
    // proxy:中间层对象(通常不使用)
    // 这里的 set 函数,我们可以对设置的属性值进行修改,并将修改后的值存入源对象
    //倒计时补零示例
    if (value < 10 && value > 0) {
      obj[key] = "0" + value
    } else {
      obj[key] = value
    }
    // 利用分支语句可以分别对不同的属性名进行操作
  },
  // in运算拦截,for…… in 循环遍历不受影响
  has(obj, key) {
    // obj为原对象
    // key为查找的键名
    // 示例:让_开头的返回false
    if (key[0] === "_") {
      return false
    }
    return true
  },
  // delete 删除拦截,在内部执行删除操作才能被删除,否则不被删除
  deleteProperty(obj, key) {
    if (key == "a") {
      return false
    }
    delete obj[key]
    // 返回true或false,提示是否被成功删除
    return true
  },
})

console.log(obj_proxy.a) // "1"
obj_proxy.a = 2 // "02"
console.log("a" in proxy, "_a" in proxy) // true , false
```

#### 拦截函数

```javascript
// 拦截器对函数的调用进行拦截,再函数调用前做一些预处理
let foo = function (a, b) {
  return a + b
  // 构造函数
  // this.a = a;
  // this.b = b;
}
let foo_proxy = new Proxy(foo, {
  // 拦截调用行为,在函数调用前进行预处理
  apply: function (fn, ctx, arguments) {
    // fu:源函数(调用的函数)
    // ctx:函数的上下文对象(即fn函数的this指向)
    // arguments:调用函数时传入的全部参数

    // 在拦截器中调用原函数,不然不会运行原函数
    fn()
  },
  // new运算拦截
  construct(fn, args) {
    // fn是构造函数
    // args是传入的参数
    // new运算必须有返回值,并且返回值必须是对象
    // 示例:编辑一下传入的参数
    args = args.map((item) => item * 2)
    return new fu(...args)
  },
})

// 调用拦截器函数实际上执行的是拦截器函数中的代码,而不是执行原函数
foo_proxy(1, 2) // 3
let p = new foo_proxy(10, 20) // {a:20,b:40}
```

### ES6

#### let 和 const

- let:声明变量,可以重复声明,可以修改值,不能重复声明同一个变量
- const:声明常量,不能修改值,不能重复声明同一个变量
- let 和 const 声明的变量,只在声明它所在的块级作用域内有效,不能在声明它之前访问
- let 和 const 声明的变量,不能作为 window 对象内的键名,使用 window.a 不能访问到该变量

#### 解构赋值

- 解构赋值是一种方便的语法,可以方便地从数组和对象中提取值,并赋值给变量。
- 数组的解构赋值:let [a, b, c] = [1, 2, 3];
- 对象的解构赋值:let { a, b, c } = { a: 1, b: 2, c: 3 };
- 对象的解构赋值可以指定默认值:let { a = 1, b = 2, c = 3 } = { a: 1, b: 2 };
- 解构赋值可以嵌套:let { a: { b, c }, d } = { a: { b: 1, c: 2 }, d: 3 };
- 解构赋值也可以用于函数参数:function f([a, b], { c, d }) {}
- 解构赋值也可以用于函数返回值:let [a, b] = f();

```javascript
let arr = [1, 2, 3]
let [a, b, c] = arr
console.log(a, b, c) // 1 2 3
let [a = 0, b, c, d = 4, e] = arr
console.log(a, b, c, d, e) // 1 2 3 4 undefined

let obj = { a: 1, b: 2, c: 3 }
let { a, b, c } = obj
console.log(a, b, c) // 1 2 3
// 解构赋值改名
let { a: A, b: B, c: C, d: D } = obj
console.log(A, B, C, D) // 1 2 3 undefined
```

```javascript
// 对象新写法
let a = 1
let b = 2
let obj = { a, b, fn() {} }
// 相当于obj={a:a,b:b,fn:function(){}}
```

#### 箭头函数

- 箭头函数是一种新的函数语法,它使用“=>”符号来表示函数体,并且没有自己的 this,arguments,super 关键字,并且没有自己的作用域。
- 箭头函数的语法比普通函数简洁,并且没有自己的 this,arguments,super 关键字,并且没有自己的作用域,因此可以方便地嵌入其他函数中。
- 箭头函数的 this 绑定规则与普通函数不同,它会根据外层作用域的 this 绑定规则来决定 this 的值。

#### 模板字符串

#### ...运算符

- 扩展运算符（spread operator）是三个点（...）表示法,它可以将数组或对象转换为用逗号分隔的参数序列。

```javascript
let arr = [1, 2, 3]
let arr2 = [...arr, 4, 5]
console.log(arr2) // [1, 2, 3, 4, 5]
```

#### Rest 参数

- Rest 参数允许函数接受不定数量的参数,将这些参数收集到一个数组中。
- Rest 参数只能写在最后一个参数位置.
- Rest 参数将多余的参数（实参数量大于形参的情况）放入数组中,因此函数可以接受任意数量的参数。

```javascript
function sum(...args) {
  return args.reduce((a, b) => a + b, 0)
}
console.log(sum(1, 2, 3)) // 6
```

#### Set 结构

- Set 结构是一个新的内置对象,它类似于数组,但是成员的值都是唯一的,没有重复的值。
- Set.size:返回 Set 结构的成员总数。
- Set.add(value):添加某个值,返回 Set 结构本身。
- Set.delete(value):删除某个值,返回一个布尔值,表示删除是否成功。
- Set.has(value):返回一个布尔值,表示该值是否为 Set 结构的成员。
- Set.clear():清除所有成员,没有返回值。
- for...of 循环:可以遍历 Set 结构的所有成员。

```javascript
let s = new Set([1, 2, 3, 2, 1])
console.log(s) // Set(3) {1, 2, 3}
console.log(s.size) // 3

s.add(4)
console.log(s) // Set(4) {1, 2, 3, 4}

s.delete(2)
console.log(s) // Set(3) {1, 3, 4}

for (let x of s) {
  console.log(x) // 1 3 4
}

console.log(s.has(2)) // false

s.clear()
console.log(s) // Set(0) {}
```

#### Map 结构

- Map 结构是一个新的内置对象,它类似于对象,也是键值对的集合,但是成员的键可以是任意值,成员的键值对是没有重复的。
- Map.size:返回 Map 结构的成员总数。
- Map.set(key, value):设置 Map 结构的成员,返回 Map 结构本身。
- Map.get(key):读取 Map 结构的成员,返回指定键的值。
- Map.delete(key):删除 Map 结构的成员,返回一个布尔值,表示删除是否成功。
- Map.has(key):返回一个布尔值,表示某个键是否在 Map 结构中。
- Map.clear():清除所有成员,没有返回值。

```javascript
let m = new Map()
m.set("name", "张三")
m.set("age", 20)
console.log(m.get("name")) // "张三"
console.log(m.get("age")) // 20
console.log(m.size) // 2

m.delete("age")
console.log(m.has("age")) // false
```

#### for...of...遍历

```javascript
// 对象obj不能用for..of,还要用for..in遍历
for (let value of arr) {
  console.log(value)
}
// 与for... in不同的是for..in取的是索引,而for..of取得直接就是值
```

#### 模块化语法

- ES6 之前,JavaScript 没有模块化的概念,只能通过全局变量来管理命名空间,导致命名冲突、难以管理依赖关系、代码冗余等问题。
- ES6 通过模块化的语法,可以将代码分割成不同的模块,并通过模块的导入和导出,来实现代码的复用和管理。
- 模块只会执行一次,即使被多次导入。模块内部的代码在第一次导入时执行,后续导入时不会再次执行。
- 关键字:import、export

```javascript
// 导出变量函数或类
// "./module.js" 是一个模块文件
export let name = "张三"
export function sayHello() {
  console.log("hello")
}
export class Person {
  constructor(name) {
    this.name = name
  }
}
// 默认导出值,一个模块只能有一个默认导出值
let age = 20
export default age
// 导出多个变量函数或类
export { name, sayHello, Person }
// 重命名导入
import { name as myName, sayHello } from "./module.js"
console.log(myName) // "张三"

// 导入一个或多个变量函数或类
import { name, sayHello, Person } from "./module.js"
console.log(name) // "张三"
sayHello() // "hello"
// 导入默认导出值,可以自己设置名称
import age from "./module.js"
console.log(age) // 20
// 将全部导出值作为对象导入,可以自己命名对象
import * as module from "./module.js"
console.log(module.name) // "张三"
console.log(module.sayHello()) // "hello"
```

#### symbol

- symbol 是 ES6 新增的数据类型,它是一种基本类型,表示独一无二的值。
- Symbol 值通过 Symbol 函数创建,每次调用 Symbol 函数都会返回一个全新的、唯一的 Symbol 值。
- 唯一性:每个 Symbol 值都是唯一的,即使它们具有相同的描述。
- 不可变性:Symbol 值一旦创建就不能更改。
- 避免属性名冲突:Symbol 通常用于对象属性的键,以避免属性名冲突。
- 全局注册表:通过 Symbol.for(key) 可以在全局注册表中创建或获取全局唯一的 Symbol。

```javascript
//  / 创建一个 Symbol;
let sym1 = Symbol()
console.log(sym1) // Symbol()

// 创建带有描述的 Symbol,括号内部填写描述
let sym2 = Symbol("description")
console.log(sym2) // Symbol(description)

// 描述相同的 Symbol 是不同的
let sym3 = Symbol("description")
console.log(sym2 === sym3) // false

// 使用 Symbol 作为对象属性
let obj = {}
obj[Symbol("key")] = "value1"
console.log(obj) // { Symbol(key): 'value1' }
obj[Symbol("key")] = "value2"
console.log(obj) // { Symbol(key): 'value1',Symbol(key): 'value2' }

// 怎么取Symbol的值？
let keys = Object.getOwnPropertySymbols(obj)
console.log(keys) // [Symbol(key), Symbol(key)]
console.log(obj[keys[0]])
console.log(obj[keys[1]])

// Symbol 的全局注册表
// 如果注册表中没有 'globalSymbol',则创建一个新的 Symbol,否则返回已有的 Symbol。
let sym4 = Symbol.for("globalSymbol") // 全局注册表中没有 'globalSymbol',则创建一个新的 Symbol并返回
console.log(sym4)
let sym5 = Symbol.for("globalSymbol") // 全局注册表中有 'globalSymbol',则返回已有的 Symbol
console.log(sym5)
console.log(sym4 === sym5) // true
```

### 深浅拷贝

#### 浅拷贝

- 基本数据类型在进行浅拷贝时,拷贝的是值
- 复杂数据类型在进行浅拷贝时,拷贝的是储存地址,实际操作的还是同一个数据
  - 使用解构赋值解决复杂数据类型全是基本数据类型的浅拷贝

#### 深拷贝

把数据内部的所有内容全部重建一遍,包括引用类型

##### 野路子深拷贝

把对象转为 JSON 字符串,然后再重新转换为一个新的对象(注意 JSON 数据类型中储存的数据不能有引用类型)

```javascript
let obj = { a: 10, b: 20 }
let cloneObj = JSON.parse(JSON.stringify(obj))
console.log(cloneObj === obj) // false,地址已经不同了
```

##### 递归封装深克隆

```javascript
let obj = { a: 10, b: 20, c: { a: 1, b: 2 } }
function deepClone(data) {
  function isObject(data) {
    return typeof data === "object" && data !== null && !(data instanceof Array)
  }
  let clone = null
  // 判定是否为数组
  // 数据 instanceof Array
  // 数据是数组返回true,否则返回false
  // instanceof会把所有类型都判定为object
  // 判定是否为对象
  // 1.typeof 数据 返回值必须为object
  // 2.数据不是null
  // 3.数据不是数组
  if (data instanceof Array) {
    clone = []
  } else {
    clone = {}
  }
  for (let attr in data) {
    if (data[attr] instanceof Array || isObject(data[attr])) {
      clone[attr] = deepClone(data[attr])
    } else {
      clone[attr] = data[attr]
    }
  }
}
let obj1 = deepClone(obj)
console.log(obj == obj1) // false
console.log(obj) // { a: 10, b: 20, c: { a: 1, b: 2 } }
console.log(obj1) // { a: 10, b: 20, c: { a: 1, b: 2 } }
```

#### lodash 浅拷贝和深拷贝

中文官网https://www.lodashjs.com/

js 文件 cdn 地址 https://cdn.bootcdn.net/ajax/libs/lodash.js/4.17.21/lodash.min.js

\_.clone(),lodash 封装的浅拷贝函数

\_.cloneDeep(),lodash 封装的深拷贝函数

### 闭包

- 闭包的特征
  - 有一个 A 函数,A 函数内部返回了 B 函数（返回一个对象,对象内的方法调用了 A 函数内定义的变量也行）;
  - A 函数外面有变量接受 B 函数;
  - B 函数访问 A 函数内部的私有变量;
- 特点
  - 1.作用域不会被销毁
    - 优点:作用域内的变量不会被销毁,变量的生命周期增加了
    - 缺点:变量不会被销毁,内存占用增加了
  - 2.保护私有变量
    - 优点:可以把一些变量放在函数中,不污染全局命名空间
    - 缺点:必须要访问函数才能访问私有变量

```javascript
function jSQ() {
  let count = 0
  return function () {
    count++
    console.log(count)
  }
}
let jSQ1 = jSQ()
let jSQ2 = jSQ()
console.log(jSQ1)
//  () {
// count++;
// console.log(count);
// }
console.log(jSQ2)
//  () {
// count++;
// console.log(count);
// }
jSQ1() // 1
jSQ1() // 2
jSQ1() // 3
jSQ1() // 4
jSQ1() // 5
jSQ2() // 1
// jSQ1和jSQ2是两个不同的计数器
```

#### 函数科里化

```javascript
function num(a, b, c, d) {
  return a + b + c + d
}
// 根据闭包的原理,进行函数科里化
function curry(fn, args = []) {
  // 获取传入的函数的形参数量
  let num = fn.length
  console.log(num) // 4,形参数量为4
  return function (..._args) {
    // ..._args将传入的参数放到数组中
    // _args接收调用_num函数时传入的参数
    // 进行参数拼接
    // args是调用curry时传入的固定参数
    _args = args.concat(_args)
    if (_args.length == num) {
      // 当最终计算函数num的形参长度与已经确定的实参长度相等时可以进行fn函数的计算了
      // ...将数组解构
      return fn(..._args)
    } else {
      // 参数不够不进行计算,此时将固定参数（此时的固定参数包括初始传入的固定参数和上一次调用时传入的参数）和最终计算函数重新传入curry函数,生成一个新的_num函数
      return curry(fn, _args)
    }
  }
}
// 不传固定参数

let _num = curry(num)
// 此时的_num接收到的是已经返回的匿名函数
let a = _num(1, 2, 3) // 参数不够,返回一个新函数
console.log(a)
a = _num(1, 2, 3)(4) // 参数够,进行运算
console.log(a)
// 传固定参数
let _num_ = curry(num, [1, 2, 3]) // 设置固定参数为1,2,3
a = _num_(4) // 传入第四个参数4
console.log(a) // 10
```

### 面向对象编程

#### 构造函数,原型对象,实例对象

```javascript
// 构造函数
function GZfn(name, age, gender) {
  this.name = name
  this.age = age
  this.gender = gender
  // 普通函数,放置在实例对象中
  this.fn = function () {
    console.log(this.name, this.age, this.gender)
  }
}
// 原型对象 prototype
GZfn.prototype.thisFn = function () {
  console.log(this.name, this.age, this.gender)
}
// 实例对象
let GZ = new GZfn("张三", 12, "男")
console.log(GZ)
GZ.fn() // 张三 12 男
GZ.thisFn() // 张三 12 男
// 运行结果如下图1
```

- 图 1
  ![图1](./images/assets/js_了解知识点_面向对象编程_构造函数,原型对象,实例对象.jpg)

#### ES6 类

```javascript
class GZfn {
  constructor(name, age, gender) {
    this.name = name
    this.age = age
    this.gender = gender
    // 普通函数,放置在实例对象中
    this.fn = function () {
      console.log(this.name, this.age, this.gender)
    }
  }
  thisFn() {
    console.log(this.name, this.age, this.gender)
  }
  // static关键字,在方法名前加上该关键字,表示该方法是一个静态方法,只能通过类名调用该方法
  static fn2() {
    console.log(this.name)
  }
}
let GZ = new GZfn("张三", 12, "男")
console.log(GZ)
GZ.fn() // 张三 12 男
GZ.thisFn() // 张三 12 男
GZfn.fn2() // GZfn
GZ.fn2() // 报错:TypeError: GZ.fn2 is not a function/。, 吗,
// 运行结果如下图
```

- 图 1
  ![图1](./images/assets/js_了解知识点_面向对象编程_ES6类.jpg)

### 继承

- 继承是面对对象程序的高级运用
- 就是让一个构造函数去继承另外一个的构造函数的功能,方法
- 继承是出现在两个构造函数之间的
- 使用场景:有多个构造函数的公共部分可以单独封装为一个构造函数,然后让其他构造函数去继承这个公共构造函数

#### ES5 继承

##### 借用继承

借用继承就是把公共构造函数当作一个普通的函数,然后在子构造函数进行调用时,使用 apply 工具修改 this 指向
**公共构造函数的原型对象上面的方法是无法继承下来的**

```javascript
// 初始化一个公共部分的构造函数,在子函数调用时把它当作一个普通函数
function father(...args) {
  this.father_name = "我是公共构造函数哦"
  this.a1 = args
  this.father_fn = function () {
    console.log("哎哟~父亲你干嘛！")
    console.log(args)
  }
}
console.log(father)
// 运行结果如下图1
function son(...args) {
  // 使用call、apply或bind改变指向的工具来调用公共构造函数
  father.apply(this, args)
  this.son_name = "我是儿子哦"
  this.a2 = args

  this.son_fn = function () {
    console.log("哎哟~儿子你干嘛！")
    console.log(args)
  }
}
console.log(son)
// 运行结果如下图2

let a = new son(1, 2, 3)
console.log(a)
// 运行结果如下图3
```

- 图 1
  ![图1](./images/assets/js_了解知识点_继承_ES5继承_借用继承1.jpg)
- 图 2
  ![图2](./images/assets/js_了解知识点_继承_ES5继承_借用继承2.jpg)
- 图 3
  ![图3](./images/assets/js_了解知识点_继承_ES5继承_借用继承3.jpg)

##### 原型继承

让子类实例对象能够访问到,公共构造函数的原型对象,需要把子类原型对象的原型指针指向公共原型对象

```javascript
function father() {}
father.prototype.fn = function () {
  console.log("我是公共构造函数的原型对象中的方法")
}
function son() {}
// 下面是一种无传参的方法,而面向对象程序中很少有不传参的情况,所以这种方法不好用
// son.prototype = new father();
// let a = new son();
// console.log(a);
// a.fn();
// 运行结果如下图1
// 使用新工具Object.create(对象)
// 返回值是一个对象,这个对象的原型指针指向我们传入的参数对象
// 我们可以利用该工具,让子实例对象的原型指针指向公共构造函数的原型对象
son.prototype = Object.create(father.prototype)
let a = new son()
console.log(a)
a.fn()
// 运行结果如下图2
```

- 图 1
  ![图1](./images/assets/js_了解知识点_继承_ES5继承_原型继承1.jpg)
- 图 2
  ![图2](./images/assets/js_了解知识点_继承_ES5继承_原型继承2.jpg)

#### ES6 继承

```javascript
class father {
  constructor(name, gender) {
    this.name = name
    this.gender = gender
  }
  sayHello() {
    console.log("hello" + this.gender + "的" + this.name)
  }
}
// extends复制类
class son extends father {
  constructor(...args) {
    // 在构造函数中,super()调用父构造函数,this指向不用修改,括号里往父构造函数里传参数
    super(...args)
    this.age = 20
  }
  // 方法进行覆盖操作
  sayHello() {
    // 在方法中,super调用父构造函数的方法
    super.sayHello()
    console.log("hello" + this.gender + "的" + this.name + "今年" + this.age + "岁")
  }
}
let f = new father("张三", "男")
console.log(f)
f.sayHello()
let s = new son("李四", "女")
console.log(s)
s.sayHello()
// 运行结果如下图1
```

- 图 1
  ![图1](./images/assets/js_了解知识点_继承_ES6继承.jpg)

### 设计模式

#### 单例模式

**单例模式的核心思想是用闭包,给构造函数设计一个开关,第一次使用该构造函数可以返回构造出的实例对象,之后再使用该构造函数只会返回第一次构造出的实例对象**

```javascript
// 首先要有一个构造函数
class A {
  constructor(name, age) {
    this.name = name
    this.age = age
  }
  static daYin() {
    console.log(this.name, this.age)
  }
}
// 然后为构造函数设置一个开关
// 需要用匿名函数返回一个函数作为开关函数
let AA = (() => {
  // 写法2:let AA = (function () {
  // obj存已经创建的实例对象,初始为null,代表还没创建
  let obj = null
  return function (...args) {
    if (!obj) {
      // 还没创建实例对象时创建实例对象
      obj = new A(...args)
    }
    return obj
  }
})()
let a1 = AA("张三", 12)
console.log(a1) // A {name: '张三', age: 12}
let a2 = AA("张二", 10)
console.log(a2) // A {name: '张三', age: 12}
```

```javascript
class A {
  constructor(name = "six-flower", age = 21, gender = "女") {
    this.name = name
    this.age = age
    this.gender = gender
    this.obj = null
  }
  // 静态属性
  static obj = null
  // 静态方法
  static a(...args) {
    if (!A.obj) {
      A.obj = new A(...args)
    }
    console.log(args)
    return A.obj
  }
}
console.log(A.a())
console.log(A.a("张超", 21, "男"))
```

#### 发布订阅模式(消息模式)

- 事件添加模式就是一个典型的发布订阅模式（添加了事件函数,但是事件还没触发,所以不会执行,只在事件触发才通知函数:你可以去执行了！）
- 订阅发布模式的组成
  - 1.存放订阅者名单的对象
  - 2.有一个 on 方法,用于添加事件（订阅者）
  - 3.有一个 emit 方法,用来发布事件信息
  - 4.有一个 off 方法,用来移除订阅者

```javascript
// 示例
const observer = {
  message: { click: [fn, fn2], dblclcik: [fn2] },
  on: function () {}, //也可以 换成 subscibe 订阅 监听事件
  emit: function () {}, //也可以换成dispatch,派发事件,发射 触发 事件
  off: function () {}, //也可以换成unsubscibe  解除绑定-取消订阅
}
```

**可以将其写成面对对象（类）的模式**

```javascript
class Observer {
  constructor() {
    this.message = {
      // 存放订阅的信息
    }
  }

  on(type, fn) {
    // 为订阅某一信息并为其添加函数
    if (!this.message[type]) {
      // 说明消息盒子中没有订阅这个类型
      // 需要订阅该类型
      this.message[type] = [fn]
    } else {
      // 有该类型就加入数组中去
      this.message[type].push(fn)
    }
  }

  emit(type) {
    // 订阅的信息触发,执行函数
    if (!this.message[type]) {
      // 如果没有订阅该信息就不执行
      return false
    }
    // 执行添加的全部函数
    this.message[type].forEach((item) => {
      // 不改变this指向的话,item这个函数内部的this指向就是window
      item.call(this)
    })
  }

  off(type, fn) {
    // 移除某一订阅的信息的某个函数
    if (!this.message[type]) {
      // 如果没有订阅该信息就不执行
      return false
    }
    for (let i = 0; i < this.message[type].length; i++) {
      if (this.message[type][i] === fn) {
        this.message[type].splice(i, 1)
        i--
        // 把全部同名的都给删除掉
      }
    }
    // 注意:添加消息订阅函数时如果添加的是匿名函数,那是无法去除的
  }
}
```

### 事件循环

事件循环--event loop--控制 js 同步代码和异步代码的执行机制

js 代码分为同步代码和异步代码

异步代码在 js 中又分为两种

- 异步宏任务:延时器、定时器、ajax
- 异步微任务:queueMicrotask 创建一个延时 0ms 的异步微任务,promise.then() await 后面的代码

**先执行宏任务再执行微任务**
**微任务优先于宏任务**

事件循环:

- 第一次事件循环:先把全部的代码当作一个宏任务执行,然后清空微任务队列
- 后面的事件循环:执行一个宏任务,然后清空微任务列表

### promise 对象

**Promise 对象是用来解决回调函数的过度嵌套而导致的回调地狱问题的！**

- 回调函数的嵌套是不利于程序维护的,过度嵌套时,代码几乎无法维护,所以称为地狱。
- Promise 对象非常适合处理 javascript 网络请求的异步程序

#### 创建 Promise 对象

- 创建 promise 对象,创建时必须要传入一个函数,该函数被我们称为规则制定函数
- 刚创建的 promise 对象,状态为初始状态,我们可以在内部调用函数来切换状态
  - 初始状态 字符串为 pending
  - 成功状态 字符串为 fulfilled
  - 失败状态 字符串为 rejected

```javascript
// 固定的创建方式,传入一个函数,然后函数内有两个实参（函数）需要我们放两个形参来接收
let pro = new Promise(function (resolve, reject) {
  // resolve函数:解决,将状态改为成功状态
  // reject函数:未解决,将状态改为失败状态
  // 调用哪个函数就可以将状态改为哪个状态
  // 状态改变工具的使用方法:放在异步程序中改变状态
  resolve() // 单独调用后结果如下图2
  reject() // 单独调用后结果如下图3
  // 两个都调用后结果如下图4和5,因为状态只能改变一次,所以只有第一次改变状态会生效
  // 利用promise对象,我们可以不用回调函数（在异步程序结束后调用其他函数）直接改变promise的状态就行了
})
console.log(pro)
// promise对象只能进行状态的改变
```

- 图 1,未改变状态时打印 pro
  ![图1](./images/assets/js_了解知识点_Promise对象_创建promise对象1.jpg)
- 图 2,调用 resolve 函数时打印 pro
  ![图2](./images/assets/js_了解知识点_Promise对象_创建promise对象2.jpg)
- 图 3,调用 reject 函数时打印 pro
  ![图3](./images/assets/js_了解知识点_Promise对象_创建promise对象3.jpg)
- 图 4,先调用 resolve 函数然后再调用 reject 函数时打印 pro
  ![图3](./images/assets/js_了解知识点_Promise对象_创建promise对象4.jpg)
- 图 5,先调用 reject 函数然后再调用 resolve 函数时打印 pro
  ![图3](./images/assets/js_了解知识点_Promise对象_创建promise对象5.jpg)

#### 使用 Promise 对象

- 使用 Promise 需要我们查看 Promise 对象的状态,状态改变了我们就去执行别的程序
- Promise 支持的监听工具
  - then(状态变为成功后执行的函数,状态变为失败后执行的函数)
  - catch(状态改变为失败后执行的函数)
  - finally(只要状态改变了就执行的函数)
    - finally 没有数据！

```javascript
let pro = new Promise((resolve, reject) => {
  let a = [0, 1]
  setTimeout(() => {
    // 定时器模拟网络请求这个异步程序
    resolve(a)
    // 改变为成功状态,并传递一个数据
  }, 1000)
})
console.log(pro)
// then方法监听状态改变
pro.then(handlerSuccess, handlerError)
// 注意:在没有添加失败处理函数时,promise对象改变为失败状态会报错,而添加了失败处理函数不会报错
function handlerSuccess(result) {
  // 改变为成功状态时执行,可以加一个形参来接收状态改变时传递的数据
  console.log("成功了")
  console.log(result)
}
function handlerError(result) {
  // 改变为失败状态时执行,可以加一个形参来接收状态改变时传递的数据
  console.log("失败了")
  console.log(result)
}
```

- 图 1,状态监听,状态改为成功状态时执行结果
  ![图1](./images/assets/js_了解知识点_Promise对象_使用promise对象1.jpg)
- 图 2,状态监听,状态改为失败状态时执行结果
  ![图1](./images/assets/js_了解知识点_Promise对象_使用promise对象2.jpg)

#### Promise 的进阶使用(列队执行)

- then 和 catch 方法的返回值是 Promise 对象
  - 状态改变处理函数没有设置返回值或者返回值不是 Promise 对象时,会返回一个默认的 Promise 对象,返回的 Promise 对象的状态与调用方法的 Promise 对象改变状态后的状态相同
  - 状态处理函数返回一个 Promise 对象,以此实现多个异步程序的列队执行
- finally 函数主要用于 dom 操作,特效制作,不进行数据操作

```javascript
let pro = new Promise((resolve, reject) => {
  setTimeout(() => {
    resolve()
    console.log("异步程序1执行了")
  }, 1000)
})
function handlerSuccess() {
  console.log("异步程序1成功")
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      console.log("异步程序2执行了")
      resolve()
    }, 1000)
  })
}
function handlerError() {
  console.log("异步程序1失败")
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      console.log("异步程序2执行了")
      resolve()
    }, 1000)
  })
}
function handlerSuccess2() {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      console.log("异步程序3执行了")
      resolve()
    }, 1000)
  })
}
function handlerSuccess3() {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      console.log("异步程序4执行了")
      resolve()
    }, 1000)
  })
}
// pro
//   .then(handlerSuccess, handlerError)
//   .then(handlerSuccess2)
//   .then(handlerSuccess3)
//   .finally(function(){
//     console.log("执行一个特效")
//   })
// 上下两种列队方法效果一样(不管变成成功状态还是失败状态都一样)
pro
  .then(handlerSuccess)
  .catch(handlerError)
  .then(handlerSuccess2)
  .then(handlerSuccess3)
  .finally(function () {
    console.log("执行一个特效")
  })
```

- 图 1,列队执行:成功状态执行结果
  ![图1](<./images/assets/js_了解知识点_Promise对象_promise的进阶使用(列队执行)1.jpg>)
- 图 2,列队执行:失败状态执行结果
  ![图1](<./images/assets/js_了解知识点_Promise对象_promise的进阶使用(列队执行)2.jpg>)

#### Promise 的高级运用(高级工具)

**Promise 对象的构造函数上有两个静态方法,只能通过 Promise 对象的构造函数调用**

- Promise.all
  - 等待传入的 Promise 对象数组中的所有 Promise 对象的状态都变为成功时处理
  - 只要有一个转变为失败状态,就会失败
  - 返回值为数组,数组对应全部 Promise 对象转变状态后的传递的数据
- Promise.allSettled
  - 接受一个 Promise 数组，返回一个新的 Promise，该 Promise 在所有输入的 Promise 完成后解决
  - 不论它们是成功还是失败。返回的结果是一个对象数组，每个对象都有两个属性：`status`（表示是 "fulfilled" 还是 "rejected"）和 `value` 或 `reason`（取决于 Promise 的状态）
- Promise.race
  - 传入的 Promise 对象数组中的哪个 Promise 对象先转变状态(不论成功失败)就用哪个
  - 返回值最先转变状态的 Promise 对象转变状态后的传递的数据

```javascript
let pro1 = new Promise((resolve, reject) => {
  setTimeout(() => {
    console.log("pro1")
    resolve("pro1 状态已改变")
  }, 1000)
})
let pro2 = new Promise((resolve, reject) => {
  setTimeout(() => {
    console.log("pro2")
    reject("pro2 状态已改变")
  }, 5000)
})
let pro3 = new Promise((resolve, reject) => {
  setTimeout(() => {
    console.log("pro3")
    resolve("pro3 状态已改变")
  }, 3000)
})
let pro4 = new Promise((resolve, reject) => {
  setTimeout(() => {
    console.log("pro4")
    resolve("pro4 状态已改变")
  }, 2000)
})
let all_promise = Promise.all([pro1, pro2, pro3, pro4])
all_promise.then((res) => {
  console.log("all工具展示")
  console.log(res)
})
let race_promise = Promise.race([pro1, pro2, pro3, pro4])
race_promise.then((res) => {
  console.log("race工具展示")
  console.log(res)
})
// 输出结果如下
// pro1;    pro1先转变状态
// race工具展示;
// pro1 状态已改变;     race工具在pro1转变成功状态后执行
// pro4;    pro4转变状态
// pro3;    pro3转变状态
// pro2;    pro2转变状态
// all工具展示
// ['pro1 状态已改变', 'pro2 状态已改变', 'pro3 状态已改变', 'pro4 状态已改变']   all工具在全部转变成功状态后执行
```

- 图 1,全部都设置转变为成功状态时
  ![图1](./images/assets/js_了解知识点_Promise对象_promise的高级运用1.jpg)
- 图 2,第一个结束的异步程序设置为失败状态时
  ![图2](./images/assets/js_了解知识点_Promise对象_promise的高级运用2.jpg)
- 图 3,设置为失败状态的不是第一个结束时
  ![图3](./images/assets/js_了解知识点_Promise对象_promise的高级运用3.jpg)
- 图 3,只有第一个结束的是设置的成功状态时
  ![图4](./images/assets/js_了解知识点_Promise对象_promise的高级运用4.jpg)

#### async 和 await 关键字

**这套关键字可以去简化 promise 对象操作**

- async 和 await 的使用
- async:定义特殊函数的关键字,意思就是该函数可以用 await 关键字
  - 返回值是一个 Promise 对象,虽然返回值是一个对象,但是不能代替 new Promise
  - async 创建的 promise 对象不好控制成功失败状态
- await:处理 Promise 对象的关键字
  - await promise 对象,await 会进行程序的阻塞,等待 promise 对象状态发生改变并返回数据之后再执行赋值行为,再执行后方的代码
  - 实际上是把 await 后方的代码全部都当成了异步代码,只有这里执行了,才会继续向下执行

```javascript
// 用new Promise定义一个Promise对象
let pro = new Promise((resolve, reject) => {
  setTimeout(() => {
    resolve("pro 执行了")
  }, 1000)
})
// 用async定义一个Promise对象
// async定义的Promise对象只要函数执行到末尾了,promise对象的状态就会变成成功,函数的返回值要自己设置
async function fn() {
  console.log(await pro) // 这样可以获取到传递的数据
  return "这样才能设置传递的数据哦"
}
console.log(fn)
async function fo() {
  console.log(await fn()) // 这样可以获取到传递的数据
}
console.log("函数的返回值只能是一个Promise对象")
console.log(fo()) // 函数的返回值只能是一个Promise对象
```

- 图 1,上面代码执行结果
  ![图1](./images/assets/js_了解知识点_Promise对象_async和await关键字1.jpg)

##### try{}catch(){}关键字

**promise 对象的状态转变为失败时,没有失败处理函数是会报错的,而这套关键字声明的 promise 对象,在上一个 promise 传递的是错误状态时,会直接报错,如果想要设置失败状态就需要借助 try...catch 关键字**

try...catch 无法捕获语法错误

```javascript
// try...catch的使用
// 报错后代码依旧会执行
try {
  // 报错的代码
  console.log(a)
} catch (e) {
  // 形参e接收报错信息
  console.log(e)
}
console.log(1)
```

- 图 2,try...catch 用法
  ![图1](./images/assets/js_了解知识点_Promise对象_async和await关键字2.jpg)

利用 try...catch 来设置失败状态

```javascript
let pro = new Promise((resolve, reject) => {
  setTimeout(() => {
    reject("转换为失败状态")
  }, 1000)
})
async function fn() {
  try {
    console.log(await pro)
  } catch (e) {
    // 打印报错信息
    console.log(e)
    // 可以去书写错误状态下执行的函数
    console.log("错误执行的代码")
  }
}
console.log(fn())
```

- 图 3,try...catch 用法
  ![图1](./images/assets/js_了解知识点_Promise对象_async和await关键字3.jpg)

#### 红绿灯案例

```html
<style>
  div {
    width: 200px;
    height: 200px;
    border-radius: 50%;
  }
</style>
<body>
  <div></div>
  <input
    type="button"
    value="开始" />
  <script>
    let div = document.querySelector("div")
    let button = document.querySelector("input")
    function c(color, timer) {
      return new Promise((resolve, reject) => {
        div.style.backgroundColor = color
        console.log(color, timer / 1000 + "s")
        setTimeout(() => {
          resolve()
        }, timer)
      })
    }
    // 加了async关键字后可以在内部使用await关键字
    async function a() {
      // c("red", 3000)
      // .then(() => {
      //   return c("yellow", 1000);
      // })
      // .then(() => {
      //   return c("green", 3000);
      // })
      // .then(() => {
      //   return c("yellow", 1000);
      // })
      // .then(() => { a(); });
      await c("red", 3000)
      await c("yellow", 1000)
      await c("green", 3000)
      await c("yellow", 1000)
      a()
    }
    button.addEventListener("click", a)
  </script>
</body>
```

### jsDoc

通过使用 jsDoc 文档注释,可以提高代码的可读性,并使其他开发者更容易理解你的代码。使用合适的工具,如 JSDoc,可以自动生成文档,并且用 jsDoc 注释过的变量名,在编程时会有补全提示

```javascript
/**
 * 计算两个数的和
 * @param {number} a - 第一个数字
 * @param {number} b - 第二个数字
 * @returns {number} 返回两个数字的和
 */
function sum(a, b) {
  return a + b
}
```

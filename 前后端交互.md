[TOC]

# 前后端交互 知识点

## 请求发送工具(AJAX)

前端的请求发起技术

- 有刷新的请求:form 表单
  - 缺点：
    - 会让页面整体重新渲染
    - 前后端无法完整分离
- 无刷新的请求:AJAX(async javascript and XML(json))
  - 优点:
    - 不需要第三库的支持,原生 js 就可以使用
    - 减轻服务端和宽带的负担
    - 页面性能好,可以实现局部数据刷新
    - 进行交互时,配置项语义化更好
  - 缺点:
    - SEO 优化不好
      - 搜索引擎的支持度不够（seo）,因为数据都不在页面上,搜索引擎搜索不到
    - 没有回退按钮

一个完整的请求报文包括:

- 请求头
  - url 地址
  - 请求方式:get|post 等
- 请求体(get 请求没有请求体,请求参数在 url 地址后面拼接)
  - 请求参数

一个完整的响应报文包括:

- 响应头
- 响应体
  - 服务器返回的数据

发起网络请求需要配置请求信息

- 请求方式:GET | POST | PUT | PATCH | DELETE
  - get：一般用于获取数据(参数放在 url 地址中)
  - post：一般用于上传数据(参数放在请求体中)
  - put：一般用于更新全部数据（覆盖式更新）(id 参数放在 url 地址中,数据参数放在请求体中)
  - patch：一般用于更新部分数据（局部更新）(id 参数放在 url 地址中,数据参数放在请求体中)
  - delete：删除数据(参数放在 url 地址中)
- 请求目标:(以接口文档为主)
  - 服务器路径:1. 协议(http://) 2. 域名(localhost) 3. 端口(8888)
  - 业务路径:请求的某个具体的功能(要查看接口文档)

#### xhr 工具

xhr 是 XMLHttpRequest() 的简写

##### 基础使用

```javascript
document.addEventListener("click", () => {
  // 1.创建一个请求实例对象
  let xhr1 = new XMLHttpRequest()
  let xhr2 = new XMLHttpRequest()
  // 2.配置请求信息
  // open方法配置请求信息,第一个参数是请求方式get/post/put/...,第二参数shi请求的url,第三个参数是本次请求是否异步,默认值为true,表示同步
  xhr1.open("GET", "http://localhost:8888/test/first")
  xhr2.open("GET", "http://localhost:8888/test/second")
  // 3.发送请求
  // send方法发送请求
  xhr1.send()
  xhr2.send()
  // 4.拿到请求信息,开发者工具的网络可以查看请求信息,以及接收到的信息
  xhr1.onload = function () {
    // 打印一下
    console.log("我是xhr1")
    console.log(xhr1.responseText)
  }
  xhr2.onload = function () {
    // 打印一下
    console.log("我是xhr2")
    console.log(JSON.parse(xhr2.responseText))
  }
})
```

- 图 1,开发者工具查看 xhr1 请求
  ![图1](./images/assets/前后端交互_前端_请求工具_xhr工具_基础使用1.jpg)
- 图 2,开发者工具查看 xhr2 请求
  ![图2](./images/assets/前后端交互_前端_请求工具_xhr工具_基础使用2.jpg)
- 图 3,控制台打印
  ![图3](./images/assets/前后端交互_前端_请求工具_xhr工具_基础使用3.jpg)

- ajax 状态码 - `xhr.readyState`
- 是用来表示一个 ajax 请求的全部过程中的某一个状态
  - `readyState === 0`： 表示未初始化完成,也就是 `open` 方法还没有执行
  - `readyState === 1`： 表示配置信息已经完成,也就是执行完 `open` 之后
  - `readyState === 2`： 表示 `send` 方法已经执行完成
  - `readyState === 3`： 表示正在解析响应内容
  - `readyState === 4`： 表示响应内容已经解析完毕,可以在客户端使用了
- 这个时候我们就会发现,当一个 ajax 请求的全部过程中,只有当 `readyState === 4` 的时候,我们才可以正常使用服务端给我们的数据
- 所以,配合 http 状态码为 200 ~ 299
  - 一个 ajax 对象中有一个成员叫做 `xhr.status`
  - 这个成员就是记录本次请求的 http 状态码的
- 两个条件都满足的时候,才是本次请求正常完成

- 在 ajax 对象中有一个事件,叫做 `readystatechange` 事件
- 这个事件是专门用来监听 ajax 对象的 `readyState` 值改变的的行为
- 也就是说只要 `readyState` 的值发生变化了,那么就会触发该事件
- 所以我们就在这个事件中来监听 ajax 的 `readyState` 是不是到 4 了

  ```javascript
  const xhr = new XMLHttpRequest()
  xhr.open("get", "http://jx.xuzhixiang.top/ap/api/checkname.php?username=1")
  xhr.send()

  xhr.onreadystatechange = function () {
    // 每次 readyState 改变的时候都会触发该事件
    // 我们就在这里判断 readyState 的值是不是到 4
    // 并且 http 的状态码是不是 200 ~ 299
    if (xhr.readyState === 4 && /^2\d{2}$/.test(xhr.status)) {
      // 这里表示验证通过
      // 我们就可以获取服务端给我们响应的内容了
    }
  }
  ```

##### 进阶使用

###### 携带参数

**使用哪种请求方式由后端决定**

1. get 请求

- 语义是获取信息
- GET 携带数据不建议超过 4KB
- get 请求携带数据要把数据放在路径上,信息会暴露在地址栏,存在数据暴露的风险
- 用 ? 分隔路径,用 & 分隔数据
- 携带的数据的数据名由服务端 2 定义,具体名称要看接口文档

```javascript
document.addEventListener("click", () => {
  let xhr = new XMLHttpRequest()
  xhr.open("GET", "http://localhost:8888/test/third?name='six-flower'&age=20")
  xhr.send()
  xhr.onload = function () {
    console.log(JSON.parse(xhr.responseText))
  }
})
```

- 图 1,get 请求携带参数
  ![图1](./images/assets/前后端交互_前端_请求工具_xhr工具_进阶使用_携带参数1.jpg)

2. post 请求

- 语义是发送信息
- POST 携带数据体不受限制
- 因为 POST 往服务器端发送信息大小不限,所以对服务器来说 POST 有一定风险（如果上传了一个病毒的话..）
- POST 携带参数放在 send 工具里

```javascript
document.addEventListener("click", () => {
  let xhr = new XMLHttpRequest()
  xhr.open("POST", "http://localhost:8888/test/fourth")
  // POST请求需要设置请求头,请求头在接口文档内查看
  // 请求内容类型:
  // application/x-www-form-urlencoded(表单搜索串)
  // application/json(json字符串)
  xhr.setRequestHeader("Content-Type", "application/x-www-form-urlencoded")
  xhr.send("name='six-flower'&age=21")
  xhr.onload = function () {
    console.log(JSON.parse(xhr.responseText))
  }
})
// 如果要上传文件
// 1.获取文件的二进制数据
// dom操作,选择文件,用files属性获得上传的文件的伪数组,
let files = document.querySelector("input[type='fill']").files
let file = files[0] //file是一个文件对象,他是一个二进制数据
// 创建一个 FormData 对象
let fd = new FormData()
// 将二进制数据添加到 FormData 对象中
fd.append("file", file) //.append(参数名,文件对象)
// FormData 对象放在请求体中,其他操作不变(放在send中,上传文件会自动设置请求头：form-data)
// 上传成功后服务器返回的响应数据是一个网址,网址是json类型的数据,该网址是图片链接
```

- 图 2,get 请求携带参数
  ![图2](./images/assets/前后端交互_前端_请求工具_xhr工具_进阶使用_携带参数2.jpg)

3. 完整 xhr 功能的封装

```javascript
function xhr(options) {
  // 获取传入的参数
  let { url, type = "GET", data, header } = options
  // 创建一个xhr请求
  let xhr = new XMLHttpRequest()
  let data_str = ""
  for (let key in data) {
    data_str += "&" + key + "=" + data[key]
  }
  data_str = data_str.slice(1)
  if (/^GET$/i.test(type) && data) {
    url += "?" + data_str
  }
  // 配置请求方式和请求地址
  xhr.open(type, url)
  // 判断请求方式,如果是get请求就不需要将请求数据放在请求体中
  if (data && !/^GET$/i.test(type)) {
    if (header == "application/json") {
      xhr.setRequestHeader("Content-Type", header)
      // 上传json格式数据
      xhr.send(JSON.stringify(data))
    } else if (header == "application/x-www-form-urlencoded") {
      xhr.setRequestHeader("Content-Type", header)
      // 上传搜索参数
      xhr.send(data_str)
    } else {
      // 上传文件
      // 创建一个 FormData 对象
      let wJ = new FormData()
      for (let key in data) {
        // 将二进制数据添加到 FormData 对象中
        console.log(key, data[key])

        wJ.append(key, data[key]) //.append(参数名,文件对象)
      }
      xhr.send(wJ)
    }
  } else {
    xhr.send()
  }
  // 返回一个Promise对象
  return new Promise(function (resolve, reject) {
    xhr.onload = function () {
      // 默认返回JSON格式数据,转换失败传text数据
      try {
        resolve(JSON.parse(xhr.responseText))
      } catch (e) {
        resolve(xhr.responseText)
      }
    }
  })
}
```

**应用示例(服务器：todos-server)**

```html
<input
  type="text"
  placeholder="填写事项"
  value="six-flower"
  id="txt" /><input
  type="button"
  value="提交" />
<ul></ul>
<script>
  // 记得开服务器
  let txt = document.querySelector("#txt")
  let btn = document.querySelector("input[type='button']")
  let ul = document.querySelector("ul")
  function xhr(options) {
    // 获取传入的参数
    let { url, type = "GET", data, header } = options
    // 创建一个xhr请求
    let xhr = new XMLHttpRequest()
    let data_str = ""
    for (let key in data) {
      data_str += "&" + key + "=" + data[key]
    }
    data_str = data_str.slice(1)
    if (/^GET$/i.test(type) && data) {
      url += "?" + data_str
    }
    // 配置请求方式和请求地址
    xhr.open(type, url)
    // 判断请求方式,如果是get请求就不需要将请求数据放在请求体中
    if (data && !/^GET$/i.test(type)) {
      if (header == "application/json") {
        xhr.setRequestHeader("Content-Type", header)
        // 上传json格式数据
        xhr.send(JSON.stringify(data))
      } else if (header == "application/x-www-form-urlencoded") {
        xhr.setRequestHeader("Content-Type", header)
        // 上传搜索参数
        xhr.send(data_str)
      } else {
        // 上传文件
        // 创建一个 FormData 对象
        let wJ = new FormData()
        for (let key in data) {
          // 将二进制数据添加到 FormData 对象中
          console.log(key, data[key])

          wJ.append(key, data[key]) //.append(参数名,文件对象)
        }
        xhr.send(wJ)
      }
    } else {
      xhr.send()
    }
    // 返回一个Promise对象
    return new Promise(function (resolve, reject) {
      xhr.onload = function () {
        // 默认返回JSON格式数据,转换失败传text数据
        try {
          resolve(JSON.parse(xhr.responseText))
        } catch (e) {
          resolve(xhr.responseText)
        }
      }
    })
  }
  // 渲染页面
  async function render() {
    let options = {
      url: "http://localhost:3000/todos",
    }
    // xhr(options).then((e) => {
    //   let arr = [...e];
    //   ul.innerHTML = arr.map(
    //     (item) => `
    //   <li><span>${item.id}|</span><span>${item.title}</span><input type="checkbox" ${item.completed ? "checked" : ""} /><input type="button" value="删除"><input type="button" value="修改"></li>
    //   `
    //   );
    // });
    let data = await xhr(options)
    console.log(data)
    ul.innerHTML = data.map(
      (item) => `
          <li><span>${item.id}|</span><span>${item.title}</span><input type="checkbox" ${item.completed ? "checked" : ""} /><input type="button" value="删除"><input type="button" value="修改"></li>
          `
    )
  }
  render()
  // 添加事件
  btn.onclick = async function () {
    let options = {
      url: "http://localhost:3000/todos",
      type: "POST",
      header: "application/json",
      data: {
        title: txt.value,
        completed: false,
      },
    }
    // xhr(options).then((e) => {
    //   render();
    // });
    await xhr(options)
    render()
  }
  // 删除事件和修改
  async function del_xiu(e) {
    let target = e.target
    if (target.nodeName == "INPUT") {
      if (target.type == "button") {
        let children = target.parentNode.childNodes
        let id = parseInt(children[0].innerHTML)
        if (target.value == "删除") {
          let options = {
            url: `http://localhost:3000/todos/${id}`,
            type: "DELETE",
          }
          // xhr(options).then((e) => {
          //   console.log("成功删除");
          //   render();
          // });
          await xhr(options)
          console.log("成功删除")
          render()
        } else {
          let options = {
            url: `http://localhost:3000/todos/${id}`,
            type: "PATCH",
            header: "application/json",
            data: {
              title: txt.value,
            },
          }
          // xhr(options).then((e) => {
          //   console.log("成功修改");
          //   render();
          // });
          await xhr(options)
          console.log("成功修改")
          render()
        }
      }
    } else {
      // 修改完成状态的代码块,接口文档未提供修改是否完成事件的接口
    }
  }
  ul.addEventListener("click", del_xiu)
</script>
```

**应用示例(服务器：shop-server)**

```html
<!-- 多选multiple -->
<input
  type="file"
  multiple />
<input
  type="button"
  value="开始" />
<script>
  function xhr(options) {
    // 获取传入的参数
    let { url, type = "GET", data, header } = options
    // 创建一个xhr请求
    let xhr = new XMLHttpRequest()
    let data_str = ""
    for (let key in data) {
      data_str += "&" + key + "=" + data[key]
    }
    data_str = data_str.slice(1)
    if (/^GET$/i.test(type) && data) {
      url += "?" + data_str
    }
    // 配置请求方式和请求地址
    xhr.open(type, url)
    // 判断请求方式,如果是get请求就不需要将请求数据放在请求体中
    if (data && !/^GET$/i.test(type)) {
      if (header == "application/json") {
        xhr.setRequestHeader("Content-Type", header)
        // 上传json格式数据
        xhr.send(JSON.stringify(data))
      } else if (header == "application/x-www-form-urlencoded") {
        xhr.setRequestHeader("Content-Type", header)
        // 上传搜索参数
        xhr.send(data_str)
      } else {
        // 上传文件
        // 创建一个 FormData 对象
        let wJ = new FormData()
        for (let key in data) {
          // 上传文件会自动设置请求头
          // 将二进制数据添加到 FormData 对象中
          wJ.append(key, data[key]) //.append(参数名,文件对象)
        }
        xhr.send(wJ)
      }
    } else {
      xhr.send()
    }
    // 返回一个Promise对象
    return new Promise(function (resolve, reject) {
      xhr.onload = function () {
        // 默认返回JSON格式数据,转换失败传text数据
        try {
          resolve(JSON.parse(xhr.responseText))
        } catch (e) {
          resolve(xhr.responseText)
        }
      }
    })
  }
  let btn = document.querySelector("input[type='button']")
  let file = document.querySelector("input[type='file']")
  btn.onclick = function () {
    let options = {
      url: "http://localhost:8888/upload",
      type: "POST",
      header: "multipart/form-data",
      data: {
        images: file.files[0],
      },
    }
    xhr(options).then((e) => {
      console.log(e)
    })
  }
</script>
```

#### fetch 工具

```javascript
async function a() {
  // fetch(url,options)

  // GET方式直接在地址中传参

  fetch("http://localhost:8888/test/fourth?name=six-flower&age=21")
    .then((response) => {
      // 此时返回的响应数据仍未解析,需要先解析然后再使用
      // .json()返回的也是一个promise对象,调用这个工具我们可以将响应的数据转为对象类型
      // .text()返回的也是一个promise对象,调用这个工具,我们获取的响应数据是一个字符串
      // return response.text();
      return response.json()
      // 已尝试,不能使用try...catch,如果响应数据无法转为对象格式,会报错
    })
    .then((data) => {
      console.log(data)
    })

  // 下面这是POST方式

  // 在传参里面传请求头,请求方式,以及携带的数据
  // let options = {
  //   // 请求方式
  //   method: "POST",
  //   // 配置请求头
  //   headers: {
  //     "Content-Type": "application/x-www-form-urlencoded"
  //   },
  //   // 携带数据
  //   body: "name=six-flower&age=21"
  // };
  // let response = await fetch("http://localhost:8888/test/fourth", options);
  // let data = await response.json();
  // console.log(data);
}

document.addEventListener("click", a)
```

#### axios 插件

axios 插件:网络请求发起工具,泛用性强,支持前端和 node.js 应用

- 中文网站: http://www.axios-js.com/zh-cn
- 通过 node.js 下载,在写代码的文件夹打开终端(cmd)输入指令 npm install axios 下载,下载后文件路径：".\node_modules\axios\dist\axios.js"
- 线上地址引入：`<script src="https://unpkg.com/axios/dist/axios.min.js"></script>`

###### 基本应用

- 参数:(url,options)|(options)
- 返回值是 Promise 对象
  - Promise 对象成功之后返回一个对象,对象内的 data 属性是获得的响应数据

```javascript
async function a() {
  let options = {
    // get用params传参
    params: {
      name: "six-flower",
      age: 21,
    },
    // 设置请求类型,默认请求方式为GET
    method: "POST",
    // 设置请求头
    // 设置请求头必须要携带数据的
    headers: {
      "Content-Type": "application/x-www-form-urlencoded",
    },
    // 携带数据
    // data只适用于这些请求方法 'PUT', 'POST', 和 'PATCH'
    data: {
      name: "six-flower",
      age: 21,
    },
  }
  axios("http://localhost:8888/test/fourth", options).then((res) => {
    console.log(res)
    console.log(res.data)
  })
  let res = await axios("http://localhost:8888/test/fourth", options)
  console.log(res)
  console.log(res.data)
}
document.addEventListener("click", a)
```

## http 协议&&跨域

### 常见 http 协议状态码

- 200(成功)OK:请求已成功，请求的资源已经通过响应头传输。
- 301(重定向)Moved Permanently:请求的资源已被永久移动到新的 URL。
- 304(使用缓存)Not Modified:自上次请求后，请求的资源未修改过。
- 401(需要登录)unauthorized:请求需要用户验证。
- 403(没有权限)Forbidden:服务器拒绝请求，客户端没有权限访问所请求的资源。
- 404(地址找不到)Not Found:服务器无法找到请求的资源。
- 500(服务器内部错误) Internal server Error:服务器遇到了意料之外的情况，导致它无法完成请求。

### TCP 的三次握手和四次挥手

#### 三次握手

1. 过程如下：

- 第一次握手：客户端给服务端发一个 SYN 报文,并指明客户端的初始化序列号 ISN(c),此时客户端处于 SYN_SENT 状态
- 第二次握手：服务器收到客户端的 SYN 报文之后,会以自己的 SYN 报文作为应答,为了确认客户端的 SYN,将客户端的 ISN+1 作为 ACK 的值,此时服务器处于 SYN_RCVD 的状态
- 第三次握手：客户端收到 SYN 报文之后,会发送一个 ACK 报文,值为服务器的 ISN+1。此时客户端处于 ESTABLISHED 状态。服务器收到 ACK 报文之后,也处于 ESTABLISHED 状态,此时,双方已建立起了连接

2. 作用如下：

- 第一次握手：客户端发送网络包,服务端收到了 这样服务端就能得出结论：客户端的发送能力、服务端的接收能力是正常的。
- 第二次握手：服务端发包,客户端收到了 这样客户端就能得出结论：服务端的接收、发送能力,客户端的接收、发送能力是正常的。不过此时服务器并不能确认客户端- 的接收能力是否正常
  第三次握手：客户端发包,服务端收到了。 这样服务端就能得出结论：客户端的接收、发送能力正常,服务器自己的发送、接收能力也正常

#### 四次挥手

1. 过程如下：

- 第一次挥手：客户端发送一个 FIN 报文,报文中会指定一个序列号。此时客户端处于 FIN_WAIT1 状态,停止发送数据,等待服务端的确认
- 第二次挥手：服务端收到 FIN 之后,会发送 ACK 报文,且把客户端的序列号值 +1 作为 ACK 报文的序列号值,表明已经收到客户端的报文了,此时服务端处于 CLOSE_WAIT 状态
- 第三次挥手：如果服务端也想断开连接了,和客户端的第一次挥手一样,发给 FIN 报文,且指定一个序列号。此时服务端处于 LAST_ACK 的状态
- 第四次挥手：客户端收到 FIN 之后,一样发送一个 ACK 报文作为应答,且把服务端的序列号值 +1 作为自己 ACK 报文的序列号值,此时客户端处于 TIME_WAIT 状态。需要过一阵子以确保服务端收到自己的 ACK 报文之后才会进入 CLOSED 状态,服务端收到 ACK 报文之后,就处于关闭连接了,处于 CLOSED 状态

2. 原因

服务端在收到客户端断开连接 Fin 报文后,并不会立即关闭连接,而是先发送一个 ACK 包先告诉客户端收到关闭连接的请求,只有当服务器的所有报文发送完毕之后,才发送 FIN 报文断开连接,因此需要四次挥手

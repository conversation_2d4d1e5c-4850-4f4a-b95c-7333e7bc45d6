# **目录**

[TOC]

# Vue 项目创建 配置

```bash
# 创建项目
pnpm create vue
# 进入项目目录, 安装依赖
pnpm i
# 引入第三方库
# 网络请求库
pnpm i axios
# 日期处理库
pnpm i dayjs
# pinia本地化储存库
pnpm i pinia-plugin-persistedstate
# 进度条库
pnpm i nprogress @types/nprogress
# 按需自动引入第三方库
pnpm i -D unplugin-vue-components unplugin-auto-import
# sass预处理器
pnpm i sass -D
# lodash
pnpm i lodash
# 全局样式重置
pnpm i --save normalize.css
# 移动端适配
pnpm i postcss-px-to-viewport -D
```

[Install Tailwind CSS with Vite - TailwindCSS 中文文档 | TailwindCSS 中文网](https://www.tailwindcss.cn/docs/guides/vite#vue)

## 配置文件

### [axios](http://www.axios-js.com/zh-cn/docs/)

```js
// src/utils/request.js
import axios from 'axios'

const request = axios.create({
  baseURL: '',
  timeout: 10000,
  headers: {},
})

// 添加请求拦截器
request.interceptors.request.use(
  function (config) {
    // 在发送请求之前做些什么
    return config
  },
  function (error) {
    // 对请求错误做些什么
    return Promise.reject(error)
  }
)

// 添加响应拦截器
request.interceptors.response.use(
  function (response) {
    // 对响应数据做点什么
    console.log(response)

    return response
  },
  function (error) {
    // 对响应错误做点什么
    return Promise.reject(error)
  }
)

export default request
```

### pinia 与 pinia-plugin-persistedstate

```js
// main.js
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
// 使用
……
app.use(createPinia().use(piniaPluginPersistedstate))

// src/store/useStore.js
import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useStore = defineStore(
  'main',
  () => {
    const someState = ref('你好 pinia')
    return { someState }
  },
  {
    persist: true,
  }
)
```

### [nprogress](https://www.npmjs.com/package/nprogress)

```js
// src/router/index.js
// 使用前引入
import NProgress from "nprogress"
import "nprogress/nprogress.css"
// 关闭右上角转圈
NProgress.configure({ showSpinner: false })
// 前置守卫或请求前中添加一句
router.beforeEach(to => {
  NProgress.start()
  ....
})
// 后置守卫或请求后中添加一句
router.afterEach((to, from) => {
  NProgress.done()
  // 设置标题
  document.title = to.meta.title
})
```

```css
/* nprogress 样式 全局样式中更改加载进度条的颜色 */
#nprogress .bar,
#nprogress .peg {
  background-color: #c82519 !important;
}
```

### unplugin-vue-components unplugin-auto-import

```js
import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    AutoImport({
      // 自动导入 Vue 相关方法
      imports: ['vue'],
    }),
    Components({
      // dirs 指定组件所在位置，默认为 src/components
      // 可以让我们使用自己定义组件的时候免去 import 的麻烦
      dirs: ['src/components/', 'src/pages/lauch/components/'],
      // 配置需要将哪些后缀类型的文件进行自动按需引入，'vue'为默认值
      extensions: ['vue'],
      // ui组件库解析
      resolvers: [],
      // dts：true 或者为 'src/components.d.ts'时，则会自动生成components.d.ts
      dts: true,
      // 遍历子目录
      deep: true,
    }),
  ],
  resolve: {
    // 配置路径别名
    // 例如 @/ 指向 src/
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url)),
    },
  },
  server: {
    // 指定服务器的主机地址
    host: '0.0.0.0',
    // 指定开发服务器的端口
    port: 1314,
    // 设置为 true 可以在服务器启动后自动打开浏览器，或指定具体的 URL
    // 或者 open: 'http://localhost:1314'
    open: true,
    // 启用 HTTPS 连接，可以设置为对象以使用自定义的证书和密钥
    https: false,
    // 用于设置代理，可以将请求转发到其他服务器。
    proxy: {
      '/api': {
        target: 'http://backend.server.com',
        changeOrigin: true,
      },
    },
  },
})
```

### normalize

```js
// main.js 引入
import 'normalize.css'
```

### postcss-px-to-viewport

```cjs
// 新建 postcss.config.cjs 写入代码
// 禁用eslint规则
/* eslint-disable no-undef */
module.exports = {
  plugins: {
    'postcss-px-to-viewport': {
      // 设计稿的宽度
      viewportWidth: 375,
      // 允许转换的单位
      unitToConvert: 'px',
      // 需要转换的属性
      propList: ['*'],
      // 最小的转换数值
      minPixelValue: 1,
    },
  },
}
```

### 禁止页面缩放

```html
<meta
  name="viewport"
  content="width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=0" />
```

### vue 文件声明

```js
// env.d.ts
declare module "*.vue" {
  import { defineComponent } from "vue"
  const component: ReturnType<typeof defineComponent>
  export default component
}
```

## 基本样式

```css
/* 基础样式 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* a标签样式重置 */
a {
  text-decoration: none;
  color: inherit;
}

/* 列表元素重置 */
li {
  list-style: none;
}

/* 图片元素重置 */
img {
  width: 100%;
  display: block;
}

svg {
  display: block;
}

/* 表单元素重置 */
input,
textarea,
select,
button {
  margin: 0;
  padding: 0;
  border: none;
  outline: none;
  background: none;
  font: inherit;
}

/* 表格元素重置 */
table {
  border-collapse: collapse;
  border-spacing: 0;
}

/* 去除默认的字体样式 */
body,
h1,
h2,
h3,
h4,
h5,
h6,
p,
ul,
ol,
dl,
dd,
dt {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
}

/* 按钮与禁用状态按钮的样式重置 */
button {
  cursor: pointer;
}

button:disabled {
  cursor: default;
}

/* 重置表单元素的默认边框和填充 */
fieldset,
legend {
  border: 0;
  padding: 0;
  margin: 0;
}

/* 去除默认的边框和填充 */
textarea {
  resize: none; /* 禁止用户调整大小 */
}

/* 确保视频和iframe元素的默认边框被去除 */
video,
iframe {
  border: 0;
}

/* 清除浮动 */
.clearfix::after {
  content: '';
  display: table;
  clear: both;
}

/* 确保一些常用元素在不同浏览器中的一致性 */
html,
body,
#app,
.Page {
  height: 100%;
  /* 防止用户选中文本 */
  user-select: none;
}
```

## router-view 组件缓存

```vue
<template>
  <router-view v-slot="{ Component }">
    <keep-alive>
      <component
        :is="Component"
        :key="$route.fullPath"></component>
    </keep-alive>
  </router-view>
</template>
```

## vue 样式穿透

```vue
<style scoped>
:deep(.van-card__title) {
  color: red;
}
</style>
```

## vue 路由 404 重定向

```js
// src/router/index.js
import { createRouter, createWebHistory } from 'vue-router'
import LayoutView from '../views/LayoutView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'layout',
      component: LayoutView,
    },
    {
      path: '/:pathMatch(.*)',
      // 找不到页面回到首页
      redirect: '/',
    },
  ],
})

export default router
```

# Nest 服务端项目配置

## 前置

- 安装 Node.js
- 安装 Nest CLI
- 安装依赖包

```bash
npm i -g @nestjs/cli
pnpm i
```

[热重载配置](https://nest.nodejs.cn/recipes/hot-reload)

# Nuxt 项目配置

# 库

## [格式化工具 prettier](https://www.prettier.cn/docs/index.html)

```json
// .prettierrc.json
{
  "singleQuote": true, // 使用单引号而不是双引号
  "trailingComma": "es5", // 在对象和数组的末尾添加逗号，遵循ES5规范
  "endOfLine": "crlf", // 使用回车换行符（CRLF）作为行尾符，适用于Windows系统
  "semi": true, // 在语句末尾添加分号
  "tabWidth": 2, // 指定缩进的空格数为2
  "printWidth": 80, // 指定每行代码的最大长度为80个字符
  "useTabs": false, // 不使用制表符，而是使用空格进行缩进
  "arrowParens": "always", // 箭头函数参数周围始终使用括号
  "bracketSpacing": true, // 在对象字面量的括号之间添加空格
  "jsxBracketSameLine": false, // 在JSX元素的结束标签前不换行
  "jsxSingleQuote": false, // 在JSX中使用双引号而不是单引号
  "proseWrap": "preserve", // 不要更改段落中的换行符
  "quoteProps": "as-needed", // 仅在必要时为对象属性添加引号
  "vueIndentScriptAndStyle": false // 不要缩进Vue文件中的<script>和<style>标签内容
}
```

## [element-plus 和 element icons: 基于 Vue 的 UI 组件库](https://element-plus.org/zh-CN/component/overview.html)

### 安装

```
pnpm i element-plus @element-plus/icons-vue
```

### 按需引入自动引入

首先安装插件

```
pnpm i -D unplugin-vue-components unplugin-auto-import unplugin-icons
```

然后在你的 vite.config.ts 文件中添加以下内容：

```js
// 插入下面三段代码
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers'
import Icons from 'unplugin-icons/vite'
import IconsResolver from 'unplugin-icons/resolver'

export default defineConfig({
  // ...
  plugins: [
    // ...
    AutoImport({
      resolvers: [
        ElementPlusResolver(),
        IconsResolver({
          prefix: 'Icon',
        }),
      ],
    }),
    Components({
      resolvers: [
        ElementPlusResolver(),
        IconsResolver({
          enabledCollections: ['ep'],
        }),
      ],
    }),
    Icons({
      autoInstall: true,
    }),
  ],
})
```

## [vant: 基于 Vue 的移动端组件库](https://arco.design/)

### 安装

```
pnpm i vant
```

### 按需引入自动引入

```
pnpm i @vant/auto-import-resolver unplugin-vue-components unplugin-auto-import -D
```

然后在你的 vite.config.ts 文件中添加以下内容：

```js
// 插入下面三段代码
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import { VantResolver } from '@vant/auto-import-resolver'

export default {
  plugins: [
    vue(),
    AutoImport({
      resolvers: [VantResolver()],
    }),
    Components({
      resolvers: [VantResolver()],
    }),
  ],
}
```

### vant 主题样式 全局修改

```css
/* vant 主题样式 全局修改 */
:root:root {
  /* css样式变量 修改全局变量的值 */
  --van-tabbar-background: red;
  --van-tabbar-item-active-background: green;
}
```

## [arco design 基于 Vue 的企业级 UI 组件库](https://arco.design/)

### 安装

```
pnpm install --save-dev @arco-design/web-vue
```

### 按需引入自动引入

```
pnpm i -D @arco-plugins/vite-vue
```

```js
import { vitePluginForArco } from '@arco-plugins/vite-vue'

export default defineConfig({
  plugins: [
    vue(),
    vitePluginForArco({
      style: 'css',
    }),
  ],
})
```

## [jose JWT 加密库](https://github.com/panva/jose)

### 安装

```sh
pnpm i jose
```

### 引入

```ts
import * as jose from 'jose'
```

### 简单使用

```ts
import * as jose from 'jose'

// 定义一个密钥
const secret = new TextEncoder().encode(
  '652ebc195a3b2157f281606eaa737fdd0b8239b919c1cf0a58a33f64aaa0d150'
)

// 生成 JWT
export async function encryption(payload: jose.JWTPayload) {
  const token = await new jose.SignJWT(payload)
    .setProtectedHeader({ alg: 'HS256' }) // 设置算法
    .setIssuedAt() // 设置签发时间
    // .setExpirationTime('24h') // 设置到期时间为24小时
    .sign(secret) // 签名
  return token
}

// 验证 JWT
export async function verification(token: string) {
  try {
    const { payload } = await jose.jwtVerify(token, secret)
    return payload // 返回解码后的有效负载
  } catch (error) {
    console.log('验证失败:', error)
    throw new Error('无效的JWT') // 如果验证失败，抛出异常
  }
}
```

### 密钥随机生成

```js
const crypto = require('crypto')
const secret = crypto.randomBytes(32).toString('hex')
console.log('生成的密钥:', secret)
```

## [dayjs 日期处理库](https://dayjs.fenxianglu.cn/)

### 安装

```
pnpm i dayjs
```

### 使用

```js
import dayjs from 'dayjs'

// 输出当前日期和时间，格式为年-月-日 时:分:秒
dayjs().format('YYYY-MM-DD HH:mm:ss')

// 格式化指定时间
dayjs('2022-01-01').format('YYYY-MM-DD HH:mm:ss')

console.log(dayjs().add(1, 'day').format('YYYY-MM-DD')) // 输出明天的日期
console.log(dayjs().subtract(1, 'hour').format('YYYY-MM-DD HH:mm:ss')) // 输出一小时前的日期和时间

// 日期比较
const date1 = dayjs('2023-04-01')
const date2 = dayjs('2023-04-02')
console.log(date1.isBefore(date2)) // true
console.log(date1.isAfter(date2)) // false
```

## [plyr 视频播放器](https://plyr.io/)

## [dotenv 环境变量管理库](https://github.com/motdotla/dotenv)

## [express-rate-limit 访问频率限制库](https://github.com/nfriedly/express-rate-limit)

## [helmet 安全防护库](https://github.com/helmetjs/helmet)

## [node-cron 定时任务库](https://github.com/node-cron)

## [sharp 图片处理库] (https://github.com/lovell/sharp)

## [@videojs-player/vue 视频播放器](https://github.com/surmon-china/videojs-player)

### 安装

```bash
pnpm install video.js @videojs-player/vue
```

### 全局安装

```ts
import { createApp } from 'vue'
import VueVideoPlayer from '@videojs-player/vue'
import 'video.js/dist/video-js.css'

const app = createApp()

app.use(VueVideoPlayer)
```

### 使用

```vue
<template>
  <div class="h-full">
    <div>
      <video-player
        class="video-player vjs-custom-skin"
        ref="videoPlayer"
        :playsinline="true"
        :options="playerOptions" />
    </div>
  </div>
</template>

<script setup lang="ts">
const playerOptions = {
  id: 'video-player', // 视频播放器的 id
  src: 'https://www.w3schools.com/html/mov_bbb.mp4', // 视频地址
  sources: {
    type: 'video/mp4', // 视频类型
    src: 'https://www.w3schools.com/html/mov_bbb.mp4', // 视频地址
  }, // 视频源
  width: '100%', // 视频宽度
  height: '100%', // 视频高度
  preload: 'auto', // 预加载
  loop: true, // 循环播放
  muted: true, // 静音播放
  poster: 'https://www.w3schools.com/html/img_avatar.png', // 视频封面
  controls: true, // 显示控制条
  autoplay: true, // 自动播放
  playsinline: true, // 全屏播放
  crossorigin: 'anonymous', // 跨域设置
  volume: 0.8, // 音量
  playbackRate: 1, // 播放速率
  playbackRates: [0.5, 1, 1.5, 2], // 播放速率
  fluid: true, // 自适应大小
  fill: true, // 填充父元素
  language: 'zh-CN', // 语言
  languages: {
    'zh-CN': {
      'Play': '播放',
      'Pause': '暂停',
      'Mute': '静音',
      'Unmute': '取消静音',
      'Fullscreen': '全屏',
      "Picture-in-Picture": "画中画",
      'Seek': '跳转',
      'Progress': '进度',
      'Duration': '时长',
      'Volume': '音量',
      'Subtitles': '字幕',
      'Speed': '速度',
      'Playback Rate': '播放速度',
      'Subtitles List': '字幕列表',
      'Download': '下载',
      'Use Arrow Keys': '使用方向键',
      'Use PgUp and PgDn': '使用PgUp和PgDn',
      'Use Home and End': '使用Home和End',
      'Use Left and Right': '使用左右方向键',
      'Use Up and Down': '使用上下方向键',
      'Use Enter to Play': '按回车键播放',
      'Use Esc to Exit Fullscreen': '按Esc键退出全屏',
      "A network error caused the media download to fail part-way.": "网络错误导致媒体下载失败",
    },
  },
  tracks: [
    {
      kind: 'subtitles',
      src: 'https://www.w3schools.com/html/mov_bbb.en.vtt',
      srclang: 'en',
      label: 'English',
      default: true,
    },
  ], // 字幕
  textTrackSettings: {
    mode: 'hidden', // 字幕模式
    label: 'English', // 字幕名称
    language: 'en', // 字幕语言
  }, // 字幕设置
  responsive: true, // 响应式
  breakpoints: {
    640: {
      width: '100%',
      height: 200,
    },
    768: {
      width: '100%',
      height: 300,
    },
    1024: {
      width: '100%',
      height: 400,
    },
  }, // 响应式断点
  fullscreen: {
    enabled: true, // 全屏按钮
    fallback: true, // 全屏失败时是否使用内置的全屏
  }, // 全屏设置
  aspectRatio: '16:9', // 视频比例
  liveui: true, // 直播 UI
  liveTracker: true, // 直播跟踪器
  disablePictureInPicture: true, // 禁用画中画
  notSupportedMessage: '你的浏览器不支持视频播放', // 不支持视频播放时显示的提示信息
  normalizeAutoplay: true, // 自动播放时是否使用 normalize 样式
  audioPosterMode: 'hidden', // 音频封面模式
  audioOnlyMode: false, // 仅播放音频
  noUITitleAttributes: true, // 隐藏 UI 标题属性
  preferFullWindow: true, // 全屏播放时是否使用全屏样式
  suppressNotSupportedError: true, // 不支持视频播放时是否隐藏错误提示
  techCanOverridePoster: true, // 视频播放器是否可以覆盖视频封面
  techOrder: ['html5', 'flash', 'other'], // 视频播放器优先级
  inactivityTimeout: 0, // 空闲超时时间
  userActions: {
    play: true, // 显示播放按钮
    pause: true, // 显示暂停按钮
    seek: true, // 显示跳转按钮
    volume: true, // 显示音量按钮
    mute: true, // 显示静音按钮
    fullscreen: true, // 显示全屏按钮
  }, // 用户操作
  restoreEl: true, // 自动恢复播放器
  vtt.js: {
    enabled: true, // 是否使用 VTT.js 解析字幕
  }, // VTT.js 设置
  children: [
    {
      name: 'control-bar',
      type: 'control-bar',
      position: 'bottom', // 控制条位置
    }], // 自定义组件
    html5: {
      vtt.js: {
        enabled: true, // 是否使用 VTT.js 解析字幕
      }, // VTT.js 设置
    }, // HTML5 设置
    plugins: {
      videoJsResolutionSwitcher: {
        default: 'low', // 默认分辨率
        dynamicLabel: true, // 动态标签
        labels: {
          low: '低',
          medium: '中',
          high: '高',
        }, // 标签
      }, // 视频分辨率切换器
      videoJsQualitySelector: {
        default: 'high', // 默认画质
        dynamicLabel: true, // 动态标签
        labels: {
          low: '低',
          medium: '中',
          high: '高',
        }, // 标签
      }, // 视频画质选择器
    }, // 插件
    options: {}, // Video.js 原生配置
}
</script>

<style></style>
```

## [Inspira UI](https://inspira-ui.com/components)

## [lodashjs](https://www.lodashjs.com/)

## [echarts 图表库](https://echarts.apache.org/zh/index.html)

## [tailwindcss](https://www.tailwindcss.cn/)

## [DataV](http://datav.jiaminghi.com/guide/charts.html)

## [swiper 轮播图库](https://www.swiper.com.cn/index.html)

## [vue-use](https://vueuse.nodejs.cn/core/useUserMedia/)

## [js-xss 防止 XSS 攻击](https://github.com/leizongmin/js-xss/tree/master?tab=readme-ov-file)

## [copy-text-to-clipboard 复制文本到粘贴板](https://www.npmjs.com/package/copy-text-to-clipboard)

## [zxcvbn 密码强度检测](https://github.com/zxcvbn-ts/zxcvbn)

## [lottie 动画库](https://lottiefiles.com/)

## [Vben Admin 后台管理系统模板](https://doc.vben.pro/guide/introduction/quick-start.html)

## [qrcode 二维码生成](https://doc.vben.pro/guide/introduction/quick-start.html)

## [ant-design 基于 React 的 UI 组件库](https://ant-design.antgroup.com/components/overview-cn)

## [Magic UI UI 组件库](https://magicui.design/docs/components/marquee)

## [semi 设计系统](https://semi.design/zh-CN/start/introduction)

## [Mock.js 用于生成模拟数据](http://mockjs.com/)

## [ReactBits](https://www.reactbits.dev/)

## [CSS Loaders 加载动画](https://css-loaders.com/polygons/)

## [JSON Server 快速搭建 RESTful API 服务器](https://rtool.cn/jsonserver/docs/plural-routes)

## [iconfont 图标库](https://www.iconfont.cn/?spm=a313x.manage_type_myprojects.i3.2.71d73a816IINyN)

## [HTML to JSX 代码转换工具](https://transform.tools/html-to-jsx)

## [Next.js 服务器端渲染框架(React)](https://www.nextjs.cn/docs/getting-started)

## [Nuxt.js 服务器端渲染框架(Vue)](https://nuxt.com/docs/getting-started/introduction)

## [HTTP 状态码](https://developer.mozilla.org/zh-CN/docs/Web/HTTP/Reference/Status)

## [Electron 跨平台桌面应用框架](https://www.electronjs.org/zh/docs/latest/)

## [Electron-Vite 集成 Electron 和 Vite](https://cn.electron-vite.org/guide/introduction)

## [Motion Vue 基于运动的 Vue 动画库](https://motion.unovue.com/)

## [Socket.io 实时通信库](https://socket.io/zh-CN/)

## [Express 基于 Node.js 的 Web 应用框架](https://nodejs.cn/express/starter/)

## [cors 处理跨域请求](https://www.npmjs.com/package/cors)

## [nodemailer 发送邮件](https://www.npmjs.com/package/nodemailer)

#### 测试时使用

```js
const nodemailer = require('nodemailer')

/**
 * 发送邮件
 * @param {string} to 收件人邮箱
 * @param {string} subject 邮件主题 (标题)
 * @param {string} html 邮件内容 (html 格式)
 */
const sendEmail = async (to, subject, html) => {
  // 生成测试账户
  const testAccount = await nodemailer.createTestAccount()

  // 创建传输器（使用 Ethereal 的 SMTP 配置）
  const transporter = nodemailer.createTransport({
    host: 'smtp.ethereal.email',
    port: 587,
    secure: false, // 必须为 false，使用 STARTTLS
    auth: {
      user: testAccount.user,
      pass: testAccount.pass,
    },
  })

  // 发送邮件
  const info = await transporter.sendMail({
    from: '"测试发件人" <<EMAIL>>',
    to,
    subject,
    html,
  })

  // 获取邮件在线查看链接
  console.log('邮件预览链接:', nodemailer.getTestMessageUrl(info))
  return '邮件预览链接:' + nodemailer.getTestMessageUrl(info)
}

// sendEmail('<EMAIL>', '测试邮件内容')

module.exports = sendEmail
```

#### 上线后用

```js
const nodemailer = require('nodemailer')

// 1. 创建传输器（使用 163 SMTP）

const sendEmail = nodemailer.createTransport({
  host: 'smtp.163.com', // 网易邮箱 SMTP 服务器地址
  port: 465, // SSL 加密端口
  secure: true, // 必须为 true，表示启用 SSL
  auth: {
    user: '<EMAIL>', // 你的网易邮箱地址
    pass: 'VZmPk5uDyzzzKwr2', // 替换为实际授权码（非邮箱密码！）
  },
})

// 2. 定义邮件内容
const mailOptions = {
  from: '"发件人名称" <<EMAIL>>', // 发件人格式（可自定义名称）
  to: '<EMAIL>', // 收件人邮箱
  subject: '账号注册验证码',
  html:
    '<p>您好，欢迎注册我们的网站！</p>' +
    '<p>您的验证码是：<strong>123456</strong></p>' +
    '<p>为了确保您的账号安全，请在注册页面输入此验证码。</p>' +
    '<p>如果这不是您本人的操作，请忽略此邮件。</p>',
}

// 3. 发送邮件
sendEmail.sendMail(mailOptions, (error, info) => {
  if (error) {
    console.error('发送失败:', error)
  } else {
    console.log('邮件已发送:', info.response)
  }
})
module.exports = sendEmail
```

## [nodemon 监控文件变化并重启服务](https://www.npmjs.com/package/nodemon)

## [Mongoose 基于 MongoDB 的 Node.js ORM 库](https://mongoose.nodejs.cn/docs/index.html)

## [js-yaml 解析 YAML 文件](https://www.npmjs.com/package/js-yaml)

## [formidable 处理文件上传](https://www.npmjs.com/package/formidable)

## [body-parser 处理请求体](https://www.npmjs.com/package/body-parser)

## [express-validator 处理请求参数验证](https://www.npmjs.com/package/express-validator)

## [coookie-parser 处理 cookie](https://www.npmjs.com/package/cookie-parser)

## [fluent-ffmpeg 处理视频和音频](https://www.npmjs.com/package/fluent-ffmpeg)

## [number-precision 处理浮点数精度](https://www.npmjs.com/package/number-precision)

### 介绍

仅 1KB 大小，几乎无性能损耗，适合对体积敏感的项目（如移动端网页），无依赖，开箱即用，专注于修复基础运算（加减乘除）的精度问题，不包含复杂数学功能。

提供简洁的 API：plus（加）、minus（减）、times（乘）、divide（除）、round（四舍五入）。

# 网站

## [渐变色分享网站](https://pxlab.cn/color/index.html)

## [渐变色在线搭配网站](http://www.sooui.com/topic/jianbian.html)
